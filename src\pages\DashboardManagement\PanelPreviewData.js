import React, { useEffect, useMemo, useState } from "react";
import { CircularProgress } from "@mui/material";
import { useQuery } from "react-query";
import { getAll, previewPanel } from "../../services/panels-api";
import Pagination from "../../components/Pagination/Pagination";
import PanelVisualization from "./PanelVisualization";
import dayjs from "dayjs";
import { convertToUTC } from "../../common/commonFunctions";
import customParseFormat from "dayjs/plugin/customParseFormat";
import {
  BarChartIcon,
  LineChartIcon,
  MultiAxisChartIcon,
  PieChartIcon,
  TableChartIcon,
} from "../../icons";

function PanelPreviewData({
  onDragStart,
  setTotalCount,
  loadingData,
  previewLoading,
}) {
  const colors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];
  const [currentPage, setCurrentPage] = useState(1);
  const [previewResponses, setPreviewResponses] = useState({});
  const [isLoadingPreviews, setIsLoadingPreviews] = useState(false);

  dayjs.extend(customParseFormat);

  const pageSize = 4;

  const {
    data: panelList,
    isLoading,
    isFetching,
  } = useQuery(["panelList", currentPage, pageSize], getAll, {
    onSuccess: (resp) => {
      //previewAPICall(resp.data.data);
    },
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (panelList?.data?.totalCount !== undefined) {
      setTotalCount((oldState) => ({
        ...oldState,
        panel: panelList?.data?.totalCount,
      }));
    }
  }, [panelList]);

  // const previewAPICall = (payloadData) => {
  //   payloadData.map((payload) => {
  //     //console.log("payload", payload.timePeriod);
  //     const selectedRange = payload.timePeriod;
  //     let formattedStart = "";
  //     let formattedEnd = "";
  //     const currentDateTime = dayjs();

  //     if (selectedRange) {
  //       if (selectedRange.includes(" to ")) {
  //         const [startString, endString] = selectedRange.split(" to ");
  //         formattedStart = startString;
  //         formattedEnd = endString;
  //       } else {
  //         if (
  //           selectedRange === "Last Hour" ||
  //           selectedRange === "Last 6 Hours" ||
  //           selectedRange === "Last 12 Hours" ||
  //           selectedRange === "Last 24 Hours"
  //         ) {
  //           const hours = {
  //             "Last Hour": 1,
  //             "Last 6 Hours": 6,
  //             "Last 12 Hours": 12,
  //             "Last 24 Hours": 24,
  //           };

  //           const lastXHours = currentDateTime.subtract(
  //             hours[selectedRange],
  //             "hour"
  //           );

  //           formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
  //           formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
  //         } else if (selectedRange === "Today") {
  //           formattedStart = currentDateTime
  //             .startOf("day")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //           formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
  //         } else if (selectedRange === "Last Seven Days") {
  //           formattedStart = currentDateTime
  //             .subtract(6, "days")
  //             .startOf("day")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //           formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
  //         } else if (selectedRange === "Last Week") {
  //           formattedStart = currentDateTime
  //             .subtract(1, "week")
  //             .startOf("week")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //           formattedEnd = currentDateTime
  //             .subtract(1, "week")
  //             .endOf("week")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //         } else if (selectedRange === "Last 30 Days") {
  //           formattedStart = currentDateTime
  //             .subtract(29, "days")
  //             .startOf("day")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //           formattedEnd = currentDateTime
  //             .endOf("day")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //         } else if (selectedRange === "Last Month") {
  //           formattedStart = currentDateTime
  //             .subtract(1, "month")
  //             .startOf("month")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //           formattedEnd = currentDateTime
  //             .subtract(1, "month")
  //             .endOf("month")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //         } else if (selectedRange === "This Month") {
  //           formattedStart = currentDateTime
  //             .startOf("month")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //           formattedEnd = currentDateTime
  //             .endOf("day")
  //             .format("YYYY-MM-DD HH:mm:ss");
  //         }
  //       }

  //       let reqData = {
  //         name: payload.name,
  //         visualizationType: payload.visualizationType,
  //         filters: [],
  //         dataColumns: { derivedFields: payload.dataColumns?.derivedFields },
  //         startDate: convertToUTC(formattedStart),
  //         endDate: convertToUTC(formattedEnd),
  //       };

  //       let reqDataForDashBoard = {
  //         name: payload.name,
  //         visualizationType: payload.visualizationType,
  //         filters: [],
  //         dataColumns: { derivedFields: payload.dataColumns?.derivedFields },
  //         startDate: formattedStart,
  //         endDate: formattedEnd,
  //       };

  //       payload?.panelDetails?.filters?.forEach((condition) => {
  //         reqDataForDashBoard.filters.push({
  //           field: condition.field,
  //           condition: condition.condition,
  //           value: condition.value,
  //           operator: condition.operator,
  //         });
  //       });

  //       if (payload.visualizationType === "Bar Graph") {
  //         reqData.dataColumns["X-Axis"] = payload.dataColumns?.["X-Axis"];
  //         reqData.dataColumns.noOfRecords = payload.dataColumns.noOfRecords;
  //       } else {
  //         reqData.dataColumns.tableFields = payload.dataColumns?.tableFields;
  //       }

  //       payload?.filters.forEach((condition) => {
  //         reqData.filters.push({
  //           field: condition.field,
  //           condition: condition.condition,
  //           value: condition.value,
  //           operator: condition.operator,
  //         });
  //       });

  //       return previewPanel({ reqData })
  //         .then((resp) => {
  //           setPreviewResponses((prevResponses) => ({
  //             ...prevResponses,
  //             [payload.name]: resp,
  //             reqData: reqDataForDashBoard,
  //           }));
  //         })
  //         .catch((error) => {
  //           setPreviewResponses((prevResponses) => ({
  //             ...prevResponses,
  //             [payload.name]: "failed",
  //           }));
  //         });
  //     }
  //   });
  //   // .finally(() => {
  //   //   setIsLoadingPreviews(false);
  //   // });
  // };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleDragStart = (e, panelId) => {
    e.dataTransfer.setData(
      "data",
      JSON.stringify({
        type: "panel",
        data: panelId,
      })
    );
  };
  //console.log("previewResponses", previewResponses);
  return (
    <>
      {/* {isFetching ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            marginTop: "20px",
          }}
        >
          <CircularProgress size={20} style={{ color: "black" }} />
        </div>
      ) : (
        <div>
          {panelList &&
            panelList.data &&
            panelList.data.data &&
            panelList.data.data.map((panel) => (
              <div key={panel.id}>
                {!previewResponses[panel.name] ? (
                  <div className="border mt-2 mx-2 border-panelBorder rounded mb-2">
                    <div className="ml-3 mt-2 text-xs break-words">
                      {panel.name}
                    </div>
                    <div className="border-b mt-2 mx-2 border-panelBorder"></div>
                    <div className="my-3 flex justify-center items-center">
                      <CircularProgress size={20} style={{ color: "black" }} />
                    </div>
                  </div>
                ) : (
                  <div
                    key={panel.id}
                    className="border border-panelBorder rounded cursor-pointer mb-4 mx-2"
                    draggable
                    onDragStart={(e) =>
                      handleDragStart(e, panel, previewResponses)
                    }
                  >
                    <div className="ml-3 mt-2 text-xs break-words">
                      {panel.name}
                    </div>
                    <div className="border-b mt-2 mx-2 border-panelBorder"></div>
                    {previewResponses[panel.name] === "failed" ? (
                      <div className="my-3 flex justify-center items-center text-xs">
                        Something went wrong!!
                      </div>
                    ) : (
                      <PanelVisualization
                        type={panel.visualizationType}
                        data={
                          panel.visualizationType === "MultiAxis Graph"
                            ? previewResponses[panel.name]?.data
                            : previewResponses[panel.name]?.data?.data ?? []
                        }
                        colors={colors}
                        // columns={columns}
                        panel={panel}
                      />
                    )}
                  </div>
                )}
              </div>
            ))}
        </div>
      )} */}
      <div>
        {isLoading || isFetching ? (
          <div className="my-3 flex justify-center items-center">
            {" "}
            <CircularProgress size={20} style={{ color: "black" }} />
          </div>
        ) : (
          <>
            {" "}
            {panelList &&
              panelList.data &&
              panelList.data.data &&
              panelList.data.data.map((panel) => (
                <div key={panel.id}>
                  <div
                    key={panel.id}
                    className="border border-panelBorder rounded cursor-pointer mb-2 "
                    draggable={!loadingData && !previewLoading}
                    onDragStart={(e) => handleDragStart(e, panel.id)}
                  >
                    <div className="flex p-2">
                      {panel.visualizationType === "Bar Graph" ? (
                        <BarChartIcon className="w-3.5 h-3.5 justify-center items-center flex" />
                      ) : panel.visualizationType === "Line Graph" ? (
                        <LineChartIcon className="w-3.5 h-3.5 justify-center items-center flex" />
                      ) : panel.visualizationType === "Pie Chart" ? (
                        <PieChartIcon className="w-3.5 h-3.5 justify-center items-center flex" />
                      ) : panel.visualizationType === "MultiAxis Graph" ? (
                        <MultiAxisChartIcon className="w-3.5 h-3.5 justify-center items-center flex " />
                      ) : panel.visualizationType === "Table Report" ? (
                        <TableChartIcon className="w-4 h-4 justify-center items-center flex " />
                      ) : null}

                      <div className="text-xs text-titleColor ml-2 whitespace-pre-wrap break-words w-40">
                        {panel.name}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
          </>
        )}
      </div>
      {panelList?.data && panelList.data.totalCount !== undefined && (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            //marginRight: "20px",
          }}
        >
          <Pagination
            className="pagination-bar"
            currentPage={currentPage}
            totalCount={panelList?.data?.totalCount}
            pageSize={pageSize}
            onPageChange={(page) => handlePageChange(page)}
          />
        </div>
      )}
    </>
  );
}

export default PanelPreviewData;
