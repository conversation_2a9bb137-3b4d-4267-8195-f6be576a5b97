/**
 * Utility functions for filter validation
 */

/**
 * Check if at least one filter is selected from the form values
 * @param {Object} values - Formik form values
 * @param {Array} availableFilters - Array of available filter names
 * @param {Array} otherFilters - Array of other filter names
 * @returns {boolean} - True if at least one filter is selected
 */
export const hasSelectedFilters = (
  values,
  availableFilters = [],
  otherFilters = []
) => {
  // Define array-based filter fields
  const arrayFilterFields = [
    "customer_name",
    "customer_bind",
    "src_prime",
    "destination",
    "dest_prime",
    "supplier",
    "supplier_name",
    "supplier_bind",
    "destination_operator_name",
    "destination_country_name",
    "customer_interface_type",
    "supplier_interface_type",
    "customer_billing_logic",
    "supplier_billing_logic",
    "traffic_type_customer",
    "traffic_type_supplier",
    "customer_interconnect",
    "supplier_interconnect",
    "lcr_name",
    "spec_lcr",
    "status",
    "customer_kam",
    "supplier_kam",
  ];

  // Define text-based filter fields
  const textFilterFields = ["destination_mcc_final", "destination_mnc_final"];

  // Define boolean/other filter fields
  const otherFilterFields = [
    "roamingDirectStatus",
    "negative_report",
    "bilateral",
  ];

  // Check array fields - only if they are available filters
  const hasArrayFilters = arrayFilterFields.some((field) => {
    if (!availableFilters.includes(field)) return false;
    const value = values[field];
    return Array.isArray(value) && value.length > 0;
  });

  // Check text fields - only if they are available filters
  const hasTextFilters = textFilterFields.some((field) => {
    if (!availableFilters.includes(field)) return false;
    const value = values[field];
    return value && typeof value === "string" && value.trim() !== "";
  });

  // Check other filters - only if they are in otherFilters array
  const hasOtherFilters = otherFilterFields.some((field) => {
    switch (field) {
      case "roamingDirectStatus":
        return (
          (otherFilters.includes("only_roaming") ||
            otherFilters.includes("only_direct") ||
            otherFilters.includes("both")) &&
          values[field] &&
          values[field] !== ""
        );

      case "negative_report":
        return (
          otherFilters.includes("negative_report") && values[field] === true
        );

      case "bilateral":
        return otherFilters.includes("bilateral") && values[field] === true;

      default:
        return false;
    }
  });

  return hasArrayFilters || hasTextFilters || hasOtherFilters;
};

/**
 * Show alert for filter selection requirement using InfoModal
 * @param {Function} setShowModal - Function to show the InfoModal
 * @param {Function} setModalMessage - Function to set the modal message
 * @param {string} message - Custom message to show (optional)
 */
export const showFilterRequiredAlert = (
  setShowModal,
  setModalMessage,
  message = "At least one filter needs to be selected"
) => {
  setModalMessage(message);
  setShowModal(true);
};

/**
 * Validate filters and show alert if none selected
 * @param {Object} values - Formik form values
 * @param {Array} availableFilters - Array of available filter names
 * @param {Array} otherFilters - Array of other filter names
 * @param {Function} setShowModal - Function to show the InfoModal
 * @param {Function} setModalMessage - Function to set the modal message
 * @param {string} customMessage - Custom alert message (optional)
 * @returns {boolean} - True if validation passes, false otherwise
 */
export const validateAndAlertFilters = (
  values,
  availableFilters = [],
  otherFilters = [],
  setShowModal,
  setModalMessage,
  customMessage,
  activeView
) => {
  if (!hasSelectedFilters(values, availableFilters, otherFilters)) {
    showFilterRequiredAlert(setShowModal, setModalMessage, customMessage);
    return false;
  }
  return true;
};
