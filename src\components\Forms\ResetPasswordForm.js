import React, { useState } from "react";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import { useParams, useNavigate } from "react-router-dom";
import { userService } from "../../services/user.service";
import SuccessDialog from "../../popups/SuccessDialog";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { passwordValidation } from "../../common/yupValidation";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { useQuery } from "react-query";
import { ConfigService } from "../../services/config-api";
import { encryptPassword } from "../../common/commonFunctions";

function ResetPasswordForm() {
  const [message, setMessage] = useState("");
  const [successDialog, setSuccessDialog] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [publicKey, setPublicKey] = useState(null);

  const navigate = useNavigate();
  // get token from paran
  const { token } = useParams();
  var style = {
    asterisk: {
      color: "red",
      marginLeft: "3px",
    },
    fieldstyle:
      "form-control  w-full mt-2 text-sm pl-5 text-black bg-white bg-clip-padding border border-solid border-Bittersweet rounded transition ease-in-out  m-0 inline-block h-12  font-semibold ",
  };
  const handleResetSuccess = () => {
    setSuccessDialog(false);
    navigate("/auth/login");
  };

  const handleFiledClear = (e, fieldName, setFieldValue) => {
    if (e.key === "Backspace" || e.key === "Delete") {
      e.preventDefault();
      e.target.value = "";
      const inputEvent = new Event("input", { bubbles: true });
      e.target.dispatchEvent(inputEvent);
      setFieldValue(fieldName, "");
    }
  };

  useQuery(["key"], ConfigService.getPublicKey, {
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      setPublicKey(data.publicKey);
    },
  });

  return (
    <div>
      <Formik
        initialValues={{
          confirmPassword: "",
          password: "",
        }}
        validationSchema={Yup.object().shape({
          password: passwordValidation.required("New Password is Required"),
          confirmPassword: Yup.string()
            .oneOf([Yup.ref("password"), null], "Password must match")
            .required("Confirm Password is Required"),
        })}
        onSubmit={async (
          { confirmPassword, password },
          { setStatus, setSubmitting }
        ) => {
          setSubmitting(true);
          setStatus();
          const encryptedPassword = await encryptPassword(password, publicKey);
          if (!encryptedPassword) {
            setSubmitting(false);
            return;
          }
          userService
            .resetPassword(token, encryptedPassword)
            .then((res) => {
              setSuccessDialog(true);
              setMessage("Your password has been changed successfully");
              setSubmitting(false);
            })
            .catch((error) => {
              setSubmitting(false);
              setSuccessDialog(false);
              toast.error(
                error?.response?.data?.message
                  ? error?.response?.data?.message
                  : error
              );
            });
        }}
      >
        {({ errors, status, touched, isSubmitting, setFieldValue }) => (
          <Form>
            <label className="w-full">
              <span>New Password</span> <span style={style.asterisk}>*</span>
              <div className="relative">
                <Field
                  className={style.fieldstyle}
                  as={"input"}
                  name="password"
                  type={showNewPassword ? "text" : "password"}
                  placeholder="Enter the new password"
                  onKeyDown={(e) =>
                    handleFiledClear(e, "password", setFieldValue)
                  }
                />
                <div
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowNewPassword((prev) => !prev)}
                >
                  {showNewPassword ? <FaEye /> : <FaEyeSlash />}{" "}
                </div>
                {errors.password && touched.password ? (
                  <div>
                    <p className="text-errorColor" valid={false}>
                      {errors.password}
                    </p>
                  </div>
                ) : null}
              </div>
            </label>

            <label className="mt-8 w-full">
              <span>Confirm Password</span>{" "}
              <span style={style.asterisk}>*</span>
              <div className="relative">
                <Field
                  className={style.fieldstyle}
                  as={"input"}
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm new password"
                  onKeyDown={(e) =>
                    handleFiledClear(e, "confirmPassword", setFieldValue)
                  }
                />
                <div
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword((prev) => !prev)}
                >
                  {showConfirmPassword ? <FaEye /> : <FaEyeSlash />}{" "}
                </div>
                {errors.confirmPassword && touched.confirmPassword ? (
                  <div>
                    <p className="text-errorColor" valid={false}>
                      {errors.confirmPassword}
                    </p>
                  </div>
                ) : null}
              </div>
            </label>

            <button
              className="mt-8 bg-red-500 text-white text-sm  w-full py-2 rounded-md"
              block="true"
              type="submit"
              value="submit"
              disabled={isSubmitting}
              style={{ height: "38px" }}
            >
              Reset
            </button>
          </Form>
        )}
      </Formik>{" "}
      <ToastContainer position="top-center" autoClose={3000} />
      <SuccessDialog
        show={successDialog}
        onHide={handleResetSuccess}
        message={message}
        btnName={"Login"}
      />
    </div>
  );
}

export default ResetPasswordForm;
