export const role_management_constants = {
  resource: [
    {
      name: "Card Management",
      permissions: {
        create: 0,
        view: 0,
        update: 0,
        delete: 0,
      },
    },
    {
      name: "Logs Management",
      permissions: {
        create: 0,
        view: 1,
        update: 0,
        delete: 0,
      },
    },
    {
      name: "Default Dashboard",
      permissions: {
        create: 0,
        view: 1,
        update: 0,
        delete: 0,
      },
    },

    {
      name: "Alert Management",
      permissions: {
        create: 0,
        view: 0,
        update: 0,
        delete: 0,
      },
    },
    {
      name: "Group Management",
      permissions: {
        create: 0,
        view: 0,
        update: 0,
        delete: 0,
      },
    },
    {
      name: "Panel Management",
      permissions: {
        create: 0,
        view: 0,
        update: 0,
        delete: 0,
      },
    },
    {
      name: "Report Management",
      permissions: {
        create: 0,
        view: 0,
        update: 0,
        delete: 0,
      },
    },
    {
      name: "Dashboard Management",
      permissions: {
        create: 0,
        view: 0,
        update: 0,
        delete: 0,
      },
    },
    {
      name: "CDR Search",
      permissions: {
        create: 0,
        view: 0,
        update: 0,
        delete: 0,
        download: 0,
      },
    },
  ],
  initial_values: {
    key: 0,
    name: "",
    dashboardCount: 0,
    panelCount: 0,
    staticReports: [],
    dynamicReports: [],
    // cardsList: [],
    dynamicDashboard: [],
    resources: {
      //"User Management": [],
      "Report Management": [],
      "Card Management": [],
      //"Role Management": [],
      "Panel Management": [],
      "Dashboard Management": [],
      "Logs Management": ["view"],
      "Alert Management": [],
      "Group Management": [],
      "Default Dashboard": ["view"],
      "CDR Search": [],
    },
  },
};

export const TIME_ZONE = "Asia/Kolkata";

export const OFFLINE_DOWNLOAD = 100000;

export const timeRanges = [
  "Last Hour",
  "Last 6 Hours",
  "Last 12 Hours",
  "Last 24 Hours",
  "Today",
  "Yesterday",
  "Last Seven Days",
  "Last Week",
  "Last 30 Days",
  "This Month",
  "Last Month",
  "This Year",
  "Last Year",
  "Calendar",
];

export const MAX_DECIMALS = 4;

export const NO_FILTER = [
  "Billing",
  "WhatsApp Traffic Report",
  "Facebook Traffic Report",
];

export function removeSelectAllFromPayload(obj) {
  if (Array.isArray(obj)) {
    return obj
      .filter((item) => item !== "Select All")
      .map(removeSelectAllFromPayload);
  } else if (obj && typeof obj === "object") {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => [
        key,
        removeSelectAllFromPayload(value),
      ])
    );
  }
  return obj;
}
