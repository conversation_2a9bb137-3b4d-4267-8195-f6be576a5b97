import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@mui/material";
import { CloseIcon, WhiteUpload, DeleteIcon, CloudUploadIcon } from "../icons";
import { useDropzone } from "react-dropzone";
import { useMutation, useQueryClient } from "react-query";
import getAPIMap from "../routes/ApiUrls";
import axios from "axios";
import ErrorDialog from "../popups/ErrorDialog";

function BulkUploadDialog({
  show,
  onClose,
  setFile,
  file,
  setGroups,
  setFilename,
  filename,
  setSelectedFileName,
  selectedFileName,
  selectedFile,
  setSelectedFile,
  setListData,
}) {
  const queryClient = useQueryClient();
  const [showNotFileType, setShowNotFileType] = useState(false);
  const [message, setMessage] = useState("");

  useEffect(() => {
    if (file) {
      setSelectedFileName(filename);
      setSelectedFile(file);
    } else {
      // Reset when file is cleared
      setSelectedFileName("");
      setSelectedFile(null);
    }
  }, [file, filename]);

  async function extractFileContent(options) {
    let url = getAPIMap("bulkEmailUpload");
    let formData = new FormData();
    formData.append("file", options.file);

    let response = await axios.post(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  }

  const { mutate: extractData } = useMutation(extractFileContent, {
    onSuccess: (data) => {
      onClose();
      setFile((prev) => [...(prev || []), ...(data?.data?.data || [])]);
      setFilename(data?.data?.filename);
      const groupDetails = data?.data?.data?.map((item) => ({
        label: item.name,
        value: item.name,
        members: item.members,
        subGroups: item.subGroups,
      }));
      setGroups((prev) => [...(prev || []), ...groupDetails]);
      setSelectedFileName("");
      setSelectedFile(null);
      queryClient.invalidateQueries(["groupList", 1, 1000]);
      //   setCurrentStep(0);
    },
    onError: (data) => {
      setShowNotFileType(true);
      setMessage(data?.response?.data?.message || "File upload failed");
    },
  });

  const onDrop = (acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      const maxFileSize = 5 * 1024 * 1024;
      const allowedTypes = [
        "text/csv",
        "application/vnd.ms-excel", // .xls
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
      ];

      if (!allowedTypes.includes(file.type)) {
        setShowNotFileType(true);
        setMessage("Only CSV, XLS, or XLSX file formats are allowed");
      } else if (file.size > maxFileSize) {
        setShowNotFileType(true);
        setMessage("File size exceeds 5MB limit");
      } else {
        // setSelectFile(file);
        setSelectedFileName(file.name);
        setSelectedFile(file);
      }
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    // accept: {
    //   "text/csv": [".csv"],
    //   "application/vnd.ms-excel": [".xls"],
    //   "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
    //     ".xlsx",
    //   ],
    // },
    multiple: false,
    maxSize: 5 * 1024 * 1024,
  });

  const clearFile = (e) => {
    e.stopPropagation();
    setSelectedFileName("");
    setSelectedFile(null);
    setFile(null);
    setGroups([]); // Clear parent groups data
    // setSelectFile(null);
  };

  const handleClose = (e) => {
    e.stopPropagation();
    setSelectedFileName("");
    setSelectedFile(null);
    // setSelectFile(null);
    onClose();
  };

  const handleUploadClick = () => {
    if (selectedFile) {
      extractData({ file: selectedFile });
    }
    setSelectedFileName("");
    setSelectedFile(null);
  };

  return (
    <>
      <Dialog
        sx={{
          "& .MuiDialog-paper": {
            width: "100%",
            maxWidth: "500px",
            maxHeight: "370px",
            fontFamily: "OpenSanHebrew",
            borderRadius: "10px",
          },
        }}
        maxWidth="xs"
        open={show}
        onClose={handleClose}
        className="p-6 font-sans"
      >
        <DialogTitle className="pb-0 flex justify-end">
          <CloseIcon className="w-3 h-3 cursor-pointer" onClick={handleClose} />
        </DialogTitle>
        <div className="flex justify-center font-bold text-[14px]">
          {selectedFile
            ? "Your file is uploaded successfully"
            : "Choose a email list file to upload"}
        </div>
        <div className="flex justify-center text-[10px] text-[#808080]">
          {selectedFile ? "" : "only csv, xls, xlsx file allowed"}
        </div>
        <DialogContent className="flex flex-col items-center p-6 font-open-sans-hebrew text-medium py-1">
          <div
            {...getRootProps()}
            className="border-2 border-dashed border-[#808080] rounded-lg w-[377px] h-[200px] flex flex-col justify-center items-center p-2 mt-2 relative mb-4"
          >
            <input {...getInputProps()} />
            {selectedFile ? (
              <div className="flex justify-center items-center w-full p-2 align-center">
                <span className="truncate">{selectedFileName}</span>
                <button
                  className="text-red-500 hover:text-red-700 ml-2"
                  onClick={clearFile}
                >
                  <DeleteIcon className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <>
                <CloudUploadIcon />
                <p className="text-[#808080] mt-0 mb-0 text-[12px]">
                  {selectedFile
                    ? ""
                    : "Drag & drop csv, xls or xlsx file here "}
                </p>
              </>
            )}
          </div>
          {!selectedFile && (
            <>
              <p className="text-[#808080] mt-0 text-[10px] mb-2">or</p>

              <div
                {...getRootProps()}
                className="bg-bgSecondary text-xs font-bold font-open-sans-hebrew p-2 rounded-lg flex justify-center text-center w-[254px] h-[33px] items-center cursor-pointer gap-2 text-white mb-4"
              >
                <input {...getInputProps()} /> {/* hidden input */}
                <WhiteUpload className="w-3 h-3" /> Upload
              </div>
            </>
          )}
          {selectedFile && !file && (
            <div
              className="bg-bgSecondary text-xs font-bold font-open-sans-hebrew p-2 rounded-lg flex justify-center text-center w-[254px] h-[33px] items-center cursor-pointer gap-2 text-white mb-4"
              onClick={handleUploadClick}
            >
              Save
            </div>
          )}
        </DialogContent>
      </Dialog>
      {showNotFileType && (
        <ErrorDialog
          show={showNotFileType}
          onHide={() => setShowNotFileType(false)}
          message={message}
        />
      )}
    </>
  );
}

export default BulkUploadDialog;
