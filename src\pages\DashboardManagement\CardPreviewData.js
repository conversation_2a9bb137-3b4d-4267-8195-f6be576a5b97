import React, { useState, useEffect } from "react";
import { getAllCards } from "../../services/cards-preview-api";
import { useQuery } from "react-query";
import Pagination from "../../components/Pagination/Pagination";
import { CircularProgress } from "@mui/material";
import CardData from "./CardData";

function CardPreviewData({ isCardAdded, setTotalCount, handleCardAddedFalse }) {
  const [currentPage, setCurrentPage] = useState(1);
  const [errorMessage, setErrorMessage] = useState("");
  const pageSize = 4;
  const {
    data: dataList,
    isLoading,
    refetch,
  } = useQuery(["cardDetails", currentPage, pageSize], getAllCards, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      handleCardAddedFalse();
    },
    onError: (err) => {
      //console.log("err", err);
      setErrorMessage("Something went wrong!");
    },
  });

  useEffect(() => {
    if (isCardAdded) {
      refetch();
    }
  }, [isCardAdded, refetch]);

  useEffect(() => {
    if (dataList?.data?.totalCount !== undefined) {
      setTotalCount((oldState) => ({
        ...oldState,
        card: dataList?.data?.totalCount,
      }));
    }
  }, [dataList]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleDragStart = (e, card) => {
    e.dataTransfer.setData(
      "data",
      JSON.stringify({ type: "card", data: card })
    );
  };

  return (
    <div>
      {" "}
      <div className="flex flex-col gap-4 min-w-[200px]">
        {errorMessage && !isLoading ? (
          <div className="my-3 flex justify-center items-center text-black text-sm">
            {errorMessage}{" "}
          </div>
        ) : isLoading ? (
          <div className="my-3 flex justify-center items-center">
            <CircularProgress size={20} style={{ color: "black" }} />
          </div>
        ) : (
          <div className="flex flex-col gap-4 min-w-[200px]">
            {dataList?.data?.data.map((card, index) => (
              <div
                onDragStart={(e) => handleDragStart(e, card)}
                draggable={!isLoading}
              >
                <CardData card={card} isLoading={isLoading} />
              </div>
            ))}
          </div>
        )}
      </div>
      {dataList?.data && dataList?.data?.totalCount !== undefined && (
        <div className="flex justify-center mr-6 mt-4">
          <Pagination
            className="pagination-bar"
            currentPage={currentPage}
            totalCount={dataList?.data?.totalCount}
            pageSize={pageSize}
            onPageChange={(page) => handlePageChange(page)}
          />
        </div>
      )}
    </div>
  );
}

export default CardPreviewData;
