import React, { useState } from "react";
import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import Dialog from "@mui/material/Dialog";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Box from "@mui/material/Box";
import Button from "../components/Button/OutlinedButton";
import CancelButton from "../components/Button/Button";
import ChipInput from "../components/Chip/ChipInput";
import { CloseIcon, MailBoxIcon, InfoIcon } from "../icons";
import SuccessDialog from "./SuccessDialog";
import ErrorDialog from "./ErrorDialog";
import { alertServices } from "../services/alert-api";
import { useQuery } from "react-query";
import { CssTooltip } from "../components/StyledComponent";
import { sendMail } from "../services/staticreport.service";
import { useMutation } from "react-query";
import { RadioGroup, Radio } from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import CustomDropDown from "../components/Dropdown/CustomeDropdown";
import { previewPanel } from "../services/panels-api";

export default function SendMail({
  closeGroupDialog,
  openGroupDialog,
  selectedFilter,
  searchStr,
  reportName,
  timeZone,
  payloadData,
  dynamicReports,
  filters,
  reportFilters,
  columnConfigData,
  graphFilters,
  graphValidation,
}) {
  const handleChipAdd = (chip, formikBag) => {
    const newChips = [...formikBag.values.chips, chip];

    setGroupMembers(newChips);
    formikBag.setFieldValue("chips", newChips);
  };

  const handleChipRemove = (index, formikBag) => {
    const newChips = [...formikBag.values.chips];
    newChips.splice(index, 1);
    setGroupMembers(newChips);
    formikBag.setFieldValue("chips", newChips);
  };

  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [errorDialog, setErrorDialog] = useState(false);
  const [nameData, setNameData] = useState([]);
  const [selectedName, setSelectedName] = useState([]);
  const [groupMembers, setGroupMembers] = useState([]);
  const [isEdit, setIsEdit] = useState(false);
  const [attachmentType, setAttachmentType] = useState("EXCEL");
  const [mailType, setMailType] = useState("Individual");
  const [subGroupsNames, setSubGroupsNames] = useState([]);
  const [selectedSubGroup, setSelectedSubGroup] = useState([]);
  const { data: groupDetails } = useQuery(
    ["groupList", "", ""],
    alertServices.getAll,
    {
      onSuccess: ({ data }) => {
        if (
          data?.response?.data?.code === 403 ||
          data?.response?.data?.code === "Forbidden"
        ) {
          setNameData(nameData);
        } else {
          const groupDetails = data?.data?.map((item) => ({
            label: item.name,
            value: item.name,
            members: item.members,
            subGroups: item.subGroups,
          }));
          setNameData(groupDetails);
        }
      },
      onError: (error) => {
        if (
          error?.response?.data?.code === 403 ||
          error?.response?.data?.message === "Forbidden"
        ) {
          setNameData(nameData);
        }
      },
    }
  );
  const handleGroupSelection = (selectedDetail) => {
    const matchingEntries = nameData?.filter((entry) =>
      selectedDetail.includes(entry.value)
    );

    const allMembers = matchingEntries?.flatMap((entry) => entry.members);
    setGroupMembers((prevGroupMembers) => {
      const uniqueMembers = Array.from(new Set(allMembers));
      return uniqueMembers;
    });

    const subdata = matchingEntries?.flatMap((entry) =>
      entry?.subGroups?.map((subGroup) => subGroup?.name)
    );
    setSubGroupsNames((prevSubGroupsNames) => {
      const uniqueSubGroupsNames = Array.from(new Set(subdata));
      return uniqueSubGroupsNames;
    });

    const allSubGroups = matchingEntries.flatMap((entry) => entry?.subGroups);
    setSelectedSubGroup(allSubGroups);
  };

  const emailValidationSchema = Yup.string()
    .email("Invalid email address")
    .required("Email address is required");

  const validationSchema = Yup.object().shape({
    mailType: Yup.string().required(),
    chips: Yup.mixed().when("mailType", {
      is: (value) => value === "Individual",
      then: () =>
        Yup.array()
          .of(Yup.string().required("Email address is required"))

          .test("is-min-of-6", "Min length allowed is 6", function (value) {
            if (!value) return true;
            return value.every((email) =>
              Yup.string().min(6).isValidSync(email)
            );
          })
          .test(
            "is-max-of-256",
            "Max characters allowed is 256",
            function (value) {
              if (!value) return true;
              return value.every((email) =>
                Yup.string().max(256).isValidSync(email)
              );
            }
          )
          .test("is-valid-emails", "Invalid email address", function (value) {
            if (!value) return true;
            return value.every((email) =>
              Yup.string().email().isValidSync(email)
            );
          })
          .min(1, "Email address is required"),
      otherwise: (schema) => schema,
    }),
    groupName: Yup.array().when("mailType", {
      is: (value) => value === "Group",
      then: () => Yup.array().min(1, "Group is required"),
      otherwise: (schema) => schema,
    }),
  });

  const { mutate: sendMailAPI, isLoading: loading } = useMutation(sendMail);
  const { mutate: previewPanelAPIData, isLoading: loadingData } =
    useMutation(previewPanel);

  return (
    <Box width={"100%"}>
      <Formik
        initialValues={{
          groupName: selectedName ? selectedName : [],
          chips: groupMembers ? groupMembers : [],
          mailType: mailType ? mailType : "",
          groupChips: subGroupsNames ? subGroupsNames : [],
        }}
        enableReinitialize={true}
        validationSchema={validationSchema}
        onSubmit={({ groupName, chips, groupChips }, { setSubmitting }) => {
          const uniqueNames = new Set();
          const filtered = selectedSubGroup.filter((item) => {
            if (!uniqueNames.has(item.name)) {
              uniqueNames.add(item.name);
              return true;
            }
            return false;
          });
          if (graphValidation && Object.keys(graphFilters).length === 0) {
            setMessage("Please setup graph filters before sending mail");
            setErrorDialog(true);
            closeGroupDialog(true);
            return;
          }

          let groupList = filtered.map((y) => {
            return y.id;
          });
          let payload = {
            reportName: reportName,
            type: attachmentType,
            sendEmail: 1,
            mailList: chips,
            search: searchStr,
            groupList: groupList,
            timezone: timeZone,
          };
          if (graphValidation) {
            payload.graphFilters = graphFilters;
            payload.isGraph = true;
          }
          if (!columnConfigData?.noStartEndDate) {
            payload.startDate = selectedFilter.startDate;
            payload.endDate = selectedFilter.endDate;
          }
          if (filters && filters.length !== 0) {
            payload.filters = filters;
          }

          if (!graphValidation) {
            payload.isGraph = false;
          }
          //  console.log("filters", filters);
          if (dynamicReports) {
            let reqData = {
              ...payloadData,
              type: attachmentType,
              sendEmail: 1,
              mailList: chips,
              groupList: groupList,
              name: reportName,
              graphFilters: graphFilters,
            };
            if (reportFilters && reportFilters.length !== 0) {
              reqData.reportFilters = reportFilters;
            }
            if (graphValidation) {
              payload.graphFilters = graphFilters;
            }
            previewPanelAPIData(
              {
                reqData,
              },
              {
                onSuccess: (resp) => {
                  closeGroupDialog();
                  setSuccessDialog(true);
                  setMessage("Email sent successfully");
                },
                onError: (error) => {
                  closeGroupDialog();
                  setErrorDialog(true);
                  setMessage(
                    error?.response?.data?.message || "No records to send"
                  );
                },
              }
            );
          } else {
            sendMailAPI(
              {
                payload,
              },
              {
                onSuccess: (resp) => {
                  closeGroupDialog();
                  setSuccessDialog(true);
                  setMessage("Email sent successfully");
                },
                onError: (error) => {
                  closeGroupDialog();
                  setErrorDialog(true);
                  setMessage(
                    error?.response?.data?.message || "No records to send"
                  );
                },
              }
            );
          }
        }}
      >
        {({ setFieldValue, resetForm, errors, touched }) => (
          <Dialog
            open={openGroupDialog}
            onClose={() => {
              closeGroupDialog();
              resetForm();
              setSelectedName([]);
              setGroupMembers([]);
            }}
            fullWidth
            onClick={(event) => {
              if (event.target === event.currentTarget) {
                closeGroupDialog();
                resetForm();
                setSelectedName([]);
                setGroupMembers([]);
              }
            }}
            sx={{
              "& .MuiDialog-container": {
                "& .MuiPaper-root": {
                  width: "100%",
                  maxWidth:
                    selectedName && mailType === "Group" ? "740px" : "400px",
                  minHeight: "350px",
                  margin: 0,
                },
              },
            }}
          >
            <Form
              //encType="multipart/form-data"
              onKeyPress={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                }
              }}
            >
              <div className="mx-4  mb-5">
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center">
                    <MailBoxIcon className="w-5 h-5" />
                    <div className=" text-black text-base font-medium ml-2">
                      {"Send Email"}
                    </div>
                  </div>
                  <CloseIcon
                    onClick={() => {
                      closeGroupDialog();
                      resetForm();
                      setSelectedName([]);
                      setGroupMembers([]);
                    }}
                    className="w-2.5 h-2.5 cursor-pointer"
                  />
                </div>

                <div className="mt-2  mb-3 border-b border-panelBorder" />
                <div className="flex">
                  <div>
                    <div className="flex flex-col md:w-[350px]  mt-4">
                      <InputLabel
                        label="Send Email"
                        isMandatory={true}
                        labelClassName={"mb-2"}
                      />
                      <RadioGroup
                        row
                        value={mailType}
                        onChange={(e) => {
                          const selectedValue = e.target.value;
                          setMailType(selectedValue);
                          if (selectedValue === "Individual") {
                            setSelectedName([]);
                            setGroupMembers([]);
                          }
                        }}
                      >
                        <FormControlLabel
                          value="Individual"
                          control={
                            <Radio
                              sx={{
                                "& .MuiSvgIcon-root": { fontSize: "16px" },
                                "&.Mui-checked .MuiSvgIcon-root": {
                                  color: "black",
                                },
                              }}
                            />
                          }
                          label={
                            <span className="text-tabColor text-sm">
                              Individual
                            </span>
                          }
                        />
                        <FormControlLabel
                          value="Group"
                          control={
                            <Radio
                              sx={{
                                "& .MuiSvgIcon-root": { fontSize: "16px" },
                                "&.Mui-checked .MuiSvgIcon-root": {
                                  color: "black",
                                },
                              }}
                            />
                          }
                          label={
                            <span className="text-tabColor text-sm">Group</span>
                          }
                        />
                      </RadioGroup>
                    </div>

                    {mailType === "Individual" ? (
                      <div className="flex flex-col mt-4 mb-3">
                        <div className="flex mb-2">
                          <InputLabel label={"Individual"} isMandatory={true} />
                          <InputLabel
                            label={"(Press enter to add email id)"}
                            labelClassName={"text-xs ml-1 mt-0.5"}
                          />
                        </div>

                        <Field name="chips">
                          {({ field, form }) => (
                            <div>
                              <ChipInput
                                chips={field.value}
                                onChipAdd={(chip) => handleChipAdd(chip, form)}
                                onChipRemove={(index) =>
                                  handleChipRemove(index, form)
                                }
                                isEdit={true}
                              />
                              <p style={{ fontSize: "12px", color: "red" }}>
                                <ErrorMessage name="chips" />
                              </p>
                            </div>
                          )}
                        </Field>
                      </div>
                    ) : (
                      <>
                        <div className="flex flex-col">
                          {mailType === "Group" && (
                            <div className="w-[340px]">
                              <div className="w-full md:w-[340px] mt-4">
                                <InputLabel
                                  label={"Group"}
                                  isMandatory={true}
                                  labelClassName={"mb-3"}
                                />
                                <CustomDropDown
                                  btnWidth="w-[340px] lg:w-[340px]"
                                  data={nameData}
                                  btnName={"Select Group"}
                                  onSelectionChange={(selectedDetail) => {
                                    const selectedValue = selectedDetail
                                      ? selectedDetail.value
                                      : "";

                                    setFieldValue("groupName", selectedDetail);
                                    setIsEdit(false);
                                    setSelectedName(selectedDetail);
                                    handleGroupSelection(selectedDetail);
                                  }}
                                />
                              </div>
                              <p style={{ fontSize: "12px", color: "red" }}>
                                <ErrorMessage name="groupName" />
                              </p>
                            </div>
                          )}
                        </div>
                      </>
                    )}
                    <div className="flex flex-col mb-10 mt-5">
                      <InputLabel
                        label="Attachment Type"
                        isMandatory={true}
                        labelClassName={"mb-2"}
                      />
                      <RadioGroup
                        row
                        value={attachmentType}
                        onChange={(e) => setAttachmentType(e.target.value)}
                      >
                        <FormControlLabel
                          value="EXCEL"
                          control={
                            <Radio
                              sx={{
                                "& .MuiSvgIcon-root": { fontSize: "16px" },
                                "&.Mui-checked .MuiSvgIcon-root": {
                                  color: "black",
                                },
                              }}
                            />
                          }
                          label={
                            <span className="text-tabColor text-xs">EXCEL</span>
                          }
                        />
                        <FormControlLabel
                          value="CSV"
                          control={
                            <Radio
                              sx={{
                                "& .MuiSvgIcon-root": { fontSize: "16px" },
                                "&.Mui-checked .MuiSvgIcon-root": {
                                  color: "black",
                                },
                              }}
                            />
                          }
                          label={
                            <span className="text-tabColor text-xs">CSV</span>
                          }
                        />
                      </RadioGroup>
                    </div>
                  </div>
                  {selectedName?.length > 0 && (
                    <div className="w-full md:w-[340px]  mt-4 mb-4 ml-3 ">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div
                            className="w-[250px] "
                            style={{ overflowWrap: "break-word" }}
                          >
                            <InputLabel label={"Members"} />
                          </div>
                          <div
                            className="rounded-2xl w-5 h-5 bg-bgouterBackground p-1.5 flex items-center justify-center cursor-pointer ml-2"
                            onClick={() => {
                              setIsEdit(true);
                            }}
                          >
                            <CssTooltip
                              title={"Add Email"}
                              placement="top"
                              arrow
                            >
                              <span className="w-10 h-10 items-center justify-center mt-3.5 font-semibold">
                                +
                              </span>
                            </CssTooltip>
                          </div>
                        </div>
                        <CssTooltip
                          title={
                            "Addition or removing the email id will not be saved"
                          }
                          placement="left"
                          arrow
                        >
                          <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
                        </CssTooltip>
                      </div>

                      <Field name="chips">
                        {({ field, form }) => (
                          <ChipInput
                            chips={field.value}
                            onChipAdd={(chip) => handleChipAdd(chip, form)}
                            onChipRemove={(index) =>
                              handleChipRemove(index, form)
                            }
                            isEdit={isEdit}
                          />
                        )}
                      </Field>
                      <p
                        style={{ fontSize: "12px", color: "red" }}
                        valid={false}
                      >
                        <ErrorMessage name="chips" />
                      </p>
                      <div className="flex items-center justify-between mt-3">
                        <div className="flex items-center">
                          <InputLabel label={"Sub Groups"} />
                        </div>
                        {/* <CssTooltip
                          title={"Removing the groups will not be saved"}
                          placement="left"
                          arrow
                        >
                          <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
                        </CssTooltip> */}
                      </div>
                      <Field name="groupChips">
                        {({ field, form }) => (
                          <ChipInput
                            chips={field.value}
                            onChipRemove={() => {}}
                            onChipAdd={() => {}}
                            isEdit={false}
                            inputProps={{ readOnly: true }}
                          />
                        )}
                      </Field>

                      <ErrorMessage
                        name="groupChips"
                        component={"div"}
                        className="text-errorColor text-xs"
                      />
                    </div>
                  )}
                </div>
                <div>
                  <div className="text-end ">
                    <CancelButton
                      onClick={() => {
                        closeGroupDialog();
                        resetForm();
                        setSelectedName("");
                        setGroupMembers([]);
                      }}
                      label={"Cancel"}
                      buttonClassName="w-[100px] h-9 text-xs"
                    ></CancelButton>
                    <Button
                      type="submit"
                      label={"Send"}
                      buttonClassName="w-[100px] h-9 text-xs ml-5"
                      loading={loading || loadingData}
                    ></Button>
                  </div>
                </div>
              </div>
            </Form>
          </Dialog>
        )}
      </Formik>
      <SuccessDialog
        show={suceessDialog}
        onHide={() => {
          setSelectedName([]);
          setGroupMembers([]);
          setSuccessDialog(false);
        }}
        message={message}
      />
      <ErrorDialog
        show={errorDialog}
        onHide={() => {
          setSelectedName([]);
          setGroupMembers([]);
          setErrorDialog(false);
        }}
        message={message}
      />
    </Box>
  );
}
