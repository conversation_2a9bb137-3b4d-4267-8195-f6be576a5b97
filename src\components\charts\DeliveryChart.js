import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import sampleData from "../../sample.json";

const DeliveryChart = () => {
  const customers = new Map();
  const colors = [
    "#2E86C1", // Medium blue
    "#E67E22", // Medium orange
    "#8E44AD", // Medium purple
    "#27AE60", // Medium green
    "#E74C3C", // Medium red
    "#16A085", // Medium teal
    "#D35400", // Medium deep orange
    "#2980B9", // Medium blue-grey
    "#C0392B", // Medium crimson
    "#7D3C98", // Medium violet
    "#2ECC71", // Medium spring green
    "#E59866", // Medium coral
    "#5499C7", // Medium steel blue
    "#45B39D", // Medium sea green
    "#AF7AC5", // Medium orchid
  ];

  sampleData.forEach((customer, index) => {
    if (!customers.has(customer.Customer)) {
      customers.set(customer.Customer, {
        data: [],
        totalColor: colors[(index * 2) % colors.length],
        errorColor: colors[(index * 2 + 1) % colors.length],
      });
    }
    customers.get(customer.Customer).data.push(customer);
  });

  // Transform data to have a single date point with all customer values
  const transformedData = [];
  const allDates = new Set();

  // Collect all unique dates
  customers.forEach((customerData) => {
    customerData.data.forEach((item) => {
      allDates.add(item.Date);
    });
  });

  // Create data points for each date with all customer values
  allDates.forEach((date) => {
    const dataPoint = { Date: date };
    customers.forEach((customerData, customerName) => {
      const customerDayData =
        customerData.data.find((item) => item.Date === date) || {};
      dataPoint[`${customerName}_Total`] =
        customerDayData["Total Submission"] || 0;
      dataPoint[`${customerName}_Error`] =
        customerDayData["Submission Error"] || 0;
    });
    transformedData.push(dataPoint);
  });

  return (
    <div style={{ width: "100%", height: 400 }}>
      <ResponsiveContainer>
        <LineChart
          data={transformedData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <XAxis dataKey="Date" />
          <YAxis />
          <Tooltip />
          <Legend />
          {Array.from(customers.entries()).map(
            ([customerName, customerData]) => (
              <React.Fragment key={customerName}>
                <Line
                  type="monotone"
                  dataKey={`${customerName}_Total`}
                  name={`${customerName} Total Submission`}
                  stroke={customerData.totalColor}
                  activeDot={{ r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey={`${customerName}_Error`}
                  name={`${customerName} Submission Error`}
                  stroke={customerData.errorColor}
                  activeDot={{ r: 4 }}
                />
              </React.Fragment>
            )
          )}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default DeliveryChart;
