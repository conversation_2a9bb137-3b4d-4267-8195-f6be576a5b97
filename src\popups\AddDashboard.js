import React, { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Dialog from "@mui/material/Dialog";
import TextFieldWrapper from "../components/FormsUI/TextField";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Box from "@mui/material/Box";
import Button from "../components/Button/OutlinedButton";
import CancelButton from "../components/Button/Button";
import { useNavigate } from "react-router-dom";
import { CloseIcon } from "../icons";

export default function AddDashboard({ handleClose, open }) {
  const onlySpaceRegex = /(?=.*[a-z])|(?=.*[A-Z])/;
  const navigate = useNavigate();
  return (
    <Box width={"100%"}>
      <Formik
        initialValues={{
          name: "",
        }}
        validationSchema={Yup.object({
          name: Yup.string()
            .required("Dashboard Name is Required")
            .max(256, "Max length allowed is 256 characters")
            .min(2, "Min length allowed is 2  characters")
            .matches(onlySpaceRegex, "Must contain atleast one Alphabet"),
        })}
        onSubmit={({ name }, { setStatus, setSubmitting }) => {
          // console.log("Values", name);
          navigate("/app/dashboard/details/create", { state: { name } });
        }}
      >
        {({ errors, status, touched, isSubmitting, setFieldValue }) => {
          return (
            <Dialog
              open={open}
              onClose={handleClose}
              fullWidth
              onClick={(event) => {
                if (event.target === event.currentTarget) {
                  handleClose();
                }
              }}
              sx={{
                "& .MuiDialog-container": {
                  "& .MuiPaper-root": {
                    width: "100%",
                    maxWidth: "450px", // Set your width here

                    margin: 0,
                  },
                },
              }}
            >
              <Form>
                <div className="mx-4  mb-5">
                  <div className=" mt-4 text-black text-base font-medium flex items-center justify-between">
                    {"+ Add Dashboard"}
                    <CloseIcon onClick={handleClose} className=" w-2.5 h-2.5" />
                  </div>

                  <div className="mt-3  mb-3 border-b border-panelBorder" />
                  <div className="w-full mt-4">
                    <InputLabel label={"Dashboard Name"} isMandatory={true} />
                    <TextFieldWrapper
                      name="name"
                      placeholder={"Enter Dashboard name"}
                    />
                  </div>
                  <div>
                    <div className="text-center mt-10 gap-5 mb-2">
                      <CancelButton
                        onClick={() => {
                          setFieldValue("name", "");
                          handleClose();
                        }}
                        label={"Cancel"}
                        buttonClassName="w-[100px] h-9 text-xs  "
                      ></CancelButton>
                      <Button
                        type="submit"
                        label={"Next >"}
                        buttonClassName="w-[100px] h-9 text-xs ml-5"
                      ></Button>
                    </div>
                  </div>
                </div>
              </Form>
            </Dialog>
          );
        }}
      </Formik>
    </Box>
  );
}
