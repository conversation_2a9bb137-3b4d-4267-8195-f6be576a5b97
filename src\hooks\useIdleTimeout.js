import { useState, useEffect, useRef, useCallback } from "react";
import {
  IDLE_TIMEOUT_DURATION,
  IDLE_WARNING_DURATION,
} from "../common/constants";

const useIdleTimeout = (onLogout, onWarning) => {
  const [isIdle, setIsIdle] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const [warningCountdown, setWarningCountdown] = useState(
    IDLE_WARNING_DURATION
  );

  const timeoutRef = useRef();
  const warningTimeoutRef = useRef();
  const countdownIntervalRef = useRef();

  const resetTimeout = useCallback(() => {
    // Clear any existing timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
    }

    // Reset states
    setIsIdle(false);
    setShowWarning(false);
    setWarningCountdown(IDLE_WARNING_DURATION);

    // Set warning timeout (IDLE_TIMEOUT_DURATION - IDLE_WARNING_DURATION seconds)
    const warningTime = (IDLE_TIMEOUT_DURATION - IDLE_WARNING_DURATION) * 1000;
    warningTimeoutRef.current = setTimeout(() => {
      setShowWarning(true);
      onWarning && onWarning();

      // Start countdown
      let countdown = IDLE_WARNING_DURATION;
      setWarningCountdown(countdown);

      countdownIntervalRef.current = setInterval(() => {
        countdown -= 1;
        setWarningCountdown(countdown);

        if (countdown <= 0) {
          clearInterval(countdownIntervalRef.current);
        }
      }, 1000);
    }, warningTime);

    // Set logout timeout
    timeoutRef.current = setTimeout(() => {
      setIsIdle(true);
      setShowWarning(false);
      onLogout && onLogout();
    }, IDLE_TIMEOUT_DURATION * 1000);
  }, [onLogout, onWarning]);

  const stayLoggedIn = useCallback(() => {
    resetTimeout();
  }, [resetTimeout]);

  useEffect(() => {
    const events = [
      "mousedown",
      "mousemove",
      "keypress",
      "scroll",
      "touchstart",
      "click",
    ];

    // Add event listeners for user activity
    const handleActivity = () => {
      if (!showWarning) {
        resetTimeout();
      }
    };

    events.forEach((event) => {
      document.addEventListener(event, handleActivity, true);
    });

    // Start the initial timeout
    resetTimeout();

    return () => {
      // Cleanup
      events.forEach((event) => {
        document.removeEventListener(event, handleActivity, true);
      });

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
      }
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, [resetTimeout, showWarning]);

  return {
    isIdle,
    showWarning,
    warningCountdown,
    stayLoggedIn,
    resetTimeout,
  };
};

export default useIdleTimeout;
