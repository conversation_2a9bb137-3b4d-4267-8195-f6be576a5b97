import React, { useMemo, useCallback, useState } from "react";
import {
  MaterialReactTable,
  useMaterialReactTable,
} from "material-react-table";
import { Box, Typography } from "@mui/material";
import { SearchIcon } from "../icons";
import InputAdornment from "@mui/material/InputAdornment";
import styled from "styled-components";
import theme from "../tailwind-theme";
import { useMutation } from "react-query";
import { searchCDR } from "../services/cdrsearch-api";

const TableWrapper = styled.div`
  border: 1px solid #d1d5db;
  border-radius: 0.3rem;

  .MuiTableCell-head {
    background-color: ${theme.backgroundColor.bgTable} !important;
    color: #000000 !important;
    position: sticky;
    top: 0;
    z-index: 1;
    font-weight: 500px !important;
    white-space: nowrap !important;
  }

  .mrt-row-expand-button {
    width: 0;
    height: 0;
    border-style: solid;
    background-color: transparent !important;
    box-shadow: none !important;
    min-width: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  .mrt-row-expand-button.MuiIconButton-root:not(.Mui-expanded) {
    border-width: 6px 0 6px 10px;
    border-color: transparent transparent transparent #5b9bd5;
  }

  .mrt-row-expand-button.MuiIconButton-root.Mui-expanded {
    border-width: 10px 6px 0 6px;
    border-color: #5b9bd5 transparent transparent transparent;
  }

  .mrt-row-expand-button svg {
    display: none !important;
  }

  .MuiTableCell-root.MuiTableCell-head[data-pinned="true"]:before {
    background-color: ${theme.backgroundColor.bgTable} !important;
  }

  .MuiTableHead-root {
    position: sticky;
    top: 0;
    z-index: 2;
  }

  .mrt-row-expand-header {
    display: none !important;
  }

  th.mrt-row-expand {
    font-size: 0 !important;
    color: transparent !important;
  }

  th.mrt-row-expand * {
    display: none !important;
  }

  th.mrt-row-expand::before,
  th.mrt-row-expand::after {
    content: none !important;
  }

  th.mrt-row-expand .MuiTableCell-head {
    display: none !important;
  }

  .mrt-row-expand,
  .mrt-row-expand-header {
    position: relative !important;
    left: auto !important;
    z-index: 1 !important;
  }

  .mrt-row-expand:after {
    display: none !important;
  }

  [data-pinned="true"] {
    position: relative !important;
    left: auto !important;
    right: auto !important;
  }

  .mrt-table-container [style*="position: sticky"] {
    position: relative !important;
  }
`;

const ExpandableTable = ({ columns, data, isLoading, filterData }) => {
  const [expandedRowId, setExpandedRowId] = useState(null);
  const [subColumn, setSubColumn] = useState([]);
  const [subData, setSubData] = useState([]);
  const safeData = useMemo(() => (Array.isArray(data) ? data : []), [data]);
  const safeColumns = useMemo(
    () => (Array.isArray(columns) ? columns : []),
    [columns]
  );
  const { mutate: searchCDRAPI, isLoading: isSearchLoading } =
    useMutation(searchCDR);

  const searchCDRAPICall = (eventId) => {
    const { limit, page, ...filteredDataWithoutLimitPage } = filterData;
    let reqObj = {
      ...filteredDataWithoutLimitPage,
      cdrType: "multiple",
      withinSingle: true,
      filters: {
        ...(filterData.filters || {}),
        event_id: eventId,
      },
    };

    searchCDRAPI(
      { reqObj },
      {
        onSuccess: (resp) => {
          if (resp?.data?.data?.length > 0) {
            let colsData = Object.keys(resp?.data?.data?.[0]).map((x) => ({
              header: x,
              accessorKey: x,
            }));
            setSubColumn(colsData);
          } else {
            setSubColumn([]);
          }

          setSubData(resp?.data?.data);
        },
        onError: (error) => {},
      }
    );
  };

  const handleExpandChange = useCallback(
    (updaterOrValue) => {
      let newExpandedState;
      if (typeof updaterOrValue === "function") {
        newExpandedState = updaterOrValue({});
      } else {
        newExpandedState = updaterOrValue;
      }

      const expandingRowIds = Object.keys(newExpandedState);

      if (expandingRowIds.length > 0) {
        const rowId = expandingRowIds[0];

        if (expandedRowId === rowId) {
          setExpandedRowId(null);
        } else {
          setExpandedRowId(rowId);
          const rowIndex = parseInt(rowId, 10);
          const expandedRow = safeData[rowIndex];
          if (expandedRow) {
            const messageIdValue = expandedRow["Message ID"];
            searchCDRAPICall(messageIdValue);
          }
        }
      } else {
        setExpandedRowId(null);
      }
    },
    [expandedRowId, safeData]
  );

  const renderDetailPanel = useCallback(() => {
    if (isSearchLoading) {
      return (
        <Box
          sx={{
            backgroundColor: "#f9f9f9",
            p: 2,
            borderRadius: "4px",
            margin: "0.5rem 1rem",
            border: "1px solid #e0e0e0",
          }}
        >
          <Typography variant="body1" fontStyle="italic">
            Loading details...
          </Typography>
        </Box>
      );
    }

    if (
      !subData ||
      subData.length === 0 ||
      !subColumn ||
      subColumn.length === 0
    ) {
      return (
        <Box
          sx={{
            backgroundColor: "#f9f9f9",
            p: 2,
            borderRadius: "4px",
            margin: "0.5rem 1rem",
            border: "1px solid #e0e0e0",
          }}
        >
          <Typography variant="body1" fontStyle="italic">
            No additional details available
          </Typography>
        </Box>
      );
    }

    return (
      <table className="md:w-[25%] sticky top-0 left-4 border-outerBorder">
        <thead>
          <tr>
            {subColumn.map((col) => (
              <th
                key={col.header}
                style={{
                  backgroundColor: "#e6f0ff",
                  padding: "16px",
                  textAlign: "left",
                  fontSize: "14px",
                }}
              >
                {col.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {subData.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {subColumn.map((col, colIndex) => (
                <td
                  key={`${rowIndex}-${col.header}`}
                  style={{
                    padding: "16px",
                    borderBottom: "1px solid #ddd",
                    background: "white",
                  }}
                >
                  {rowIndex === 0 ? (
                    <span
                      style={{
                        backgroundColor: "#FFCC80",
                        padding: "2px 6px",
                        borderRadius: "5px",
                        display: "inline-block",
                      }}
                    >
                      {row[col.accessorKey] !== undefined
                        ? row[col.accessorKey]
                        : "N/A"}
                    </span>
                  ) : row[col.accessorKey] !== undefined ? (
                    row[col.accessorKey]
                  ) : (
                    "N/A"
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    );
  }, [subColumn, subData, isSearchLoading]);

  const table = useMaterialReactTable({
    columns: safeColumns,
    data: safeData,
    enableColumnActions: false,
    enableTopToolbar: false,
    enableBottomToolbar: false,
    enableColumnPinning: false,
    enablePinning: false,
    enableSorting: true,
    enableStickyHeader: true,
    enableExpanding: true,
    enableExpandAll: false,
    enableMultiRowExpanding: false,
    enablePagination: false,
    enableFilters: true,
    enableGlobalFilter: false,
    enableColumnFilters: true,
    manualPagination: false,
    enableRowVirtualization: true,
    displayColumnDefOptions: {
      "mrt-row-expand": {
        header: "",
        size: 50,
        enablePinning: false,
      },
    },
    muiTableContainerProps: {
      sx: { maxHeight: "66vh" },
    },
    initialState: {
      showColumnFilters: true,
      columnPinning: { left: [], right: [] },
      expanded: {},
    },
    state: {
      isLoading: isLoading,
      expanded: expandedRowId ? { [expandedRowId]: true } : {},
    },
    onExpandedChange: handleExpandChange,
    muiTableHeadCellProps: {
      sx: {
        backgroundColor: "#374150",
        fontSize: "12px",
        color: "#ffffff",
        fontFamily: "OpenSanHebrew",
        "& .Mui-TableHeadCell-Content-Wrapper": {
          whiteSpace: "nowrap",
        },
      },
    },
    muiExpandButtonProps: {
      sx: {
        color: "#5E99C6",
      },
    },
    muiFilterTextFieldProps: {
      placeholder: "Search",
      InputProps: {
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon />
          </InputAdornment>
        ),
        style: {
          backgroundColor: "#ffffff",
          height: "30px",
          fontSize: 12,
          boxShadow: "0px 4px 4px 0px #0000001A",
        },
      },
      variant: "outlined",
      size: "small",
    },
    renderEmptyRowsFallback: useCallback(
      () => (
        <div
          style={{
            padding: "25px",
            fontSize: "16px",
            color: "#808080",
            fontStyle: "italic",
            textAlign: "center",
          }}
        >
          No records to display
        </div>
      ),
      []
    ),
    renderDetailPanel: renderDetailPanel,
  });

  return (
    <TableWrapper>
      <MaterialReactTable table={table} />
    </TableWrapper>
  );
};

export default React.memo(ExpandableTable);
