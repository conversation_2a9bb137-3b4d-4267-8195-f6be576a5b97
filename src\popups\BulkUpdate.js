import React, { useEffect, useState } from "react";
import { Formik, Form, ErrorMessage } from "formik";
import Dialog from "@mui/material/Dialog";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Box from "@mui/material/Box";
import Button from "../components/Button/OutlinedButton";
import CancelButton from "../components/Button/Button";
import { CloseIcon } from "../icons";
import Select from "../components/FormsUI/Select";
import CustomDropDown from "../components/Dropdown/CustomeDropdown";
import { alertServices, getAlertNameList } from "../services/alert-api";
import { useMutation, useQuery } from "react-query";
import * as Yup from "yup";
import theme from "../tailwind-theme";

export default function BulkUpdate({
  closeDialog,
  openDialog,
  alertHistoryList,
  setBulkUpdate,
  onSuccessCallback,
  selectedFilter,
  searchInput = "",
}) {
  const [alertNameList, setAlertNameList] = useState([]);
  const [alertNameToIdsMap, setAlertNameToIdsMap] = useState({});
  const { mutate: updateAlertAPI, isLoading: upadteLoading } = useMutation(
    alertServices.handleStatusUpdate
  );
  const [message, setMessage] = useState("");

  const statusOptions = [
    { label: "Open", value: "open" },
    { label: "Analyzing", value: "analyzing" },
    { label: "Fixed", value: "fixed" },
    { label: "Closed", value: "closed" },
  ];

  const startDate = selectedFilter?.startDate;
  const endDate = selectedFilter?.endDate;

  useQuery(
    ["/alertNameList", startDate, endDate, searchInput],
    getAlertNameList,
    {
      refetchOnWindowFocus: false,
      onSuccess: ({ data }) => {
        const alertNames = data.map((element) => ({
          value: element?.value,
          label: element?.label,
        }));
        setAlertNameList(alertNames);
      },
    }
  );

  useEffect(() => {
    if (Array.isArray(alertNameList) && alertNameList.length > 0) {
      const nameToIds = {};

      alertNameList.forEach(({ value, label }) => {
        if (!nameToIds[label]) {
          nameToIds[label] = [];
        }
        value.split(",").forEach((id) => {
          nameToIds[label].push(id.trim());
        });
      });
      setAlertNameToIdsMap(nameToIds);
    }
  }, [alertNameList]);

  const validationSchema = Yup.object().shape({
    alertName: Yup.array()
      .min(1, "Alert Name is required")
      .required("Alert Name is required"),
    status: Yup.string()
      .min(1, "Status is required")
      .required("Status is required"),
  });

  return (
    <Box width={"100%"}>
      <Formik
        initialValues={{
          alertName: [],
          status: "",
        }}
        validationSchema={validationSchema}
        enableReinitialize={true}
        onSubmit={(values) => {
          const idToLabelMap = alertNameList.reduce((acc, { value, label }) => {
            acc[value] = label;
            return acc;
          }, {});

          const allSelectedIds = values.alertName
            .filter((id) => id !== "Select All")
            .map((id) => alertNameToIdsMap[idToLabelMap[id]])
            .filter(Boolean)
            .flat()
            .map((id) => parseInt(id, 10));

          updateAlertAPI(
            {
              status: values?.status,
              id: allSelectedIds,
            },
            {
              onSuccess: (data) => {
                setBulkUpdate(true);
                setMessage("Status Updated Successfully");
                if (typeof onSuccessCallback === "function") {
                  onSuccessCallback();
                }
                closeDialog();
              },

              onError: (error) => {
                setMessage(error);
                closeDialog();
              },
            }
          );
        }}
      >
        {({ setFieldValue, resetForm }) => {
          return (
            <>
              <Dialog
                open={openDialog}
                onClose={() => {
                  closeDialog();
                  resetForm();
                }}
                fullWidth
                onClick={(event) => {
                  if (event.target === event.currentTarget) {
                    closeDialog();
                    resetForm();
                  }
                }}
                sx={{
                  "& .MuiDialog-container": {
                    "& .MuiPaper-root": {
                      width: "100%",
                      maxWidth: "450px",
                      margin: 0,
                    },
                  },
                }}
              >
                <Form>
                  <div className="mx-4  mb-5">
                    <div className=" mt-4 text-black text-base font-medium flex items-center justify-between">
                      {"Bulk Update"}
                      <CloseIcon
                        onClick={() => {
                          closeDialog();
                          resetForm();
                        }}
                        cursor={"pointer"}
                        className=" w-2.5 h-2.5"
                      />
                    </div>

                    <div className="mt-2  mb-3 border-b border-panelBorder" />

                    <div className="w-full  mt-4">
                      <InputLabel label={"Alert Name"} isMandatory={true} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={alertNameList || []}
                        btnName={"Select Alert Name"}
                        onSelectionChange={(selectedDetails) => {
                          setFieldValue("alertName", selectedDetails);
                        }}
                        isMultiSelect={true}
                        isUser={true}
                      />
                      <p
                        style={{
                          fontSize: "12px",
                          color: theme.textColor.errorColor,
                        }}
                      >
                        <ErrorMessage name="alertName" />
                      </p>
                    </div>
                    <div className="w-full  mt-4">
                      <InputLabel label={"Status"} isMandatory={true} />
                      <Select
                        name="status"
                        options={statusOptions || []}
                        placeholder={"Select Status"}
                        isSearchable={true}
                        menuListzIndex={9999}
                      />
                    </div>
                    <div>
                      <div className="text-center mt-10 gap-5 mb-2">
                        <CancelButton
                          onClick={() => {
                            setFieldValue("name", "");
                            closeDialog();
                            resetForm();
                          }}
                          label={"Cancel"}
                          buttonClassName="w-[100px] h-9 text-xs  "
                        ></CancelButton>
                        <Button
                          type="submit"
                          label={"Update"}
                          buttonClassName="w-[100px] h-9 text-xs ml-5"
                        ></Button>
                      </div>
                    </div>
                  </div>
                </Form>
              </Dialog>
            </>
          );
        }}
      </Formik>
    </Box>
  );
}
