import React, { useContext, useRef, useState } from "react";
import { Formik, Form, Field } from "formik";
import CustomDropDown from "../Dropdown/ReportsDropdownList";
import InputLabel from "../FormsUI/InputLabel/InputLabel";
import Button from "../Button/Button";
import OutlinedButton from "../Button/OutlinedButton";
import TextField from "../FormsUI/TextField";
import { MetaDataContext } from "../../context/MetaDataContext";
import { DataContext } from "../../context/DataContext";
import * as Yup from "yup";
import { filterLabelMap } from "../../common/constants";
import { useQuery } from "react-query";
import { reportService } from "../../services/staticreport.service";
import Calendar from "../DatePicker/Calendar";
import dayjs from "dayjs";
import { AuthContext } from "../../context/AuthContext";

export default function StaticCollapsibleFilter({
  setFilters,
  filterData,
  setLabelData,
  refetch,
  setSelectedFilter,
  selectedFilter,
  setIsApplied,
}) {
  const formikRef = useRef(null);
  const { setBilateralData } = useContext(DataContext);
  const { user } = useContext(AuthContext);
  const [isExpanded, setIsExpanded] = useState(false);
  const [availableFilters, setAvailableFilters] = useState([]);
  const [otherFilter, setOtherFilter] = useState([]);

  const [selectedRange, setSelectedRange] = useState("Today");

  const {
    customers,
    customerBind,
    suppliers,
    supplierBind,
    destinationNameList,
    destinationCountryList,
    lcrDataList,
    cdrStatus,
    customerProtocol,
    supplierProtocol,
    supplierBillingLogic,
    customerBillingLogic,
    specLCRDataList,
    customerInterfaceType,
    supplierInterfaceType,
    sourcePrime,
    destinationPrime,
    customerAirtelKamList,
    supplierAirtelKamList,
    customerInterconnect,
    supplierInterconnect,
  } = useContext(MetaDataContext);

  const initialValues = {
    customer_name: [],
    customer_bind: [],
    src_prime: [],
    destination: [],
    dest_prime: [],
    supplier: [],
    supplier_bind: [],
    destination_operator_name: [],
    destination_country_name: [],
    customer_interface_type: [],
    supplier_interface_type: [],
    customer_billing_logic: [],
    supplier_billing_logic: [],
    traffic_type_customer: [],
    traffic_type_supplier: [],
    customer_interconnect: [],
    supplier_interconnect: [],
    destination_mcc_final: filterData?.destination_mcc_final ?? "",
    destination_mnc_final: filterData?.destination_mnc_final ?? "",
    lcr_name: [],
    spec_lcr: [],
    status: [],
    customer_kam: [],
    supplier_kam: [],
    roamingDirectStatus: "",
    negative_report: filterData?.negative_report || false,
    bilateral: filterData?.bilateral || false,
    b_number: filterData?.b_number || "",
  };
  const customerOptions = [
    { value: " ", label: "Empty String" },
    ...(customerAirtelKamList || []),
  ];
  const supplierOptions = [
    { value: " ", label: "Empty String" },
    ...(supplierAirtelKamList || []),
  ];

  const toggleTable = () => setIsExpanded((prev) => !prev);

  const handleSubmit = (values) => {
    const allFilters = [...availableFilters, ...otherFilter];

    const normalizedValues = {
      ...values,
      ...(values.destination_mcc_final && {
        destination_mcc_final: parseInt(values.destination_mcc_final, 10),
      }),
      ...(values.destination_mnc_final && {
        destination_mnc_final: parseInt(values.destination_mnc_final, 10),
      }),
    };

    // Remove "Select All" from arrays
    Object.keys(normalizedValues).forEach((key) => {
      if (Array.isArray(normalizedValues[key])) {
        normalizedValues[key] = normalizedValues[key].filter(
          (item) => item !== "Select All"
        );
      }
    });

    const filteredValues = Object.keys(normalizedValues)
      .filter((key) => {
        const value = normalizedValues[key];
        return (
          allFilters.includes(key) &&
          value !== null &&
          value !== undefined &&
          value !== "" &&
          value !== false &&
          (!Array.isArray(value) || value.length > 0)
        );
      })
      .reduce((obj, key) => {
        obj[key] = normalizedValues[key];
        return obj;
      }, {});

    const appliedLabels = [];

    // Handle roaming/direct status condition
    if (
      otherFilter.includes("only_roaming") ||
      otherFilter.includes("only_direct") ||
      otherFilter.includes("both")
    ) {
      const statusList = values.roamingDirectStatus || []; // use original values here to check Select All

      if (statusList.length > 0) {
        appliedLabels.push(filterLabelMap["roamingDirectStatus"]);
      }

      if (statusList.includes("only_roaming")) {
        filteredValues.only_roaming = true;
      }
      if (statusList.includes("only_direct")) {
        filteredValues.only_direct = true;
      }
      if (statusList.includes("both")) {
        filteredValues.both = true;
      }
    }

    appliedLabels.push(
      ...Object.keys(filteredValues)
        .filter((key) => filterLabelMap[key])
        .map((key) => filterLabelMap[key])
    );

    setFilters(filteredValues);
    setIsApplied(true);
    setLabelData(appliedLabels);
  };

  const validation = Yup.object().shape({
    destination_mcc_final: Yup.string()
      .matches(/^[\d]+$/, "Destination MCC must be a number")
      .max(3, "Destination MCC must be  between 1 to 3 digits")
      .test(
        "not-zero",
        "Destination MCC cannot be 000",
        (value) => value !== "000"
      ),
    destination_mnc_final: Yup.string()
      .matches(/^[\d]+$/, "Destination MNC must be a number")
      .max(3, "Destination MNC must be  between 1 to 3 digits"),
    customer_name: Yup.array().when([], {
      is: () => shouldShowFilter("customer_name") && !user?.isSuperAdmin,
      then: (schema) => schema.min(1, "Customer Name is required"),
      otherwise: (schema) => schema,
    }),
    supplier: Yup.array().when([], {
      is: () => shouldShowFilter("supplier") && !user?.isSuperAdmin,
      then: (schema) => schema.min(1, "Supplier is required"),
      otherwise: (schema) => schema,
    }),
  });

  // ChevronIcon component similar to CDRSearch.js
  const ChevronIcon = ({ direction = "down" }) => {
    return direction === "down" ? (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fillRule="evenodd"
          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
          clipRule="evenodd"
        />
      </svg>
    ) : (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fillRule="evenodd"
          d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
          clipRule="evenodd"
        />
      </svg>
    );
  };

  useQuery(["reportFilter", "Dynamic Report"], reportService.getReportFilter, {
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      setAvailableFilters(data?.filters || []);
      setOtherFilter(data?.otherFilters || []);
    },
    onError: () => {},
  });

  const shouldShowFilter = (filterName) => {
    return availableFilters.includes(filterName);
  };

  return (
    <div className="mb-2 ">
      <div
        onClick={toggleTable}
        className="cursor-pointer px-1 py-1 font-semibold text-sm flex items-center gap-1 underline"
      >
        <span>Select filters</span>
        <ChevronIcon direction={isExpanded ? "up" : "down"} />
      </div>

      {isExpanded && (
        <div className="bg-white py-5 px-6">
          <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            innerRef={formikRef}
            // enableReinitialize={true}
            validationSchema={validation}
          >
            {({ values, isValid, setFieldValue, errors, touched }) => (
              <Form>
                <div className="flex gap-4 justify-end mb-3">
                  <Calendar
                    setSelectedFilter={setSelectedFilter}
                    setSelectedRange={setSelectedRange}
                    selectedRange={selectedRange}
                    selectedFilter={selectedFilter}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-2">
                  {/* Customer Name */}
                  {shouldShowFilter("customer_name") && (
                    <div>
                      <InputLabel
                        label={"Customer Name"}
                        isMandatory={!user?.isSuperAdmin}
                      />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customers || []}
                        btnName={"Select Customer Name"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("customer_name", selectedDetail);
                        }}
                        value={values.customer_name}
                        defaultSelectedData={filterData?.customer_name || []}
                      />
                      {errors.customer_name && touched.customer_name && (
                        <div className="text-red-500 text-xs">
                          {errors.customer_name}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Customer Bind */}
                  {shouldShowFilter("customer_bind") && (
                    <div>
                      <InputLabel label={"Customer Bind"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerBind || []}
                        btnName={"Select Customer Bind"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("customer_bind", selectedDetail);
                        }}
                        value={values.customer_bind}
                        defaultSelectedData={filterData?.customer_bind || []}
                      />
                    </div>
                  )}

                  {/* Source Prime */}
                  {shouldShowFilter("src_prime") && (
                    <div>
                      <InputLabel label={"Source Prime"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={sourcePrime || []}
                        btnName={"Select Source Prime"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("src_prime", selectedDetail);
                        }}
                        value={values.src_prime}
                        defaultSelectedData={filterData?.src_prime || []}
                      />
                    </div>
                  )}

                  {/* Destination Prime */}
                  {shouldShowFilter("dest_prime") && (
                    <div>
                      <InputLabel label={"Destination Prime"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={destinationPrime || []}
                        btnName={"Select Destination Prime"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("dest_prime", selectedDetail);
                        }}
                        value={values.dest_prime}
                        defaultSelectedData={filterData?.dest_prime || []}
                      />
                    </div>
                  )}

                  {/* Supplier Name */}
                  {shouldShowFilter("supplier") && (
                    <div>
                      <InputLabel
                        label={"Supplier Name"}
                        isMandatory={!user?.isSuperAdmin}
                      />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={suppliers || []}
                        btnName={"Select Supplier Name"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("supplier", selectedDetail);
                        }}
                        value={values.supplier}
                        defaultSelectedData={filterData?.supplier || []}
                      />
                      {errors.supplier && touched.supplier && (
                        <div className="text-red-500 text-xs">
                          {errors.supplier}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Supplier Bind */}
                  {shouldShowFilter("supplier_bind") && (
                    <div>
                      <InputLabel label={"Supplier Bind"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierBind || []}
                        btnName={"Select Supplier Bind"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("supplier_bind", selectedDetail);
                        }}
                        value={values.supplier_bind}
                        defaultSelectedData={filterData?.supplier_bind || []}
                      />
                    </div>
                  )}

                  {/* Destination Operator */}
                  {shouldShowFilter("destination_operator_name") && (
                    <div>
                      <InputLabel label={"Destination Operator"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={destinationNameList || []}
                        btnName={"Select Destination Operator"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "destination_operator_name",
                            selectedDetail
                          );
                        }}
                        value={values.destination_operator_name}
                        defaultSelectedData={
                          filterData?.destination_operator_name || []
                        }
                      />
                    </div>
                  )}

                  {/* Destination Operator */}
                  {shouldShowFilter("destination") && (
                    <div>
                      <InputLabel label={"Destination Operator"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={destinationNameList || []}
                        btnName={"Select Destination Operator"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("destination", selectedDetail);
                        }}
                        value={values.destination}
                        defaultSelectedData={filterData?.destination || []}
                      />
                    </div>
                  )}

                  {/* Destination Country */}
                  {shouldShowFilter("destination_country_name") && (
                    <div>
                      <InputLabel label={"Destination Country"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={destinationCountryList || []}
                        btnName={"Select Destination Country"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "destination_country_name",
                            selectedDetail
                          );
                        }}
                        value={values.destination_country_name}
                        defaultSelectedData={
                          filterData?.destination_country_name || []
                        }
                      />
                    </div>
                  )}

                  {/* Customer Interface Type */}
                  {shouldShowFilter("customer_interface_type") && (
                    <div>
                      <InputLabel label={"Customer Interface"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerInterfaceType || []}
                        btnName={"Select Customer Interface"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "customer_interface_type",
                            selectedDetail
                          );
                        }}
                        value={values.customer_interface_type}
                        defaultSelectedData={
                          filterData?.customer_interface_type || []
                        }
                      />
                    </div>
                  )}

                  {/* Supplier Interface Type */}
                  {shouldShowFilter("supplier_interface_type") && (
                    <div>
                      <InputLabel label={"Supplier Interface"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierInterfaceType || []}
                        btnName={"Select Supplier Interface"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "supplier_interface_type",
                            selectedDetail
                          );
                        }}
                        value={values.supplier_interface_type}
                        defaultSelectedData={
                          filterData?.supplier_interface_type || []
                        }
                      />
                    </div>
                  )}

                  {/* Customer Billing Logic */}
                  {shouldShowFilter("customer_billing_logic") && (
                    <div>
                      <InputLabel label={"Customer Billing Logic"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerBillingLogic || []}
                        btnName={"Select Customer Billing Logic"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "customer_billing_logic",
                            selectedDetail
                          );
                        }}
                        value={values.customer_billing_logic}
                        defaultSelectedData={
                          filterData?.customer_billing_logic || []
                        }
                      />
                    </div>
                  )}

                  {/* Supplier Billing Logic */}
                  {shouldShowFilter("supplier_billing_logic") && (
                    <div>
                      <InputLabel label={"Supplier Billing Logic"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierBillingLogic || []}
                        btnName={"Select Supplier Billing Logic"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "supplier_billing_logic",
                            selectedDetail
                          );
                        }}
                        value={values.supplier_billing_logic}
                        defaultSelectedData={
                          filterData?.supplier_billing_logic || []
                        }
                      />
                    </div>
                  )}

                  {/* Customer Protocol */}
                  {shouldShowFilter("traffic_type_customer") && (
                    <div>
                      <InputLabel label={"Customer Traffic Type"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerProtocol || []}
                        btnName={"Select Customer Traffic Type"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "traffic_type_customer",
                            selectedDetail
                          );
                        }}
                        value={values.traffic_type_customer}
                        defaultSelectedData={
                          filterData?.traffic_type_customer || []
                        }
                      />
                    </div>
                  )}

                  {/* Supplier Protocol */}
                  {shouldShowFilter("traffic_type_supplier") && (
                    <div>
                      <InputLabel label={"Supplier Traffic Type"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierProtocol || []}
                        btnName={"Select Supplier Traffic Type"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "traffic_type_supplier",
                            selectedDetail
                          );
                        }}
                        value={values.traffic_type_supplier}
                        defaultSelectedData={
                          filterData?.traffic_type_supplier || []
                        }
                      />
                    </div>
                  )}
                  {shouldShowFilter("customer_interconnect") && (
                    <div>
                      <InputLabel label={"Customer Interconnect"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerInterconnect || []}
                        btnName={"Select Customer Interconnect"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "customer_interconnect",
                            selectedDetail
                          );
                        }}
                        value={values.customer_interconnect}
                        defaultSelectedData={
                          filterData?.customer_interconnect || []
                        }
                      />
                    </div>
                  )}
                  {shouldShowFilter("supplier_interconnect") && (
                    <div>
                      <InputLabel label={"Supplier Interconnect"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierInterconnect || []}
                        btnName={"Select Supplier Interconnect"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "supplier_interconnect",
                            selectedDetail
                          );
                        }}
                        value={values.supplier_interconnect}
                        defaultSelectedData={
                          filterData?.supplier_interconnect || []
                        }
                      />
                    </div>
                  )}
                  {/* Destination MCC */}
                  {shouldShowFilter("destination_mcc_final") && (
                    <div>
                      <InputLabel label={"Destination MCC"} />
                      <TextField
                        name="destination_mcc_final"
                        placeholder={"Enter destination MCC"}
                      />
                    </div>
                  )}

                  {/* Destination MNC */}
                  {shouldShowFilter("destination_mnc_final") && (
                    <div>
                      <InputLabel label={"Destination MNC"} />
                      <TextField
                        name="destination_mnc_final"
                        placeholder={"Enter destination MNC"}
                      />
                    </div>
                  )}

                  {/* LCR Name */}
                  {shouldShowFilter("lcr_name") && (
                    <div>
                      <InputLabel label={"LCR Name"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={lcrDataList || []}
                        btnName={"Select LCR Name"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("lcr_name", selectedDetail);
                        }}
                        value={values.lcr_name}
                        defaultSelectedData={filterData?.lcr_name || []}
                      />
                    </div>
                  )}

                  {/* Spec LCR */}
                  {shouldShowFilter("spec_lcr") && (
                    <div>
                      <InputLabel label={"Spec LCR"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={specLCRDataList || []}
                        btnName={"Select Spec LCR"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("spec_lcr", selectedDetail);
                        }}
                        value={values.spec_lcr}
                        defaultSelectedData={filterData?.spec_lcr || []}
                      />
                    </div>
                  )}

                  {/* Status */}
                  {shouldShowFilter("status") && (
                    <div>
                      <InputLabel label={"Status"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={cdrStatus || []}
                        btnName={"Select Status"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("status", selectedDetail);
                        }}
                        value={values.status}
                        defaultSelectedData={filterData?.status || []}
                      />
                    </div>
                  )}
                  {shouldShowFilter("customer_kam") && (
                    <div>
                      <InputLabel label={"Customer KAM"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerOptions || []}
                        btnName="Select Customer KAM"
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("customer_kam", selectedDetail);
                        }}
                        value={values.customer_kam}
                        defaultSelectedData={filterData?.customer_kam || []}
                      />
                    </div>
                  )}
                  {shouldShowFilter("supplier_kam") && (
                    <div>
                      <InputLabel label={"Supplier KAM"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierOptions || []}
                        btnName="Select Supplier KAM"
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("supplier_kam", selectedDetail);
                        }}
                        value={values.supplier_kam}
                        defaultSelectedData={filterData?.supplier_kam || []}
                      />
                    </div>
                  )}
                  {shouldShowFilter("b_number") && (
                    <div>
                      <InputLabel label={"B Number"} />
                      <TextField
                        name="b_number"
                        placeholder={"Enter B Number"}
                      />
                    </div>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4  mt-5">
                  {(otherFilter.includes("only_roaming") ||
                    otherFilter.includes("only_direct") ||
                    otherFilter.includes("both")) && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Roaming and Direct Status:
                      </label>
                      <div className="flex flex-col gap-2 mt-3">
                        {otherFilter.includes("only_roaming") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="roamingDirectStatus"
                              value="only_roaming"
                              className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Only Roaming
                            </span>
                          </label>
                        )}
                        {otherFilter.includes("only_direct") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="roamingDirectStatus"
                              value="only_direct"
                              className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Only Direct
                            </span>
                          </label>
                        )}
                        {otherFilter.includes("both") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="roamingDirectStatus"
                              value="both"
                              className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Both
                            </span>
                          </label>
                        )}
                      </div>
                    </div>
                  )}
                  {(otherFilter.includes("bilateral") ||
                    otherFilter.includes("negative_report")) && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Others:
                      </label>
                      <div className="flex flex-col gap-2 mt-2">
                        {otherFilter.includes("negative_report") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="checkbox"
                              name="negative_report"
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Negative Report Required
                            </span>
                          </label>
                        )}
                        {otherFilter.includes("bilateral") && (
                          <Field name="bilateral">
                            {({ field, form }) => (
                              <label className="inline-flex items-center">
                                <input
                                  type="checkbox"
                                  {...field}
                                  checked={field.value}
                                  onChange={(e) => {
                                    const checked = e.target.checked;
                                    form.setFieldValue("bilateral", checked);
                                    setBilateralData(checked);

                                    setFieldValue(
                                      "destination_operator_name",
                                      []
                                    );
                                    setFieldValue(
                                      "destination_country_name",
                                      []
                                    );
                                    setFieldValue("destination", []);
                                  }}
                                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">
                                  Include Bilateral
                                </span>
                              </label>
                            )}
                          </Field>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex gap-4 justify-end">
                  <Button
                    label="Clear"
                    type="button"
                    buttonClassName={"w-24"}
                    onClick={() => {
                      if (formikRef.current) {
                        setIsApplied(false);
                        formikRef.current.resetForm();
                        setFilters([]);
                        refetch();
                        setBilateralData(false);
                        setSelectedRange("Today");
                        setSelectedFilter({
                          startDate: dayjs()
                            .startOf("day")
                            .format("YYYY-MM-DD HH:mm:ss"),
                          endDate: dayjs().format("YYYY-MM-DD HH:mm:ss"),
                        });
                      }
                    }}
                  />
                  <OutlinedButton
                    label="Apply"
                    type="submit"
                    buttonClassName={"w-24"}
                    onClick={() => {
                      if (!isValid) return;

                      if (formikRef.current) {
                        formikRef.current.handleSubmit();
                        refetch();
                      }
                    }}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      )}
    </div>
  );
}
