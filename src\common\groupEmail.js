import React, { useEffect, useMemo, useState } from "react";
import { Field, useFormikContext } from "formik";
import ChipInput from "../components/Chip/ChipInput";
import CloseIcon from "@mui/icons-material/Close";
import SearchIcon from "@mui/icons-material/Search";
import AddButton from "../components/Button/Button";
import { DownArrowIcon, UpArrowIcon } from "../../src/icons";

const GroupEmailComponent = (props) => {
  const {
    groups,
    selectedName,
    setGroupMembers,
    newChipState,
    setNewChipState,
  } = props;
  const { setFieldValue, values } = useFormikContext();

  const selectedGroups = useMemo(() => {
    return groups.filter((group) => selectedName?.includes(group.label));
  }, [groups, selectedName]);
  useEffect(() => {
    const updatedMembers = selectedGroups.reduce((members, group) => {
      const groupEmails = [
        ...group.members,
        ...group.subGroups.flatMap((subGroup) => subGroup.members),
      ];
      return [...members, ...groupEmails];
    }, []);

    // setGroupMembers([...new Set(updatedMembers)]);

    const uniqueMembers = [...new Set(updatedMembers)];

    setGroupMembers((prev) => {
      const isSame =
        prev.length === uniqueMembers.length &&
        prev.every((m) => uniqueMembers.includes(m));
      return isSame ? prev : uniqueMembers;
    });
  }, [selectedGroups, setGroupMembers]);

  const [showNewMailRows, setShowNewMailRows] = useState(
    newChipState?.length > 0 ? false : true
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [occurrences, setOccurrences] = useState(0);
  const [showUpArrow, setShowUpArrow] = useState(true);

  const [visibleGroups, setVisibleGroups] = useState({});

  const toggleVisibility = (id) => {
    setVisibleGroups((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const addNewMailRow = () => {
    setShowNewMailRows(false);
  };

  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    setOccurrences(countOccurrences(query));
  };

  const countOccurrences = (query) => {
    if (!query) return 0;
    let count = 0;
    selectedGroups.forEach((group) => {
      count += group.members.filter((member) =>
        member.toLowerCase().includes(query.toLowerCase())
      ).length;
      group.subGroups?.forEach((subGroup) => {
        count += subGroup.members.filter((member) =>
          member.toLowerCase().includes(query.toLowerCase())
        ).length;
      });
    });
    // Include new chips in the count
    count += values.newChips.filter((chip) =>
      chip.toLowerCase().includes(query.toLowerCase())
    ).length;
    return count;
  };

  const highlightText = (text) => {
    if (!searchQuery) return text;
    const regex = new RegExp(`(${searchQuery})`, "gi");
    const parts = text.split(regex);
    return parts.map((part, index) =>
      part.toLowerCase() === searchQuery.toLowerCase() ? (
        <span key={index} style={{ backgroundColor: "#EDDF82" }}>
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  const clearSearch = () => {
    setSearchQuery("");
    setOccurrences(0);
  };

  const handleChipRemove = (index) => {
    const allChips = [...values.newChips];
    allChips.splice(index, 1);
    setFieldValue("newChips", allChips);
    setNewChipState((prev) => prev.filter((_, i) => i !== index));
  };

  const [error, setError] = React.useState("");

  const handleChipAdd = (chip) => {
    // Email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(chip)) {
      setError("Please enter a valid Email ID");
      return;
    }
    setError("");
    const updatedChips = [...values.newChips, chip];
    setFieldValue("newChips", updatedChips);

    setNewChipState((prev) => [...prev, chip]);
  };

  return (
    <div className="mx-8 my-6">
      <div className="mx-20">Selected Group Details</div>
      <div className="mx-20 rounded-md my-6 border-collapse border border-slate-400">
        <div className="mx-10 my-4 flex flex-row">
          <div
            className={`flex items-center border border-gray-300 px-2 py-2 rounded w-1/2 `}
          >
            <SearchIcon className="text-gray-400 mr-4 ml-1" />
            <input
              type="text"
              placeholder="Search Mail IDs"
              value={searchQuery}
              onChange={handleSearchChange}
              className="border-none outline-none w-full text-xs"
            />
          </div>

          {searchQuery && (
            <div className="relative bg-[#EDDF82] py-1 px-6 rounded-md flex items-center  mx-4">
              <span className="text-sm">{`${searchQuery} found in ${occurrences} place${
                occurrences > 1 ? "s" : ""
              }`}</span>
              <button
                onClick={clearSearch}
                className="absolute -top-1 right-0 mx-1 text-gray-600 hover:text-black"
              >
                <CloseIcon className="w-3 h-3 cursor-pointer" />
              </button>
            </div>
          )}
        </div>
        <div className="mx-8 my-6">
          <div className="table w-full border-collapse border border-slate-400 ">
            <div className="table-row  bg-[#9c9898] text-black">
              <div className="table-cell w-1/4 p-3 border border-slate-400 font-bold text-xm">
                Group Name
              </div>
              <div className="table-cell p-2 border border-slate-400 font-bold text-xm">
                Sub Group & Members
              </div>
            </div>

            {selectedGroups.map((group) => (
              <div key={group.label} className="table-row w-full">
                <div className="table-cell p-2 w-1/4 border border-slate-400 align-top bg-[#F3F8FF] text-gray-800">
                  <div className="bg-[#f8c7c7] rounded-md w-fit text-sm p-1">
                    {group.label}
                    <span className="text-titleColor px-1 text-xs">{` (${
                      group?.members?.length > 0
                        ? group.members.length
                        : group?.subGroups?.length > 0
                        ? group.subGroups.reduce(
                            (acc, x) => acc + (x.members?.length || 0),
                            0
                          )
                        : 0
                    } Email IDs)`}</span>{" "}
                  </div>
                </div>
                <div className="table-cell min-w-[50px] p-2 bg-[#F3F8FF] border border-slate-400">
                  {group.subGroups?.length > 0 ? (
                    <>
                      {group.members?.length > 0 && (
                        <div className="mb-3 pb-2 border-b border-dotted border-gray-400">
                          <div className="bg-[#d3f1cb] rounded w-fit p-1 font-medium flex items-center">
                            Members
                            <span className="text-xs px-1 text-titleColor">{`(${
                              group?.members?.length || 0
                            } Email IDs)`}</span>
                            <span
                              onClick={() => toggleVisibility(group.label)}
                              className="cursor-pointer ml-2"
                            >
                              {visibleGroups[group.label] ? (
                                <UpArrowIcon className="h-4 w-4" />
                              ) : (
                                <DownArrowIcon className="h-4 w-4" />
                              )}
                            </span>
                          </div>

                          {visibleGroups[group.label] && (
                            <div className="flex flex-row  flex-wrap  text-gray-800">
                              {group.members?.map((member, i) => (
                                <div
                                  key={i}
                                  className="bg-[#e0e0e0] w-fit mt-2 mb-1 mr-1 p-1 text-sm rounded"
                                >
                                  {highlightText(member)}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                      {group.subGroups?.map((subGroup, index) => (
                        <div
                          key={subGroup.id || index}
                          className="mb-2 pb-2 border-b border-dotted border-gray-400 last:border-0"
                        >
                          <div className="bg-[#d3f1cb] rounded-md w-fit p-1 font-medium flex items-center">
                            {subGroup.name}
                            <span className="text-titleColor px-1 text-xs">{` (${
                              subGroup.members?.length || 0
                            } Email IDs)`}</span>

                            <span
                              onClick={() =>
                                toggleVisibility(`${group.label}-${index}`)
                              }
                              className="cursor-pointer ml-2"
                            >
                              {visibleGroups[`${group.label}-${index}`] ? (
                                <UpArrowIcon className="h-4 w-4" />
                              ) : (
                                <DownArrowIcon className="h-4 w-4" />
                              )}
                            </span>
                          </div>

                          {visibleGroups[`${group.label}-${index}`] && (
                            <div className="flex flex-row flex-wrap  text-gray-800">
                              {subGroup.members?.map((member, i) => (
                                <div
                                  key={i}
                                  className="bg-[#e0e0e0] w-fit mt-2 mb-1 mr-1 p-1 text-sm rounded"
                                >
                                  {highlightText(member)}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </>
                  ) : (
                    <div>
                      <div className="bg-[#d3f1cb] rounded w-fit p-1 font-medium flex items-center">
                        Members
                        <span className="text-xs px-1 text-titleColor">{`(${
                          group?.members?.length || 0
                        } Email IDs)`}</span>
                        <span
                          onClick={() => toggleVisibility(group.label)}
                          className="cursor-pointer ml-2"
                        >
                          {visibleGroups[group.label] ? (
                            <UpArrowIcon className="h-4 w-4" />
                          ) : (
                            <DownArrowIcon className="h-4 w-4" />
                          )}
                        </span>
                      </div>

                      {visibleGroups[group.label] && (
                        <div className="flex flex-row flex-wrap  text-gray-800 text-sm">
                          {group.members?.map((member, i) => (
                            <div
                              key={i}
                              className="bg-[#e0e0e0] w-fit mt-2 mb-1 mr-1 p-1 text-sm rounded"
                            >
                              {highlightText(member)}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}

            {showNewMailRows === false ? (
              <div className="table-row">
                <div className="table-cell p-2 border border-slate-400 bg-[#F3F8FF]">
                  <div className="bg-[#f8c7c7] rounded w-fit p-1 text-[#1f2937] text-sm flex flex-row items-center gap-2 ">
                    New Email IDs
                    <CloseIcon
                      className="w-3 h-3 cursor-pointer"
                      onClick={() => {
                        setFieldValue("newChips", []);
                        setNewChipState([]);
                      }}
                    />
                  </div>
                </div>
                <div className="table-cell p-2 bg-[#F3F8FF]">
                  <Field name="newChips">
                    {({ field }) => (
                      <ChipInput
                        chips={field.value}
                        onChipAdd={(chip) => handleChipAdd(chip)}
                        onChipRemove={(index) => handleChipRemove(index)}
                        isEdit={true}
                        isNew={true}
                        searchQuery={searchQuery}
                      />
                    )}
                  </Field>
                  {<div className="text-xs text-errorColor">{error}</div>}
                </div>
              </div>
            ) : null}
          </div>
        </div>

        <div className="flex justify-end m-4">
          <AddButton
            label={"Add New Mail"}
            disabled={showNewMailRows === false}
            buttonClassName={`w-full md:w-[144px] text-xs mx-4 rounded ${
              showNewMailRows === false ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={() => addNewMailRow()}
          ></AddButton>
        </div>
      </div>
    </div>
  );
};

export default GroupEmailComponent;
