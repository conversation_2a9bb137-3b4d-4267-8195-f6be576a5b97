import React, {
  useMemo,
  useReducer,
  useEffect,
  useContext,
  useRef,
  useCallback,
} from "react";
import Pagination from "../components/Pagination/Pagination";
import { reportService } from "../services/staticreport.service";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import ExportPopup from "../popups/exportpopup";
import {
  MailBoxIcon,
  CloseIcon,
  SearchhIcon,
  InfoIcon,
  RefreshIcon,
  MenusIcon,
  FileRefreshIcon,
  ChartIcon,
  TableChartIcon,
} from "../icons";
import { AuthContext } from "../context/AuthContext";
import InfoModal from "../components/modals/InfoModal";
import { DataContext } from "../context/DataContext";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import Button from "../components/Button/OutlinedButton";
import SendMail from "../popups/SendMail";
import { CssTooltip } from "../components/StyledComponent";
import bgImage from "../assets/img/Records.png";
import { dynamicReports, views, filterLabelMap } from "../common/constants";
import ErrorDialog from "../popups/ErrorDialog";
import BreadcrumbNavigation from "../components/BreadCrumps/BreadCrump";
import { DownloadContext } from "../context/DownloadContext";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import theme from "../tailwind-theme";
import ReportTable from "../components/table/ReportTable";
import AggregationTable from "../components/table/AggregationTable";
import ReportFilter from "../components/CollapsibleFilter/ReportFilter";
import { MetaDataProvider } from "../context/MetaDataContext";
import SuccessDialog from "../popups/SuccessDialog";
import ReportCalendar from "../components/DatePicker/ReportCalendar";
import { NO_FILTER } from "../utils/constants";
import { billingReportRefresh } from "../services/cdrsearch-api";
import { useMutation, useQuery } from "react-query";
import {
  getExportPermissions,
  createExportTooltipContent,
} from "../utils/exportUtils";
import {
  generateReportFilename,
  getInitialFilterAndRange,
} from "../utils/dateRangeUtils";
import { REPORT_INFO_TOOLTIP } from "../common/constants";
import { getTimeRangeOptions } from "../utils/reportUtils";
import ColumnSelector from "../components/ColumnSelector/ColumnSelector";
import { getSelectedColumn } from "../services/staticreport.service";
import LoadingOverlay from "../components/LoadingOverlay";
import { generatePDFFromElement } from "../utils/pdfUtils";
import { exportDataToCSV } from "../utils/csvExportUtils";
import DynamicReportGraphView from "./DynamicReportGraphView";

// ---- Pure helpers: no component state used inside ----

// Helper function to check if filter value is valid
const isValidFilterValue = (value) =>
  value !== null &&
  value !== undefined &&
  value !== "" &&
  value !== false &&
  (!Array.isArray(value) || value.length > 0);
const buildReportPayload = ({
  mode, // 'table' | 'graph'
  data,
  limitPerPage,
  graphPageLimit,
  currentPage,
  searchStr,
  timeZone,
  selectedFilter,
  filters,
  graphFilters,
  noStartEndDate,
}) => {
  const payload = {
    reportName: data,
    download: 0,
    type: "",
    limit: mode === "table" ? limitPerPage : graphPageLimit,
    page: currentPage,
    search: searchStr,
    timezone: timeZone,
  };

  if (!noStartEndDate) {
    payload.startDate = selectedFilter.startDate;
    payload.endDate = selectedFilter.endDate;
  }

  if (["Daily", "Weekly", "Monthly"].includes(selectedFilter.duration)) {
    payload.defaultViewBy =
      selectedFilter.duration === "Daily"
        ? "day"
        : selectedFilter.duration === "Weekly"
        ? "week"
        : "month";
  }

  if (filters && filters.length !== 0) {
    payload.filters = filters;
  }
  if (mode === "graph") {
    payload.graphFilters = graphFilters;
    payload.isGraph = true;
  } else {
    payload.isGraph = false;
  }
  return payload;
};

const buildReportFilenameBase = ({ data, selectedFilter, filters }) => {
  const base = generateReportFilename(
    data,
    selectedFilter.startDate,
    selectedFilter.endDate
  );
  return base + (filters && filters.length !== 0 ? "_filters" : "");
};

const calcFtpPeriodDates = (type) => {
  if (type === "This Month") {
    const startDate = dayjs().startOf("month");
    const endDate = dayjs();
    return { startDate, endDate };
  }
  if (type === "Last Month") {
    const lastMonth = dayjs().subtract(1, "month");
    return {
      startDate: lastMonth.startOf("month"),
      endDate: lastMonth.endOf("month"),
    };
  }
  return {};
};

function StaticReports({ onClose }) {
  const location = useLocation();
  const { value: data, viewBy, activeTab, subtype } = location.state || {};
  const { resultPerPage } = useContext(DataContext);
  // Reducer setup for all component state to improve maintainability
  const initialStateFactory = () => {
    const initial = getInitialFilterAndRange(data, dynamicReports);
    return {
      limitPerPage: 100,
      currentPage: 1,
      totalCount: 0,
      details: [],
      filters: [],
      graphFilters: {},
      aggrgationDetail: [],
      showExportConfirmation: false,
      isLoading: true,
      isDownloadLoading: false,
      labelData: [],
      selectedFilter: initial.filter,
      selectedRange: initial.range,
      searchStr: "",
      reload: false,
      showAlertConfirmation: false,
      sendMailDialog: false,
      filterDialog: false,
      message: "",
      extensionType: "",
      errorDialog: false,
      successDialog: false,
      showMenuDropdown: false,
      showFtpOptions: false,
      ftpRefreshType: "",
      openDialog: false,
      allTableColumns: [],
      visibleColumns: {},
      derivedFields: [],
      activeView: "table",
      showGraph: false,
      payload: [],
      graphPreference: {},
    };
  };

  function reducer(state, action) {
    switch (action.type) {
      case "SET_FIELD":
        return { ...state, [action.field]: action.value };
      case "PATCH":
        return { ...state, ...action.payload };
      case "TOGGLE":
        return { ...state, [action.field]: !state[action.field] };
      case "RESET_FOR_VIEW_SWITCH":
        return {
          ...state,
          activeView: action.view,
          aggrgationDetail: [],
          currentPage: 1,
          searchStr: "",
          filters: [],
          graphFilters: {},
          labelData: [],
          details: [],
          showGraph: false,
        };
      default:
        return state;
    }
  }

  const [state, dispatch] = useReducer(reducer, null, initialStateFactory);

  const {
    limitPerPage,
    currentPage,
    totalCount,
    details,
    filters,
    graphFilters,
    aggrgationDetail,
    showExportConfirmation,
    isLoading,
    isDownloadLoading,
    labelData,
    selectedFilter,
    selectedRange,
    searchStr,
    reload,
    showAlertConfirmation,
    sendMailDialog,
    filterDialog,
    message,
    extensionType,
    errorDialog,
    successDialog,
    showMenuDropdown,
    showFtpOptions,
    ftpRefreshType,
    openDialog,
    allTableColumns,
    visibleColumns,
    derivedFields,
    activeView,
    showGraph,
    graphPreference,
  } = state;

  const menuRef = useRef(null);
  const searchTimeoutRef = useRef(null);
  const graphContainerRef = useRef(null);

  const navigate = useNavigate();

  dayjs.extend(customParseFormat);

  const { isDownloading, setIsDownloading } = useContext(DownloadContext);
  const { roles, configApiData, user } = useContext(AuthContext);

  const { mutate: staticReportAPI, isLoading: loadingData } = useMutation(
    reportService.getReport
  );

  const matchingReports = roles?.staticReports?.filter((report) => {
    return report.name === data;
  });
  const permission = user.isSuperAdmin
    ? 1
    : matchingReports[0]?.permissions?.download;

  const isAdmin = user.isSuperAdmin;

  function handlePageChange(page) {
    dispatch({ type: "SET_FIELD", field: "currentPage", value: page });
  }

  const getUrl = new URL(window.location.href);
  const timeZone = getUrl.search.slice(1);

  const exportPermissions = getExportPermissions(totalCount, configApiData);

  const handleLimitChange = (e) => {
    dispatch({
      type: "PATCH",
      payload: { currentPage: 1, limitPerPage: e?.target?.value },
    });
    if (e.target.value > 1000) {
      toast.warn("There might be delay in loading larger data.");
    }
  };

  const handleKeyUp = useCallback((event) => {
    const value = event.target.value;
    dispatch({ type: "PATCH", payload: { searchStr: value, currentPage: 1 } });

    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debouncing
    searchTimeoutRef.current = setTimeout(() => {
      dispatch({ type: "TOGGLE", field: "reload" });
    }, 500); // 500ms debounce delay
  }, []);

  // Fetch columns for ColumnSelector (only for Table Reports)
  const { data: columnConfigData, refetch } = useQuery(
    [
      "reportList",
      "static",
      data,
      // If you have a reportId, add it here (e.g., location.state?.id)
    ],
    getSelectedColumn,
    {
      enabled: !!data,
      refetchOnWindowFocus: false,
      onSuccess: ({ data: cfgData }) => {
        const { dataColumns, selectedColumns = [] } = cfgData || {};
        if (!dataColumns) return;
        const { tableFields = [], derivedFields = [] } = dataColumns;
        const combinedFields = [...tableFields, ...derivedFields];
        dispatch({
          type: "PATCH",
          payload: {
            derivedFields,
            allTableColumns: combinedFields,
            graphPreference: cfgData?.graphPreference,
          },
        });

        if (Object.keys(visibleColumns).length > 0) return;
        const initialVisibility = combinedFields.reduce((acc, field) => {
          // Always show date columns
          if (field.toLowerCase().includes("date")) {
            acc[field] = true;
          } else {
            acc[field] =
              selectedColumns.length > 0
                ? selectedColumns.includes(field)
                : true;
          }
          return acc;
        }, {});
        dispatch({
          type: "SET_FIELD",
          field: "visibleColumns",
          value: initialVisibility,
        });
      },
    }
  );

  useEffect(() => {
    if (graphPreference.filters && activeView === "graph") {
      console.log("graphPreference", graphPreference);

      // Process roaming status and generate applied labels
      const normalizedValues = graphPreference.filters;
      const appliedLabels = [];
      const statusList = normalizedValues.roamingDirectStatus || [];
      const statusMap = {
        only_roaming: "Only Roaming",
        only_direct: "Only Direct",
        both: "Roaming and Direct",
      };

      const hasRoamingDirect = Object.keys(statusMap).some((status) =>
        statusList.includes(status)
      );

      const filteredValues = {};

      if (hasRoamingDirect) {
        Object.entries(statusMap).forEach(([key, label]) => {
          if (statusList.includes(key)) {
            appliedLabels.push(label);
            filteredValues[key] = true;
          }
        });

        if (!hasRoamingDirect) {
          appliedLabels.push("Roaming and Direct");
          filteredValues.both = true;
        }
      }

      // Add other labels
      Object.keys(normalizedValues).forEach((key) => {
        if (
          key !== "roamingDirectStatus" &&
          !Object.keys(statusMap).includes(key) &&
          filterLabelMap[key] &&
          isValidFilterValue(normalizedValues[key])
        ) {
          appliedLabels.push(filterLabelMap[key]);
        }
      });

      // Set the applied labels
      dispatch({ type: "SET_FIELD", field: "labelData", value: appliedLabels });
    }
  }, [graphPreference, activeView]);

  // Single effect to fetch report data (pure payload builder + mutation)
  useEffect(() => {
    const isGraph = activeView === "graph";
    const shouldFetch =
      isGraph && graphFilters && Object.keys(graphFilters).length > 0;

    if (!shouldFetch && isGraph) {
      dispatch({ type: "SET_FIELD", field: "showGraph", value: false });
      return;
    } else {
      refetch();
    }

    const payloadBuilt = buildReportPayload({
      mode: activeView,
      data,
      limitPerPage,
      graphPageLimit: configApiData?.GRAPH_PAGE_LIMIT,
      currentPage,
      searchStr,
      timeZone,
      selectedFilter,
      filters,
      graphFilters,
      noStartEndDate: columnConfigData?.data?.noStartEndDate,
    });
    dispatch({ type: "SET_FIELD", field: "payload", value: payloadBuilt });

    staticReportAPI(
      { payload: payloadBuilt },
      {
        onSuccess: (res) => {
          let newData = res?.data?.data?.map((x) => ({ ...x }));
          dispatch({
            type: "PATCH",
            payload: {
              details: newData,
              aggrgationDetail: res.data.aggregateResponse,
              totalCount: res.data.totalCount,
            },
          });
          if (
            res.data.totalCount > configApiData?.GRAPH_PAGE_LIMIT &&
            isGraph
          ) {
            dispatch({
              type: "PATCH",
              payload: {
                showAlertConfirmation: true,
                message:
                  "Reframe from using huge data to be displayed in the graph.Selecting these many will result in a cluttered and unreadable graph. Please select optimal data.",
                showGraph: false,
              },
            });
          } else {
            dispatch({ type: "SET_FIELD", field: "showGraph", value: true });
          }
        },
        onError: () => {
          dispatch({
            type: "PATCH",
            payload: { details: [], aggrgationDetail: [], totalCount: 0 },
          });
        },
      }
    );
  }, [
    activeView,
    data,
    limitPerPage,
    configApiData?.GRAPH_PAGE_LIMIT,
    currentPage,
    searchStr,
    timeZone,
    selectedFilter,
    filters,
    reload,
    columnConfigData?.data?.noStartEndDate,
    staticReportAPI,
  ]);

  // Cleanup search timeout on component unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const exportReport = (type) => {
    dispatch({
      type: "PATCH",
      payload: { isLoading: true, isDownloadLoading: true },
    });

    const fileName = buildReportFilenameBase({ data, selectedFilter, filters });
    dispatch({ type: "SET_FIELD", field: "extensionType", value: type });

    if (totalCount < configApiData.INITIATE_OFFLINE_DOWNLOAD) {
      setIsDownloading(true);
    }

    if (type !== "") {
      reportService
        .downloadReport(
          data,
          "1",
          type,
          !columnConfigData?.data.noStartEndDate
            ? selectedFilter.startDate
            : undefined,
          !columnConfigData?.data.noStartEndDate
            ? selectedFilter.endDate
            : undefined,
          searchStr,
          timeZone,
          totalCount,
          filters,
          selectedFilter.duration,
          configApiData.INITIATE_OFFLINE_DOWNLOAD,
          fileName
        )
        .then((blob) => {
          dispatch({ type: "SET_FIELD", field: "isLoading", value: false });
          if (totalCount > configApiData.INITIATE_OFFLINE_DOWNLOAD) {
            dispatch({
              type: "PATCH",
              payload: { successDialog: true, message: blob.data.message },
            });
          } else {
            const url = URL.createObjectURL(blob.data);
            const link = document.createElement("a");
            const filename = fileName + ".zip";
            link.href = url;
            link.download = filename;
            link.click();
          }
        })
        .catch((error) => {
          dispatch({
            type: "PATCH",
            payload: {
              isDownloadLoading: false,
              errorDialog: true,
              message: "No record found",
            },
          });
        })
        .finally(() => {
          setIsDownloading(false);
          dispatch({
            type: "SET_FIELD",
            field: "isDownloadLoading",
            value: false,
          });
        });
    }
  };

  // Separate function for graph download as PDF
  const exportGraphAsPDF = () => {
    if (!graphContainerRef.current) {
      dispatch({
        type: "PATCH",
        payload: {
          errorDialog: true,
          message: "Graph not available for download",
        },
      });
      return;
    }

    // Check if there's data for the graph
    if (!details || details.length === 0) {
      dispatch({
        type: "PATCH",
        payload: {
          errorDialog: true,
          message: "No data available for graph download",
        },
      });
      return;
    }

    dispatch({ type: "SET_FIELD", field: "isDownloadLoading", value: true });
    setIsDownloading(true);

    const fileName =
      buildReportFilenameBase({ data, selectedFilter, filters }) + "_graph";

    // Use utility function for PDF generation
    generatePDFFromElement(
      graphContainerRef.current,
      fileName,
      `${data} - Graph Report`,
      () => {
        dispatch({
          type: "SET_FIELD",
          field: "isDownloadLoading",
          value: false,
        });
        setIsDownloading(false);
      },
      (error) => {
        dispatch({
          type: "PATCH",
          payload: {
            errorDialog: true,
            message: "Error generating PDF. Please try again.",
            isDownloadLoading: false,
          },
        });
        setIsDownloading(false);
      }
    );
  };

  // New function for graph data download as CSV
  const exportGraphDataAsCSV = () => {
    if (!details || details.length === 0) {
      dispatch({
        type: "PATCH",
        payload: {
          errorDialog: true,
          message: "No data available for CSV download",
        },
      });
      return;
    }

    const fileName =
      buildReportFilenameBase({ data, selectedFilter, filters }) +
      "_graph_data";

    exportDataToCSV({
      data: details,
      filename: fileName,
      options: {
        title: `Reports for ${selectedFilter.startDate} to ${selectedFilter.endDate}`,
      },
      onStart: () => {
        dispatch({
          type: "SET_FIELD",
          field: "isDownloadLoading",
          value: true,
        });
        setIsDownloading(true);
      },
      onSuccess: () => {
        dispatch({
          type: "SET_FIELD",
          field: "isDownloadLoading",
          value: false,
        });
        setIsDownloading(false);
      },
      onError: (errorMessage) => {
        dispatch({
          type: "PATCH",
          payload: {
            errorDialog: true,
            message: errorMessage || "Error generating CSV. Please try again.",
            isDownloadLoading: false,
          },
        });
        setIsDownloading(false);
      },
    });
  };

  // Filter columns for table rendering
  const columns = useMemo(() => {
    if (!details || details.length === 0) return [];

    const firstItem = details[0];
    const keys = Object.keys(firstItem);

    const dynamicColumns = keys.map((key) => ({
      id: key,
      accessorKey: key,
      header: key,
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => row.original[key],
    }));

    return dynamicColumns;
  }, [details]);

  const filteredKeys = Object.keys(filters).filter(
    (key) => key !== "durationTime"
  );
  const badgeCount = filteredKeys.length;

  const removeFilter = NO_FILTER.some((filter) => filter === data);

  const tooltipContent = createExportTooltipContent(configApiData);

  const { mutate: ftpRefreshAPI } = useMutation(billingReportRefresh);

  const handleFtpRefresh = (type) => {
    const { startDate, endDate } = calcFtpPeriodDates(type);
    if (!startDate || !endDate) return;

    const reqObj = {
      reportName: data,
      timezone: timeZone,
      startDate: startDate.format("YYYY-MM-DD HH:mm:ss"),
      endDate: endDate.format("YYYY-MM-DD HH:mm:ss"),
      isRefresh: true,
    };
    ftpRefreshAPI(
      { reqObj },
      {
        onSuccess: ({ data }) => {
          dispatch({
            type: "PATCH",
            payload: { successDialog: true, message: data?.data?.message },
          });
        },
        onError: () => {
          dispatch({
            type: "PATCH",
            payload: {
              errorDialog: true,
              message: "Something went wrong ! Try Again",
            },
          });
        },
      }
    );

    dispatch({
      type: "PATCH",
      payload: { showFtpOptions: false, showMenuDropdown: false },
    });
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        dispatch({
          type: "SET_FIELD",
          field: "showMenuDropdown",
          value: false,
        });
      }
    }

    if (showMenuDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMenuDropdown]);

  const handleTabView = (view) => {
    dispatch({ type: "RESET_FOR_VIEW_SWITCH", view: view.value });
    if (view.value === "graph" && graphPreference?.filters) {
      refetch();
      dispatch({
        type: "PATCH",
        payload: {
          filters: graphPreference.filters,
          graphFilters: graphPreference.graphFilters,
        },
      });
      dispatch({ type: "TOGGLE", field: "reload" });
    }
  };

  return (
    <>
      {/* Loading Overlay */}
      {loadingData && <LoadingOverlay message="Loading..." />}
      {/* Sticky Header with Breadcrumb */}
      <div className="sticky top-0 z-10 w-full bg-bgPrimary flex items-start text-headingColor text-2xl font-bold leading-tight">
        <BreadcrumbNavigation
          linkTwo="Static Reports"
          onlinkTwoClick={() =>
            navigate("/app/reports", {
              state: { tab: activeTab, subType: subtype },
            })
          }
          title={data}
        />
      </div>

      {/* Main Content Container */}
      <div className="bg-white p-3">
        {/* Close Icon Section */}
        <div className="flex justify-end items-center">
          <CloseIcon
            onClick={() =>
              navigate("/app/reports", {
                state: { tab: activeTab, subType: subtype },
              })
            }
            className="w-2 h-2 cursor-pointer mb-1"
          />
        </div>

        {/* Search and Controls */}

        <div className="mx-3 flex flex-wrap items-center justify-between gap-y-3">
          {/* Search Input */}
          {activeView === "table" ? (
            <div className="w-full md:w-[300px] relative">
              <input
                type="text"
                style={{
                  border: `1px solid ${theme.borderColor.outerBorder}`,
                  paddingLeft: "2.5rem",
                }}
                className="w-full text-tabColor bg-white rounded-md focus:outline-none text-sm h-10"
                placeholder="Search"
                value={searchStr}
                onChange={handleKeyUp}
              />
              <div className="absolute top-3 left-3">
                <SearchhIcon className="w-4 h-4" />
              </div>
            </div>
          ) : (
            <div className="w-full md:w-[300px] relative" />
          )}
          {/* Calendar, Info Tooltip, Mail, Filter, Refresh, Download */}
          <div className="flex items-center space-x-8">
            {/* Column Selector - Only show for Table Reports */}

            {!configApiData?.REPORTS_NOT_INCLUDES_EXPANDABLE_COLUMNS?.includes(
              data
            ) &&
              activeView === "table" && (
                <ColumnSelector
                  allFields={allTableColumns}
                  appliedFields={visibleColumns}
                  derivedFields={derivedFields}
                  onApply={(newVisibleColumns) => {
                    dispatch({
                      type: "SET_FIELD",
                      field: "visibleColumns",
                      value: newVisibleColumns,
                    });
                    // Force component re-render to ensure column ordering is updated
                    dispatch({ type: "TOGGLE", field: "reload" });
                  }}
                  onTriggerPreview={(columnSelection) => {
                    //  setSelectedColumn(columnSelection);
                  }}
                  reportName={data}
                  reportType={"static"}
                />
              )}

            {/* Menu Icon with Dropdown - Now positioned first */}
            <div className="relative" ref={menuRef}>
              <div
                className="rounded-full bg-bgouterBackground p-2 flex items-center justify-center cursor-pointer"
                onClick={() => {
                  dispatch({
                    type: "PATCH",
                    payload: {
                      showMenuDropdown: !showMenuDropdown,
                      showFtpOptions: false,
                      openDialog: false,
                    },
                  });
                }}
              >
                <CssTooltip title={"Report Menu"} placement="top" arrow>
                  <MenusIcon className="w-5 h-5" />
                </CssTooltip>
              </div>

              {/* Dropdown Menu */}
              {showMenuDropdown && (
                <div className="absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg z-10 border border-gray-200 p-3">
                  <div className="grid grid-cols-2 gap-2">
                    {/* Send Mail Option */}
                    <div
                      className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                      onClick={() => {
                        if (totalCount > 0) {
                          dispatch({
                            type: "PATCH",
                            payload: {
                              sendMailDialog: true,
                              showMenuDropdown: false,
                              showFtpOptions: false,
                            },
                          });
                        } else {
                          dispatch({
                            type: "PATCH",
                            payload: {
                              errorDialog: true,
                              message: "No data to send",
                            },
                          });
                        }
                      }}
                    >
                      <MailBoxIcon className="w-5 h-5" />
                      <span className="text-sm text-gray-700 ml-2 ">
                        Send Email
                      </span>
                    </div>

                    {/* Refresh Option (Only for Billing Tab) */}
                    {activeTab === 2 && subtype === "Billing" && (
                      <div
                        className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                        onClick={() => {
                          dispatch({ type: "TOGGLE", field: "reload" });
                          dispatch({
                            type: "PATCH",
                            payload: {
                              showMenuDropdown: false,
                              showFtpOptions: false,
                            },
                          });
                          toast.success("Refresh Applied...");
                        }}
                      >
                        <RefreshIcon className="w-5 h-5" />
                        <span className="text-sm text-gray-700 ml-2 truncate">
                          Refresh
                        </span>
                      </div>
                    )}

                    {/* FTP Refresh Option with Submenu */}
                    {configApiData?.MONTHLY_BILLING_REPORTS.includes(data) &&
                      isAdmin && (
                        <>
                          {" "}
                          <div
                            className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                            onClick={() =>
                              dispatch({
                                type: "SET_FIELD",
                                field: "showFtpOptions",
                                value: !showFtpOptions,
                              })
                            }
                          >
                            <FileRefreshIcon className="w-5 h-5" />
                            <span className="text-sm text-gray-700 ml-2 truncate">
                              FTP Refresh
                            </span>
                          </div>
                          {/* FTP Options Submenu with Radio Buttons */}
                          {showFtpOptions && (
                            <div className="absolute left-full top-16 bg-white rounded-md shadow-lg z-20 border border-gray-200 p-3 w-48">
                              <div className="text-sm font-medium mb-2">
                                Select Period:
                              </div>

                              <div className="flex items-center mb-2">
                                <input
                                  type="radio"
                                  id="thisMonth"
                                  name="ftpPeriod"
                                  value="This Month"
                                  checked={ftpRefreshType === "This Month"}
                                  onChange={() =>
                                    dispatch({
                                      type: "SET_FIELD",
                                      field: "ftpRefreshType",
                                      value: "This Month",
                                    })
                                  }
                                  className="mr-2 accent-black"
                                />
                                <label
                                  htmlFor="thisMonth"
                                  className="text-sm text-gray-700 cursor-pointer"
                                >
                                  This Month
                                </label>
                              </div>

                              <div className="flex items-center mb-3">
                                <input
                                  type="radio"
                                  id="lastMonth"
                                  name="ftpPeriod"
                                  value="Last Month"
                                  checked={ftpRefreshType === "Last Month"}
                                  onChange={() =>
                                    dispatch({
                                      type: "SET_FIELD",
                                      field: "ftpRefreshType",
                                      value: "Last Month",
                                    })
                                  }
                                  className="mr-2 accent-black"
                                />
                                <label
                                  htmlFor="lastMonth"
                                  className="text-sm text-gray-700 cursor-pointer"
                                >
                                  Last Month
                                </label>
                              </div>

                              <button
                                className="w-full py-1.5 bg-bgSecondary text-white text-sm rounded hover:bg-opacity-90"
                                onClick={() => {
                                  dispatch({
                                    type: "PATCH",
                                    payload: {
                                      showFtpOptions: false,
                                      showMenuDropdown: false,
                                    },
                                  });
                                  handleFtpRefresh(ftpRefreshType);
                                }}
                              >
                                Apply
                              </button>
                            </div>
                          )}
                        </>
                      )}
                  </div>
                </div>
              )}
            </div>

            {/* Info Tooltip */}

            {/* Calendar Picker - Disabled for weekly/monthly */}

            {!columnConfigData?.data?.noStartEndDate ? (
              <>
                <CssTooltip
                  title={
                    <div className="text-xs p-1">
                      {REPORT_INFO_TOOLTIP.map((text, idx) => (
                        <p className="mb-1.5" key={idx}>
                          {text}
                        </p>
                      ))}
                    </div>
                  }
                  placement="top"
                  arrow
                >
                  <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
                </CssTooltip>
                <div
                  className={`${
                    filters?.duration === "weekly" ||
                    filters?.duration === "monthly"
                      ? "pointer-events-none opacity-50"
                      : ""
                  }`}
                  onClick={() => {
                    dispatch({
                      type: "SET_FIELD",
                      field: "showMenuDropdown",
                      value: false,
                    });
                  }}
                >
                  <ReportCalendar
                    selectedFilter={selectedFilter}
                    setSelectedFilter={(value) => {
                      dispatch({
                        type: "PATCH",
                        payload: { selectedFilter: value, currentPage: 1 },
                      });
                    }}
                    setSelectedRange={(value) =>
                      dispatch({
                        type: "SET_FIELD",
                        field: "selectedRange",
                        value,
                      })
                    }
                    selectedRange={selectedRange}
                    reportTimeRange={getTimeRangeOptions(viewBy)}
                    viewBy={viewBy}
                    subtype={subtype}
                    openDialog={openDialog}
                    setOpenDialog={(value) =>
                      dispatch({
                        type: "SET_FIELD",
                        field: "openDialog",
                        value,
                      })
                    }
                    data={data}
                    isAdmin={isAdmin}
                  />
                </div>
              </>
            ) : null}

            {/* Download Button */}
            <CssTooltip
              title={
                activeView === "table"
                  ? tooltipContent
                  : "Download graph as PDF or export underlying data as CSV. For large datasets, please apply filters for better performance."
              }
              placement="top"
              arrow
              PopperProps={{
                modifiers: [
                  {
                    name: "preventOverflow",
                    options: {
                      altBoundary: true,
                    },
                  },
                ],
              }}
              enterNextDelay={100}
              enterDelay={100}
              leaveDelay={200}
              componentsProps={{
                popper: {
                  sx: {
                    opacity: 1,
                  },
                },
              }}
            >
              <span style={{ display: "inline-block" }}>
                {" "}
                <Button
                  buttonClassName="text-xs w-32 text-white h-10 rounded-md"
                  label="Download"
                  onClick={() => {
                    if (permission === 0) {
                      dispatch({
                        type: "PATCH",
                        payload: {
                          showAlertConfirmation: true,
                          message: "Download permission not allowed",
                        },
                      });
                    } else {
                      if (activeView === "graph") {
                        // Check if there's data and graph configuration before allowing download
                        if (!details || details.length === 0) {
                          dispatch({
                            type: "PATCH",
                            payload: {
                              showAlertConfirmation: true,
                              message: "No data available for graph download",
                            },
                          });
                          return;
                        }
                        if (!graphFilters?.visualizationType) {
                          dispatch({
                            type: "PATCH",
                            payload: {
                              showAlertConfirmation: true,
                              message:
                                "Please configure graph settings before downloading",
                            },
                          });
                          return;
                        }
                        // For graph view, show export options (PDF or CSV)
                        dispatch({
                          type: "SET_FIELD",
                          field: "showExportConfirmation",
                          value: true,
                        });
                      } else {
                        // For table view, show export options
                        dispatch({
                          type: "SET_FIELD",
                          field: "showExportConfirmation",
                          value: true,
                        });
                      }
                    }
                  }}
                  disabled={
                    isDownloading ||
                    isDownloadLoading ||
                    (activeView === "graph" &&
                      (!details ||
                        details.length === 0 ||
                        !graphFilters?.visualizationType))
                  }
                />
              </span>
            </CssTooltip>
          </div>
        </div>

        {/* Report Summary and Tables */}
        <div className="mx-3 mt-5">
          {/* Display Date Range if data exists */}
          {details.length > 0 && !columnConfigData?.data?.noStartEndDate && (
            <div className="mt-5 mx-1 flex items-center justify-between">
              <div className="text-sm text-black font-bold">
                Report from {selectedFilter?.startDate} to{" "}
                {selectedFilter?.endDate}
              </div>
            </div>
          )}
          {/* Aggregated Data Table */}
          {aggrgationDetail && Object.keys(aggrgationDetail).length > 0 && (
            <div className="mt-3">
              <AggregationTable aggregateResponse={aggrgationDetail} />
            </div>
          )}

          {/* ===== TABS START ===== */}

          <div className="mt-1 border-b border-gray-200 p-1">
            <nav className="-mb-px flex items-center" aria-label="Tabs">
              {/* Tabs */}
              {views
                .filter((view) => {
                  // Hide Graph view for reports in NO_FILTER array
                  if (view.value === "graph" && removeFilter) {
                    return false;
                  }
                  return true;
                })
                .map((view, index) => (
                  <button
                    key={view.value}
                    onClick={() => handleTabView(view)}
                    className={`${
                      index !== 0 ? "ml-8" : ""
                    } whitespace-nowrap py-1 px-1 border-b-2 font-medium text-sm ${
                      activeView === view.value
                        ? "border-[#DC3833] text-[#DC3833]"
                        : "border-transparent text-gray-500"
                    }`}
                  >
                    {view.label}
                  </button>
                ))}

              {/* Filters Button */}
              {removeFilter ? null : (
                <div className="ml-auto relative">
                  <CssTooltip
                    title={
                      labelData.length > 0 ? (
                        <div className="text-xs p-1">
                          {labelData.map((label, idx) => (
                            <div key={idx} className="mb-1">
                              {label}
                            </div>
                          ))}
                        </div>
                      ) : (
                        "No filters applied"
                      )
                    }
                    placement="top"
                    arrow
                  >
                    <button
                      className="whitespace-nowrap rounded-md border border-gray-300 px-4 py-2 font-medium text-sm text-gray-600 hover:border-[#DC3833] hover:text-[#DC3833] relative"
                      onClick={() => {
                        dispatch({
                          type: "PATCH",
                          payload: {
                            filterDialog: true,
                          },
                        });
                      }}
                    >
                      {activeView === "table" ? (
                        <span className="flex items-center gap-2">
                          <TableChartIcon className="w-4 h-4" />
                          Table Filter
                        </span>
                      ) : (
                        <span className="flex items-center gap-2">
                          <ChartIcon />
                          Chart Filter
                        </span>
                      )}
                      {badgeCount > 0 && (
                        <div className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full h-5 w-5 flex items-center justify-center text-xs font-medium">
                          {badgeCount}
                        </div>
                      )}
                    </button>
                  </CssTooltip>
                </div>
              )}
            </nav>
          </div>
          {/* ===== TABS END ===== */}
          {/* Report Table  */}

          {/* Show this content only when 'Table View' is active */}
          {activeView === "table" && (
            <>
              {/* Report Table  */}
              {details.length > 0 ? (
                <>
                  {/* Main Report Table */}
                  <div className="mt-5">
                    <ReportTable
                      columns={columns}
                      data={details}
                      //  isLoading={loadingData}
                    />
                  </div>

                  {/* Pagination and Result Count */}
                  <div className="flex items-center justify-between mt-5">
                    <div className="flex items-center">
                      <ResultPerPageComponent
                        countPerPage={resultPerPage}
                        limit={limitPerPage}
                        handleLimitChange={handleLimitChange}
                        pageName="reports"
                      />
                      <div className="text-sm pl-3 text-titleColor">
                        {(currentPage - 1) * limitPerPage + 1} -{" "}
                        {Math.min(limitPerPage * currentPage, totalCount)} of{" "}
                        {totalCount} rows
                      </div>
                    </div>

                    <Pagination
                      className="pagination-bar"
                      currentPage={currentPage}
                      totalCount={totalCount}
                      pageSize={limitPerPage}
                      onPageChange={handlePageChange}
                    />
                  </div>
                </>
              ) : (
                !loadingData && (
                  // Empty State UI
                  <div className="border border-outerBorder mb-5 mt-8">
                    <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                      Oops! No records to display.
                    </div>
                    <div className="flex justify-center my-10">
                      <img
                        src={bgImage}
                        className="h-[10%] w-[10%] object-cover"
                        alt="bg"
                      />
                    </div>
                  </div>
                )
              )}
            </>
          )}

          {/* Show this content only when 'Graph View' is active */}
          {activeView === "graph" ? (
            <div
              ref={graphContainerRef}
              className="mt-8 text-center text-gray-500 border border-outerBorder py-5"
            >
              <DynamicReportGraphView
                visualizationType={graphFilters?.visualizationType}
                data={details}
                config={graphFilters}
                totalCount={totalCount}
                configApiData={configApiData}
                onSetupClick={() => {
                  dispatch({
                    type: "PATCH",
                    payload: { filterDialog: true },
                  });
                }}
                showGraph={showGraph}
              />
            </div>
          ) : null}
        </div>
      </div>

      {/* Export Report Modal */}
      <ExportPopup
        show={showExportConfirmation}
        onHide={() =>
          dispatch({
            type: "SET_FIELD",
            field: "showExportConfirmation",
            value: false,
          })
        }
        onConfirm={(type) => {
          if (activeView === "graph") {
            // Handle graph exports
            if (type.includes("PDF")) {
              exportGraphAsPDF();
            }
            if (type.includes("CSV")) {
              exportGraphDataAsCSV();
            }
          } else {
            // Handle table exports
            exportReport(type);
          }
          dispatch({
            type: "SET_FIELD",
            field: "showExportConfirmation",
            value: false,
          });
        }}
        title={"Export Report"}
        identity={"Reports"}
        exportPermissions={
          activeView === "graph"
            ? { csv: true, excel: false, pdf: true } // Only show CSV and PDF for graphs
            : exportPermissions // Use normal permissions for tables
        }
      />

      {/* Alert Modal for Download Permission */}
      <InfoModal
        show={showAlertConfirmation}
        onHide={() => {
          dispatch({
            type: "SET_FIELD",
            field: "showAlertConfirmation",
            value: false,
          });
        }}
        message={message}
        graph={activeView === "graph" ? true : false}
        openGraphFilter={() => {
          dispatch({
            type: "PATCH",
            payload: { filterDialog: true },
          });
        }}
        applyFilter={() => {
          dispatch({ type: "SET_FIELD", field: "showGraph", value: true });
        }}
      />
      {/* Send Mail Dialog */}
      <SendMail
        openGroupDialog={sendMailDialog}
        closeGroupDialog={() => {
          dispatch({
            type: "SET_FIELD",
            field: "sendMailDialog",
            value: false,
          });
        }}
        selectedFilter={selectedFilter}
        searchStr={searchStr}
        type={extensionType}
        reportName={data}
        timeZone={timeZone}
        filters={filters}
        columnConfigData={columnConfigData?.data}
        graphFilters={graphFilters}
        graphValidation={activeView === "graph" ? true : false}
      />

      {/* Toast Notifications */}
      <ToastContainer position="top-center" autoClose={3000} />

      {/* Error and Success Dialogs */}
      <ErrorDialog
        show={errorDialog}
        onHide={() =>
          dispatch({ type: "SET_FIELD", field: "errorDialog", value: false })
        }
        message={message}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() =>
          dispatch({ type: "SET_FIELD", field: "successDialog", value: false })
        }
        message={message}
      />

      {/* Conditional Filter Dialog */}
      {filterDialog && (
        <MetaDataProvider>
          <ReportFilter
            openFilterDialog={filterDialog}
            activeView={activeView}
            closeFilterDialog={() => {
              dispatch({
                type: "SET_FIELD",
                field: "filterDialog",
                value: false,
              });
            }}
            reportName={data}
            setFilters={(value) =>
              dispatch({ type: "SET_FIELD", field: "filters", value })
            }
            setgraphFilters={(value) =>
              dispatch({ type: "SET_FIELD", field: "graphFilters", value })
            }
            isLoading={isLoading}
            filterData={{ filters, graphFilters }}
            setLabelData={(value) =>
              dispatch({ type: "SET_FIELD", field: "labelData", value })
            }
            setCurrentPage={(value) =>
              dispatch({ type: "SET_FIELD", field: "currentPage", value })
            }
            fieldData={columnConfigData}
            configApiData={configApiData}
            totalCount={totalCount}
            onApplyGraphFilter={() => {
              dispatch({ type: "TOGGLE", field: "reload" });
            }}
            panelVisualizationType={"Table Report"}
            graphValidation={activeView === "graph" ? true : false}
            graphPreference={graphPreference}
          />
        </MetaDataProvider>
      )}
    </>
  );
}

export default StaticReports;
