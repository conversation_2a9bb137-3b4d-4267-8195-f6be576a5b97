import { useContext, useEffect, useState } from "react";
import { Form, Formik } from "formik";
import * as Yup from "yup";
import TextFieldWrapper from "../components/FormsUI/TextField";
import Select from "../components/FormsUI/Select";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import {
  alertTypeOptions,
  filterOptions,
  vloumeDropFilterOptions,
  deliveryReportDropFilterOptions,
  onlySpaceRegex,
  alertCategoryOptions,
  onlyNumSpaceNotAllowed,
} from "../common/constants";
import Button from "../components/Button/OutlinedButton";
import { DataContext } from "../context/DataContext";
import { alertServices } from "../services/alert-api";
import { useQueries, useQuery } from "react-query";

const AddAlertFormOne = ({ editDetail, isCopy }) => {
  const { handleNextClick, setFormData, formData, handleNextClickStep } =
    useContext(multiStepFormContext);
  const { alertDetails, setAlertDetails } = useContext(DataContext);

  // State for dropdown options
  const [dropdownOptions, setDropdownOptions] = useState({
    alertTypesOptions: [],
    alertFilterOptions: [],
    volumeTypesOptions: [],
    timePeriodOptions: [],
    timeIntervalOptions: [],
  });

  const keys = [
    "alertTypesOptions",
    "alertFilterOptions",
    "volumeTypesOptions",
    "timePeriodOptions",
    "timeIntervalOptions",
  ];
  const [filteredAlertFilterOptions, setFilteredAlertFilterOptions] = useState(
    []
  );
  const [selectedAlertType, setSelectedAlertType] = useState("");
  const queries = useQueries(
    keys.map((key) => ({
      queryKey: ["getMetaData", key],
      queryFn: () =>
        alertServices.getMetaData({ queryKey: ["getMetaData", key] }),
      onSuccess: (response) => {
        const dataForKey = response.data[key];

        if (key === "alertFilterOptions" && dataForKey) {
          // Special logic for alertFilterOptions
          const transformedFilterData = Object.entries(dataForKey).flatMap(
            ([alertType, options]) => {
              return options.map((option) => ({
                alertType,
                label: option.label,
                value: option.value,
              }));
            }
          );

          setDropdownOptions((prev) => ({
            ...prev,
            [key]: transformedFilterData,
          }));
        } else if (dataForKey && Array.isArray(dataForKey)) {
          // Generic transformation logic for other keys
          const transformedData = dataForKey.flatMap((item) => {
            if (Array.isArray(item.label) && Array.isArray(item.value)) {
              return item.label.map((label, index) => ({
                label: label,
                value: item.value[index],
              }));
            }
            return [{ label: item.label, value: item.value }];
          });

          setDropdownOptions((prev) => ({
            ...prev,
            [key]: transformedData,
          }));
        }
      },
    }))
  );

  useEffect(() => {
    if (
      selectedAlertType ||
      formData?.alertType ||
      editDetail?.metadata?.meta_alert_type
    ) {
      const options = dropdownOptions.alertFilterOptions.filter(
        (option) =>
          option.alertType ===
          (selectedAlertType ||
            formData?.alertType ||
            editDetail?.metadata?.meta_alert_type)
      );

      setFilteredAlertFilterOptions(options);
    } else {
      setFilteredAlertFilterOptions([]); // Reset when no alertType is selected
    }
  }, [
    formData?.alertType,
    selectedAlertType,
    dropdownOptions.alertFilterOptions,
    editDetail?.metadata?.meta_alert_type,
  ]);

  const validationSchema = Yup.object().shape({
    alertName: Yup.string()
      .required("Alert Name is required")
      .max(190, "Max length allowed is 190")
      .min(3, "Minimum 3 characters required")
      .matches(onlyNumSpaceNotAllowed, "Enter atleast one alphabet"),
    filterType: Yup.string().required("Filter is required"),
    alertType: Yup.string().required("Alert Type is required"),
    category: Yup.string().required("Alert Category is required"),
  });

  const determineFilterType = (filters, alertFilterOptions) => {
    const { customers, suppliers, destinations } = filters || {};

    if (
      customers?.length > 0 &&
      suppliers?.length > 0 &&
      destinations?.length > 0
    ) {
      return alertFilterOptions.find(
        (option) => option.value === "Customer-Supplier Destination"
      )?.value;
    }
    if (customers?.length > 0 && destinations?.length > 0) {
      return alertFilterOptions.find(
        (option) => option.value === "Customer Destination"
      )?.value;
    }
    if (suppliers?.length > 0 && destinations?.length > 0) {
      return alertFilterOptions.find(
        (option) => option.value === "Supplier Destination"
      )?.value;
    }
    if (customers?.length > 0) {
      return alertFilterOptions.find((option) => option.value === "Customer")
        ?.value;
    }
    if (suppliers?.length > 0) {
      return alertFilterOptions.find((option) => option.value === "Supplier")
        ?.value;
    }
    if (destinations?.length > 0) {
      return alertFilterOptions.find((option) => option.value === "Destination")
        ?.value;
    }

    return null;
  };
  return (
    <div>
      <Formik
        initialValues={{
          alertName: isCopy
            ? formData?.alertName
            : formData?.alertName || editDetail?.name || "",
          alertType:
            formData?.alertType || editDetail?.metadata?.meta_alert_type || "",
          category: formData?.category || editDetail?.category || "",
          filterType:
            formData?.filterType ||
            determineFilterType(
              editDetail?.filters,
              dropdownOptions.alertFilterOptions
            ) ||
            "",
        }}
        validationSchema={validationSchema}
        enableReinitialize={true}
        onSubmit={(values) => {
          const keys = Object.keys(values);
          setAlertDetails((prevDetails) => {
            const newDetails = [...prevDetails];
            newDetails[0] = keys; // Store keys for step 1
            return newDetails;
          });
          handleNextClick();
          handleNextClickStep();
          let newData = Object.assign(formData, values);
          setFormData(newData);
        }}
      >
        {({ values, errors, status, setFieldValue }) => (
          <Form>
            <div className="flex flex-col items-center justify-center mb-4 mx-10 md:mx-0">
              <div className="w-full md:w-[500px] mt-4">
                <InputLabel label={"Alert Name"} isMandatory={true} />
                <TextFieldWrapper
                  name="alertName"
                  placeholder={"Enter alert name"}
                  setFormData={setFormData}
                />
              </div>
              <div className="w-full md:w-[500px] mt-4">
                <InputLabel label={"Alert Type"} isMandatory={true} />
                <Select
                  name="alertType"
                  options={dropdownOptions?.alertTypesOptions}
                  placeholder={"Select alert type"}
                  setFormData={setFormData}
                  visTypeDropdown={true}
                  onChange={(e) => {
                    const selectedType = e?.value;
                    setSelectedAlertType(selectedType);
                    setFieldValue("filterType", "");
                  }}
                />
              </div>
              <div className="w-full md:w-[500px] mt-4">
                <InputLabel label={"Alert Category"} isMandatory={true} />
                <Select
                  name="category"
                  options={alertCategoryOptions}
                  placeholder={"Select alert category"}
                  setFormData={setFormData}
                  visTypeDropdown={true}
                  // visTypeChange={visTypeChange}
                  // setVisTypeChange={setVisTypeChange}
                />
              </div>
              <div className="w-full md:w-[500px] mt-4">
                <InputLabel label={"Filter"} isMandatory={true} />
                <Select
                  name="filterType"
                  options={filteredAlertFilterOptions}
                  placeholder={"Select filter"}
                  setFormData={setFormData}
                  visTypeDropdown={true}
                  // visTypeChange={visTypeChange}
                  // setVisTypeChange={setVisTypeChange}
                />
              </div>
            </div>
            <div className="flex-grow flex justify-end items-center mx-20 mt-20">
              <Button
                block="true"
                type="submit"
                label="Next"
                value="submit"
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded "
              ></Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};
export default AddAlertFormOne;
