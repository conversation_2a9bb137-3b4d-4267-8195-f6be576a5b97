import React from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const ViewAlertDialog = ({ open, handleClose, data }) => {
  if (!data || !data?.data || Object.keys(data?.data).length === 0) {
    return null;
  }

  // Extract headers and values from data.data
  const headers = Object.keys(data?.data);
  const values = Object.values(data?.data);

  // Create the dialog title in format: "name: start - end"
  const dialogTitle = `${data?.header?.name} ${data?.header?.intervalStartTime} - ${data?.header?.intervalEndTime}`;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "12px",
          overflow: "hidden",
          position: "absolute",
          top: "30%",
          left: "50%",
          transform: "translateX(-50%)",
          margin: 0,
        },
      }}
    >
      {/* Dialog Header */}
      <DialogTitle
        sx={{
          backgroundColor: "#2a57c9",
          color: "#fff",
          fontWeight: "bold",
          textAlign: "center",
          position: "relative",
        }}
      >
        {dialogTitle}
        <IconButton
          onClick={handleClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: "#fff",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      {/* Dialog Content */}
      <DialogContent
        sx={{
          padding: 0,
          backgroundColor: "#f5f5f5",
        }}
      >
        <Table sx={{ borderCollapse: "separate", borderSpacing: 0 }}>
          {/* Table Header */}
          <TableHead>
            <TableRow>
              {headers.map((header, index) => (
                <TableCell
                  key={index}
                  sx={{
                    backgroundColor: "#d32f2f",
                    color: "#fff",
                    textAlign: "center",
                    fontWeight: "bold",
                    borderRight: "1px solid #fff", // Vertical line between columns
                    "&:last-child": {
                      borderRight: "none", // No border for the last column
                    },
                  }}
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>

          {/* Table Body */}
          <TableBody>
            <TableRow
              sx={{
                "&:nth-of-type(even)": { backgroundColor: "#e3f2fd" },
                "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
              }}
            >
              {values.map((value, cellIndex) => {
                // Determine background color based on column index
                let backgroundColor = "#ffffff"; // Default white background
                if (
                  ["submission", "code"].some((keyword) =>
                    headers[cellIndex].toLowerCase().includes(keyword)
                  )
                ) {
                  backgroundColor = "#E9F5FD"; // Light blue for submission columns
                } else if (
                  ["variation", "percentage"].some((keyword) =>
                    headers[cellIndex].toLowerCase().includes(keyword)
                  )
                ) {
                  backgroundColor = "#D3F1CB"; // Light green for variation columns
                }

                return (
                  <TableCell
                    key={cellIndex}
                    sx={{
                      textAlign: "center",
                      borderRight: "1px solid #ccc", // Vertical line between columns
                      backgroundColor: backgroundColor, // Dynamic background color
                      "&:last-child": {
                        borderRight: "none", // No border for the last column
                      },
                    }}
                  >
                    {value}
                  </TableCell>
                );
              })}
            </TableRow>
          </TableBody>
        </Table>
      </DialogContent>
    </Dialog>
  );
};

export default ViewAlertDialog;
