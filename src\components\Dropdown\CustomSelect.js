import React, { useState } from "react";
import ReactSelect from "react-select";
import theme from "../../tailwind-theme";

const CustomSelect = ({
  options,
  selectedOption,
  handleChange,
  placeholder,
}) => {
  return (
    <div>
      {" "}
      {/* Example container width */}
      <ReactSelect
        options={options}
        value={selectedOption}
        onChange={handleChange}
        placeholder={placeholder ? placeholder : "select"}
        styles={{
          control: (provided, state) => ({
            ...provided,
            width: "254px", // Width
            height: "40px", // Height
            flexShrink: 0, // Flex shrink
            borderRadius: "4px", // Border radius
            border: `2px solid ${theme.borderColor.outerBorder}`, // Border color
            boxShadow: "none", // Remove default box shadow
            "&:hover": {
              border: `2px solid ${theme.borderColor.outerBorder}`, // Example border color changes when hovered
            },
          }),
          option: (provided, state) => ({
            ...provided,
            backgroundColor: state.isFocused ? "#3182ce" : null, // Example option background color changes when focused
            color: state.isFocused ? "#ffffff" : null, // Example option text color changes when focused
          }),
          singleValue: (provided) => ({
            ...provided,
            color: "#4A5568", // Example single value color
          }),
        }}
      />
    </div>
  );
};

export default CustomSelect;
