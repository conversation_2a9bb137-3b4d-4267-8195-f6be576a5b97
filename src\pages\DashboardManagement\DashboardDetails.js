import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import { Formik, Form } from "formik";
import Table from "../../components/table/ReportTable";
import InputLabel from "../../components/FormsUI/InputLabel/InputLabel";
import { EditingIcon, TrashIcon, SearchhIcon } from "../../icons";
import Title from "../../Title";
import BackButton from "../../components/Button/Button";
import Button from "../../components/Button/OutlinedButton";
import { getAll, deleteDashboard } from "../../services/dashboard-api";
import { useQuery, useMutation } from "react-query";
import ErrorDialog from "../../popups/ErrorDialog";
import SuccessDialog from "../../popups/SuccessDialog";
import DeleteDialog from "../../popups/DeleteDialog";
import AddDashboard from "../../popups/AddDashboard";
import { AuthContext } from "../../context/AuthContext";
import InfoModal from "../../components/modals/InfoModal";
import { useNavigate } from "react-router-dom";
import Pagination from "../../components/Pagination/Pagination";
import EditButton from "../../components/Button/EditButton";
import bgImage from "../../assets/img/Records.png";
import theme from "../../tailwind-theme";
import { CssTooltip } from "../../components/StyledComponent";

function DashboardDetails() {
  const [openDialog, setOpenDialog] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [message, setMessage] = useState("");
  const [value, setValue] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const [tableData, setTableData] = useState([]);
  const navigate = useNavigate();

  const pageSize = 10;
  const { roles, configApiData } = useContext(AuthContext);
  const prevRolesRef = useRef(roles);

  useEffect(() => {
    if (prevRolesRef.current !== roles) {
      prevRolesRef.current = roles;
      const defaultDashboard = roles?.resources.some(
        (resource) =>
          resource.name === "Default Dashboard" &&
          resource.permissions.view === 1
      );

      if (defaultDashboard) {
        navigate("/app/home");
      }
    }
  }, [roles, navigate]);

  const {
    data: dataList,
    isLoading,
    isFetching,
    refetch,
  } = useQuery(["dashboardList", currentPage, pageSize, searchInput], getAll, {
    onSuccess: (resp) => {
      //console.log("resp", resp);
      setTableData(
        resp?.data?.data?.map((x) => {
          return {
            cardCount: x.cardCount,
            createdAt: x.createdAt,
            //createdAt: formatDateTime(x.createdAt),
            //dayjs(x.createdAt).format("DD/MM/YYYY"),
            createdBy: x.createdBy,
            id: x.id,
            name: x.name,
            panelCount: x.panelCount,
            sharedByAdmin: x.sharedByAdmin,
          };
        })
      );
    },
  });

  const { mutate: DeleteDashboard, isLoading: dashboardLoading } =
    useMutation(deleteDashboard);

  const handleDelete = (id, event) => {
    // console.log(" Id", data);
    event.stopPropagation();
    setValue(id);
    setDeleteDialog(true);
  };
  const handleEdit = (id, event) => {
    //console.log(" Id", id);
    event.stopPropagation();
    setValue(id);
    navigate(`/app/dashboard/details/edit/${id}`, {
      state: { isEdit: true },
    });
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  const handleDialogOpen = () => {
    setOpenDialog(true);
  };

  const handleOpenInfoModal = () => {
    setShowAlertConfirmation(true);
  };
  const handleCloseInfoModal = () => {
    setShowAlertConfirmation(false);
  };
  const handleRowClick = (row) => {
    //console.log("row details", row.original);
    navigate(`/app/dashboard/mydashboard/${row.original.id}`, {
      state: {
        isName: true,
        createdBy: row.original.sharedByAdmin,
        onRowClick: true,
      },
    });
  };

  function handlePageChange(page) {
    setCurrentPage(page);
  }
  //console.log("Roles", roles);
  const permissions = roles?.resources?.filter(
    (res) => res.name === "Dashboard Management"
  )[0].permissions;
  //console.log("Permisssions", permissions);
  const columns = useMemo(
    () => [
      {
        header: "Dashboard",
        accessorKey: "name",
        size: 120,
        Cell: ({ row }) => (
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <div>
              <CssTooltip title={row?.original?.name} placement="top" arrow>
                <p>
                  {row?.original?.name
                    ? row?.original?.name.length > 20
                      ? `${row?.original?.name.slice(0, 20)}...`
                      : row?.original?.name
                    : "-"}
                </p>
              </CssTooltip>
            </div>
          </div>
        ),
      },
      {
        header: "Creation Date",
        accessorKey: "createdAt",
        size: 140,
      },
      {
        header: "No of Panels",
        accessorKey: "panelCount",
        size: 100,
      },
      {
        header: "No of Cards",
        accessorKey: "cardCount",
        size: 100,
      },
      {
        header: "Created by",
        accessorKey: "createdBy",
        size: 120,
      },
      {
        header: "",
        accessorKey: "action",
        enableSorting: false,
        enableColumnFilter: false,
        size: 140,
        Cell: ({ row }) => (
          <div className="flex gap-6 mr-3">
            <EditButton
              label="Edit"
              icon={EditingIcon}
              onClick={(e) => {
                e.stopPropagation();
                if (permissions?.update === 0) {
                  setShowAlertConfirmation(true);
                  setMessage("Update permission not allowed");
                } else {
                  handleEdit(row.original.id, e);
                }
              }}
              disabled={
                !roles?.isSuperAdminRole ? row?.original?.sharedByAdmin : false
              }
            />
            <EditButton
              label="Delete"
              icon={TrashIcon}
              onClick={(e) => {
                e.stopPropagation();
                if (permissions?.delete === 0) {
                  setShowAlertConfirmation(true);
                  setMessage("Delete permission not allowed");
                } else {
                  handleDelete(row.original.id, e);
                }
              }}
              disabled={
                !roles?.isSuperAdminRole ? row.original.sharedByAdmin : false
              }
            />
          </div>
        ),
      },
    ],
    []
  );

  return (
    <>
      <div className="bg-bgPrimary my-5">
        <div className="mt-10">
          <Title title={"List of My Dashboard"} />
        </div>
        <div className="border border-listBorder bg-white p-3 mt-10">
          <div className="mx-3 mt-3">
            <div className="mt-5">
              {!isLoading &&
              dataList?.data?.totalCount === 0 &&
              searchInput === "" ? (
                <>
                  <div className="border border-outerBorder mb-5">
                    <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                      {"Oops ! no records to display."}
                    </div>
                    <div className="flex justify-center text-tabColor text-base font-semibold mt-5 ">{`You have not added any dashboard yet.`}</div>
                    <div className="flex justify-center my-12">
                      <img
                        src={bgImage}
                        style={{
                          height: "10%",
                          width: "10%",
                          objectFit: "cover",
                        }}
                        alt="bg"
                      />
                    </div>
                    <div className="flex justify-center mb-5">
                      <Button
                        label={"+ Add New Dashboard"}
                        buttonClassName="w-full md:w-[201px] text-xs mb-3 rounded"
                        onClick={() => {
                          //console.log("dataList", dataList?.data?.totalCount);

                          if (!roles.isSuperAdminRole) {
                            if (permissions?.create === 1) {
                              const count = dataList?.data?.data?.filter(
                                (item) => !item.sharedByAdmin
                              ).length;
                              //console.log("count", count);
                              if (roles.dashboardCount > count) {
                                handleDialogOpen();
                              } else {
                                handleOpenInfoModal(true);
                                setMessage(
                                  `You can add max of ${roles.dashboardCount} dashboards.`
                                );
                              }
                            } else {
                              handleOpenInfoModal(true);
                              setMessage("Create permission not allowed");
                            }
                          } else {
                            if (
                              configApiData.MAX_DASHBOARDS >
                              dataList?.data?.totalCount
                            ) {
                              handleDialogOpen();
                            } else {
                              handleOpenInfoModal(true);
                              setMessage(
                                `You can add max of ${configApiData.MAX_DASHBOARDS} dashboards.`
                              );
                            }
                          }
                        }}
                      ></Button>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <Formik initialValues={{ dashboard: "" }}>
                    <Form>
                      <InputLabel label={" Dashboard Name"} />
                      <div className="flex mt-3">
                        <div className=" w-1/2 ">
                          <input
                            type="text"
                            style={{
                              border: `1px solid ${theme.borderColor.outerBorder}`,
                              paddingLeft: "2.5rem",
                            }}
                            className="w-full px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                            placeholder="Search dashboard"
                            value={searchInput}
                            onChange={(e) => {
                              setSearchInput(e.target.value);
                              setCurrentPage(1);
                            }}
                          />

                          <div
                            className=""
                            style={{
                              marginLeft: "0.8rem",
                              marginTop: "-1.84rem",
                            }}
                          >
                            <SearchhIcon className="w-4 h-4" />
                          </div>
                        </div>

                        <div className="flex-grow flex justify-end items-center gap-3">
                          <BackButton
                            label={"My Dashboard"}
                            buttonClassName="w-full md:w-[201px]  border-errorBorder text-xs mb-3 rounded text-errorColor"
                            onClick={() => {
                              navigate("/app/dashboard/details/mydashboard");
                            }}
                          ></BackButton>
                          <Button
                            label={"+ Add New Dashboard"}
                            buttonClassName="w-full md:w-[201px] text-xs mb-3 rounded"
                            onClick={() => {
                              //console.log("dataList", dataList?.data?.totalCount);

                              if (!roles?.isSuperAdminRole) {
                                if (permissions?.create === 1) {
                                  const count = dataList?.data?.data?.filter(
                                    (item) => !item.sharedByAdmin
                                  ).length;
                                  //console.log("count", count);
                                  if (roles?.dashboardCount > count) {
                                    handleDialogOpen();
                                  } else {
                                    handleOpenInfoModal(true);
                                    setMessage(
                                      `You can add max of ${roles?.dashboardCount} dashboards.`
                                    );
                                  }
                                } else {
                                  handleOpenInfoModal(true);
                                  setMessage("Create permission not allowed");
                                }
                              } else {
                                if (
                                  configApiData?.MAX_DASHBOARDS >
                                  dataList?.data?.totalCount
                                ) {
                                  handleDialogOpen();
                                } else {
                                  handleOpenInfoModal(true);
                                  setMessage(
                                    `You can add max of ${configApiData?.MAX_DASHBOARDS} dashboards.`
                                  );
                                }
                              }
                            }}
                          ></Button>
                        </div>
                      </div>
                    </Form>
                  </Formik>
                  <div className="mt-5">
                    <Table
                      columns={columns}
                      data={Array.isArray(tableData) ? tableData : []}
                      onRowClick={handleRowClick}
                      isLoading={isFetching}
                    />
                  </div>

                  {dataList?.data &&
                    dataList?.data?.totalCount !== undefined && (
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          marginTop: "20px",
                        }}
                      >
                        <Pagination
                          className="pagination-bar"
                          currentPage={currentPage}
                          totalCount={dataList?.data?.totalCount}
                          pageSize={pageSize}
                          onPageChange={(page) => {
                            handlePageChange(page);
                          }}
                        />
                      </div>
                    )}
                </>
              )}
            </div>
          </div>
        </div>
        <DeleteDialog
          show={deleteDialog}
          onHide={() => setDeleteDialog(false)}
          onConfirm={() => {
            DeleteDashboard(
              { value },
              {
                onSuccess: () => {
                  setDeleteDialog(false);
                  setSearchInput("");
                  setMessage("Dashboard deleted successfully");
                  setCurrentPage(1);
                  setSuccessDialog(true);
                  refetch();
                },
                onError: (error) => {
                  setDeleteDialog(false);
                  setMessage(error?.response?.data?.message);
                  setErrorDialog(true);
                },
              }
            );
          }}
          title={"Are you sure to delete the dashboard ?"}
          isLoading={dashboardLoading}
        />
        <SuccessDialog
          show={suceessDialog}
          onHide={() => setSuccessDialog(false)}
          message={message}
        />
        <ErrorDialog
          show={errorDialog}
          onHide={() => setErrorDialog(false)}
          message={message}
        />
        <AddDashboard
          open={openDialog}
          handleClose={handleDialogClose}
          refetch={refetch}
        />
        <InfoModal
          show={showAlertConfirmation}
          onHide={handleCloseInfoModal}
          message={message}
        />
      </div>
    </>
  );
}

export default DashboardDetails;
