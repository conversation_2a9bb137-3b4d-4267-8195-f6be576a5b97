import React, { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Dialog from "@mui/material/Dialog";
import TextFieldWrapper from "../components/FormsUI/TextField";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Box from "@mui/material/Box";
import Button from "../components/Button/OutlinedButton";
import CancelButton from "../components/Button/Button";
import { useNavigate } from "react-router-dom";
import { CloseIcon } from "../icons";
import SuccessDialog from "./SuccessDialog";
import ErrorDialog from "./ErrorDialog";
import Select from "../components/FormsUI/Select";
import { reportService } from "../services/staticreport.service";
import { cardServices } from "../services/cards-api";
import { useQuery } from "react-query";

export default function AddCard({
  closeCard,
  openCard,
  edit,
  selectedDetails,
  refreshData,
  isAdd,
  onCardAdded,
}) {
  const onlySpaceRegex = /(?=.*[a-z])|(?=.*[A-Z])/;
  const navigate = useNavigate();
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [derievedList, setDerievedList] = useState([]);
  const [isloading, setLoading] = useState(false);
  const id = selectedDetails?.id;

  useQuery(["derievedList"], reportService.getDerivedField, {
    onSuccess: ({ data }) => {
      const list = data?.map((data) => {
        return {
          label: data,
          value: data,
        };
      });
      setDerievedList(list);
    },
  });

  return (
    <Box width={"100%"}>
      <Formik
        initialValues={{
          name: edit ? selectedDetails?.name : "",
          derivedField: edit ? selectedDetails?.reportField : "",
        }}
        validationSchema={Yup.object({
          name: Yup.string()
            .required("Card Name is Required")
            .max(256, "Max length allowed is 256 characters")
            .min(2, "Min length allowed is 2  characters")
            .matches(onlySpaceRegex, "Must contain atleast one Alphabet"),
          derivedField: Yup.string().required("Derived Field is Required"),
        })}
        enableReinitialize={true}
        onSubmit={({ name, derivedField }, { setStatus, setSubmitting }) => {
          let payload = {
            name: name,
            derivedField: derivedField,
          };

          if (edit) {
            setLoading(true);
            cardServices
              .update(payload, id)
              .then((res) => {
                // console.log("response", res);
                refreshData();
                setSuccessDialog(true);
                setMessage(res?.data?.message);
                closeCard();
              })
              .catch((err) => {
                // console.log("error", err);
                setErrorDialog(true);
                setMessage(
                  err?.response?.data?.message ||
                    "Something went wrong! Please check the connectivity!"
                );
                setSubmitting(false);
                closeCard();
              })
              .finally(() => {
                setLoading(false);
              });
          } else {
            setLoading(true);
            cardServices
              .create(payload)
              .then((res) => {
                if (!isAdd) {
                  refreshData();
                  setSuccessDialog(true);
                  setMessage(res?.data?.message);
                  closeCard();
                }
                if (isAdd) {
                  setSuccessDialog(true);
                  setMessage(res?.data?.message);
                  closeCard();
                }
              })
              .catch((err) => {
                //console.log("error", err);
                setErrorDialog(true);
                setMessage(
                  err?.response?.data?.message ||
                    "Something went wrong! Please check the connectivity!"
                );
                setSubmitting(false);
                closeCard();
              })
              .finally(() => {
                setLoading(false);
              });
          }
        }}
      >
        {({ setFieldValue, resetForm }) => {
          return (
            <>
              <Dialog
                open={openCard}
                onClose={() => {
                  closeCard();
                  resetForm();
                }}
                fullWidth
                onClick={(event) => {
                  if (event.target === event.currentTarget) {
                    closeCard();
                    resetForm();
                  }
                }}
                sx={{
                  "& .MuiDialog-container": {
                    "& .MuiPaper-root": {
                      width: "100%",
                      maxWidth: "450px", // Set your width here
                      //height: "350px",
                      margin: 0,
                    },
                  },
                }}
              >
                <Form>
                  <div className="mx-4  mb-5">
                    <div className=" mt-4 text-black text-base font-medium flex items-center justify-between">
                      {edit ? "Edit Card" : "Add Card"}
                      <CloseIcon
                        onClick={() => {
                          closeCard();
                          resetForm();
                        }}
                        className=" w-2.5 h-2.5"
                      />
                    </div>

                    <div className="mt-2  mb-3 border-b border-panelBorder" />

                    <div className="w-full mt-4">
                      <InputLabel label={"Card Name"} isMandatory={true} />
                      <TextFieldWrapper
                        name="name"
                        placeholder={"Enter card name"}
                      />
                    </div>
                    <div className="w-full  mt-4">
                      <InputLabel label={"Derived Field"} isMandatory={true} />
                      <Select
                        name="derivedField"
                        options={derievedList}
                        placeholder={"Select Derived Field"}
                        isSearchable={true}
                        menuListzIndex={9999}
                        // maxMenuHeight={150}
                      />
                    </div>
                    <div>
                      <div className="text-center mt-10 gap-5 mb-2">
                        <CancelButton
                          onClick={() => {
                            setFieldValue("name", "");
                            closeCard();
                            resetForm();
                          }}
                          label={"Cancel"}
                          buttonClassName="w-[100px] h-9 text-xs  "
                        ></CancelButton>
                        <Button
                          type="submit"
                          label={"Save"}
                          buttonClassName="w-[100px] h-9 text-xs ml-5"
                          loading={isloading}
                        ></Button>
                      </div>
                    </div>
                  </div>
                </Form>
              </Dialog>
              <SuccessDialog
                show={suceessDialog}
                onHide={() => {
                  resetForm();
                  setSuccessDialog(false);
                  if (isAdd) {
                    onCardAdded();
                  }
                }}
                message={message}
              />
              <ErrorDialog
                show={errorDialog}
                onHide={() => {
                  resetForm();
                  setErrorDialog(false);
                }}
                message={message}
              />
            </>
          );
        }}
      </Formik>
    </Box>
  );
}
