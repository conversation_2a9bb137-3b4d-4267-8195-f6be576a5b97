import React, { useContext } from "react";
import routes from "../../routes/sidebar";
import { NavLink } from "react-router-dom";
import * as Icons from "../../icons";
import { AuthContext } from "../../context/AuthContext";
import { roleService } from "../../services/roles.service";
import { useQuery } from "react-query";
import { DataContext } from "../../context/DataContext";

function Icon({ icon, ...props }) {
  const Icon = Icons[icon];
  return <Icon {...props} />;
}

function SidebarContent() {
  const { user, setRoles, roles } = useContext(AuthContext);
  const { handleReset } = useContext(DataContext);
  const userId = user.roleId;

  useQuery(["/rolesDetails"], () => roleService.getById(userId), {
    enabled: !!userId,
    onSuccess: (data) => {
      setRoles(data?.data);
    },
    refetchOnWindowFocus: true,
  });

  let filteredRoutes = [];

  if (roles !== null) {
    const { resources, dynamicDashboard } = roles;

    filteredRoutes = routes.filter((route) => {
      if (
        route.name === "Alert Management" &&
        !process.env.REACT_APP_ALERT_MANAGEMENT
      ) {
        return false; // Skip Alert Management if the feature is disabled
      }
      if (route.name === "Offline Downloads") {
        const dashboardManagement = resources?.find(
          (res) => res.name === "Dashboard Management"
        );

        const reportManagement = resources?.find(
          (res) => res.name === "Report Management"
        );

        const hasCreateOrEdit =
          dashboardManagement?.permissions?.create === 1 ||
          dashboardManagement?.permissions?.edit === 1;

        const hasOnlyView =
          dashboardManagement?.permissions?.view === 1 &&
          dashboardManagement?.permissions?.create !== 1 &&
          dashboardManagement?.permissions?.edit !== 1;

        const hasReportView = reportManagement?.permissions?.view === 1;

        // ✅ Condition 1: Dashboard create/edit
        if (hasCreateOrEdit) {
          return true;
        }

        // ✅ Condition 2: Dashboard only view + dynamicDashboard download
        if (hasOnlyView) {
          return dynamicDashboard?.some(
            (item) => item.permissions?.download === 1
          );
        }

        // ✅ Condition 3: Report Management view access
        if (hasReportView) {
          return true;
        }

        // ❌ Default deny
        return false;
      }

      // ✅ General condition for all other routes
      return resources?.some(
        (res) =>
          res.name === route.roleName &&
          res.permissions?.[route.permission] === 1
      );
    });
  }

  return (
    <div className="flex flex-col bg-bgPrimary overflow-hidden">
      <div
        style={{
          color: "white",
          marginLeft: "10px",
        }}
      >
        {filteredRoutes.map((route, i) =>
          route.name ? (
            <NavLink
              exact
              to={route.path}
              className="px-4 py-4 border-b border-white w-[95px] text-sm font-semibold transition-colors duration-150 hover:text-gray-800 dark:hover:text-gray-200 flex flex-col items-center"
              activeClassName="text-gray-800 dark:text-gray-100"
              key={route.name}
              style={({ isActive }) => ({
                backgroundColor: isActive ? "gray" : "#545151 ",
                borderRadius: "2px",
              })}
              onClick={() => {
                handleReset();
              }}
            >
              <Icon
                aria-hidden="true"
                icon={route.icon}
                style={{ height: "20px", marginBottom: "10px" }}
              />
              <span className="mt-1 text-center text-xs font-normal mb-2">
                {route.name}
              </span>
            </NavLink>
          ) : null
        )}
      </div>
    </div>
  );
}

export default SidebarContent;
