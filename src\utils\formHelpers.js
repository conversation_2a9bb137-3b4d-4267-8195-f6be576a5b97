/**
 * Helper function to filter logical operators based on conditions below the current filter
 * @param {Array} logicalOperator - Array of logical operator options
 * @param {Object} values - Form values containing conditions array
 * @param {number} currentIndex - Current filter index
 * @returns {Array} Filtered array of logical operator options
 */
export const filterLogicalOperators = (
  logicalOperator,
  values,
  currentIndex
) => {
  return logicalOperator.filter((option) => {
    // Check if there are any conditions below the current one that have values
    const hasConditionsBelow = values.conditions
      .slice(currentIndex + 1)
      .some(
        (condition) => condition.type1 || condition.type2 || condition.type3
      );

    // If there are conditions below and option is "no", disable it
    if (hasConditionsBelow && option.value === "no") {
      return false;
    }
    return true;
  });
};
