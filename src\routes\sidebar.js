/**
 * ⚠ These are used just to render the Sidebar!
 * You can include any link here, local or external.
 *
 * If you're looking to actual Router routes, go to
 * `routes/index.js`
 */
const routes = [
  {
    path: "/app/home", // the url
    icon: "HomeIcon", // the component being exported from icons/index.js
    name: "Dashboard", // name that appear in Sidebar
    roleName: "Default Dashboard",
    permission: "view",
  },
  {
    path: "/app/dashboard/details",
    icon: "DashboardIcon",
    name: "Dashboard Management",
    roleName: "Dashboard Management",
    permission: "view",
  },
  {
    path: "/app/reports",
    icon: "ReportsIcon",
    name: "Reports",
    roleName: "Report Management",
    permission: "view",
  },
  {
    path: "/app/cdrsearch",
    icon: "CDRSearchIcon",
    name: "CDR Search",
    roleName: "CDR Search",
    permission: "view",
  },
  {
    path: "/app/offline/downloads",
    icon: "CDRSearchIcon",
    name: "Offline Downloads",
    roleName: "CDR Search",
    permission: "view",
  },
  {
    path: "/app/panelmanagement",
    icon: "PanelIcon",
    name: "Panel Management",
    roleName: "Panel Management",
    permission: "view",
  },
  {
    path: "/app/cardmanagement",
    icon: "CardIcon",
    name: "Card Management",
    roleName: "Card Management",
    permission: "view",
  },
  {
    path: "/app/groupmanagement",
    icon: "GroupIcon",
    name: "Group Management",
    roleName: "Group Management",
    permission: "view",
  },
  {
    path: "/app/alerts",
    icon: "PeopleIcon",
    name: "Alert Management",
    roleName: "Alert Management",
    permission: "view",
  },
  {
    path: "/app/logs",
    icon: "AuditLogIcon",
    name: "Audit Logs",
    roleName: "Logs Management",
    permission: "view",
  },
  {
    path: "/app/rolemanagement",
    icon: "RoleIcon",
    name: "Role Management",
    roleName: "Role Management",
    permission: "view",
  },
  {
    path: "/app/usermanagement",
    icon: "PeopleIcon",
    name: "User Management",
    roleName: "User Management",
    permission: "view",
  },
  // {
  //   path: "/app/createdashboard",
  //   icon: "HomeIcon",
  //   name: "Create Dashboard",
  //   roleName: "Dashboard Management",
  //   permission: "create",
  // },
  // {
  //   path: "/app/mydashboard",
  //   icon: "HomeIcon",
  //   name: "My Dashboard",
  //   roleName: "Dashboard Management",
  //   permission: "view",
  // },
];

export default routes;
