import axios from "axios";
import getAPIMap from "../routes/ApiUrls";
//import { config } from "../assets/config/config";
//const apiUrl = config.api.url;

export async function getAllCards(options) {
  let url = getAPIMap("card") + `?values=${true}`;
  if (options.queryKey[1] && options.queryKey[1] !== "") {
    url = url + "&page=" + options.queryKey[1];
  }
  if (options.queryKey[2] && options.queryKey[2] !== "") {
    url = url + "&limit=" + options.queryKey[2];
  }
  if (options.queryKey[3] && options.queryKey[3] !== "") {
    url = url + "&search=" + options.queryKey[3];
  }
  if (options.queryKey[4] !== "" && options.queryKey[4] !== undefined) {
    url += `&createdBy=${options.queryKey[4]}`;
    url += `&userAnalysis=${true}`;
  }

  let response = axios.get(url);
  // console.log("response", response);
  return response;
}
export async function getCardsById(options) {
  let url =
    getAPIMap("card") +
    `/${options.queryKey[1]}?values=${true}&userAnalysis=${true}&userId=${
      options.queryKey[2]
    }`;
  let response = axios.get(url);
  return response;
}

export async function getAllUser(options) {
  let url = getAPIMap("user");
  let response = axios.get(url);
  return response;
}
