import React, { useState, useEffect, useRef } from "react";
import { ExpandIconn, SearchhIcon } from "../../icons";
import { CssTooltip } from "../StyledComponent";
import CustomSwitch from "../Toggle-switch/switch";
import Button from "../Button/Button";
import OutlinedButton from "../Button/OutlinedButton";
import { useMutation } from "react-query";
import { reportSave } from "../../services/staticreport.service";

const ColumnSelector = ({
  allFields,
  appliedFields,
  onApply,
  onOpen,
  derivedFields,
  reportId,
  reportName,
  reportType,
  onTriggerPreview,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [fieldStates, setFieldStates] = useState(appliedFields);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAll, setShowAll] = useState(false);
  const menuRef = useRef(null);
  const { mutate: reportSaveAPI, isLoading: saveloading } =
    useMutation(reportSave);

  useEffect(() => {
    // When the dropdown is closed, reset the local state to match the applied one
    if (!isOpen) {
      setFieldStates(appliedFields);
    }
  }, [isOpen, appliedFields]);

  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const filteredFields = allFields
    .filter((label) => label.toLowerCase().includes(searchTerm.toLowerCase()))
    .sort((a, b) => {
      // Show toggled-on fields first
      const aOn = fieldStates[a] ? 0 : 1;
      const bOn = fieldStates[b] ? 0 : 1;
      if (aOn !== bOn) return aOn - bOn;
      return 0;
    });

  const handleApply = () => {
    const selectedColumns = Object.entries(fieldStates)
      .filter(([, isVisible]) => isVisible)
      .map(([field]) => field);

    // Segregate derived fields and table fields
    const selectedDerivedFields = selectedColumns.filter(
      (field) => derivedFields && derivedFields.includes(field)
    );
    const selectedTableFields = selectedColumns.filter(
      (field) => !derivedFields || !derivedFields.includes(field)
    );

    const payload = {
      ...(reportId && { reportId }),
      reportName,
      reportType,
      selectedColumns,
      selectAll: showAll,
    };

    reportSaveAPI(
      { payload },
      {
        onSuccess: (data) => {
          onApply(fieldStates);
          // Trigger previewPanel API with segregated fields
          if (onTriggerPreview) {
            onTriggerPreview({
              selectedColumns,
              selectedDerivedFields,
              selectedTableFields,
            });
          }

          setIsOpen(false);
        },
        onError: (error) => {
          console.error("Error saving report:", error);
        },
      }
    );
  };

  const handleCancel = () => {
    setIsOpen(false); // state will be reset by useEffect
  };

  const toggleMenu = () => {
    const nextState = !isOpen;
    setIsOpen(nextState);
    if (nextState && onOpen) {
      onOpen();
    }
  };

  const handleFieldToggle = (label, shouldDisable, unused) => {
    if (shouldDisable) return;
    setFieldStates((prev) => {
      const updated = { ...prev, [label]: !prev[label] };
      // If any field is toggled off, setShowAll should be false
      const allOn = allFields.every((f) => updated[f]);
      setShowAll(allOn);
      return updated;
    });
  };

  return (
    <div className="relative" ref={menuRef}>
      <div
        className="rounded-full bg-bgouterBackground p-2 flex items-center justify-center cursor-pointer"
        onClick={toggleMenu}
      >
        <CssTooltip title={"Expand & Collapse"} placement="top" arrow>
          <ExpandIconn className="w-5 h-5" />
        </CssTooltip>
      </div>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 max-h-[400px] bg-white rounded-md shadow-xl z-20 border border-gray-200 p-5 flex flex-col gap-4">
          {/* Header */}
          <div className="flex justify-between items-center">
            <button
              className="text-sm opacity-50 text-gray-500 hover:text-black"
              onClick={() => {
                setFieldStates((prev) => {
                  const updated = { ...prev };
                  // Only affect filtered fields
                  filteredFields.forEach((label) => {
                    // Keep 'Date' always on and first derived field
                    if (label === "Date" || label === "Datetime") {
                      updated[label] = true;
                    } else if (derivedFields && derivedFields.includes(label)) {
                      // Keep at least one derived field checked
                      const currentDerivedSelected = Object.entries(
                        updated
                      ).filter(
                        ([field, isVisible]) =>
                          derivedFields.includes(field) &&
                          isVisible &&
                          field !== label
                      );
                      if (currentDerivedSelected.length === 0) {
                        updated[label] = true; // Keep this as the last derived field
                      } else {
                        updated[label] = false;
                      }
                    } else {
                      // For non-derived fields, keep at least one table field
                      const currentTableSelected = Object.entries(
                        updated
                      ).filter(
                        ([field, isVisible]) =>
                          !derivedFields?.includes(field) &&
                          field !== "Date" &&
                          field !== "Datetime" &&
                          isVisible &&
                          field !== label
                      );
                      if (
                        currentTableSelected.length === 0 &&
                        !updated["Date"] &&
                        !updated["Datetime"]
                      ) {
                        updated[label] = true; // Keep this as the last table field
                      } else {
                        updated[label] = false;
                      }
                    }
                  });
                  return updated;
                });
                setShowAll(false);
              }}
            >
              Hide all
            </button>
            <button
              className="text-sm text-gray-500 hover:text-black"
              onClick={() => {
                setFieldStates((prev) => {
                  const updated = { ...prev };
                  // Only affect filtered fields
                  filteredFields.forEach((label) => {
                    updated[label] = true;
                  });
                  // Check if all fields are now selected
                  const allOn = allFields.every((f) => updated[f]);
                  setShowAll(allOn);
                  return updated;
                });
              }}
            >
              Show All
            </button>
          </div>

          {/* Search */}
          <div className="relative w-full">
            <input
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-9 pr-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring focus:border-blue-300"
            />
            <div className="absolute top-1/2 left-3 transform -translate-y-1/2 text-gray-400">
              <SearchhIcon className="w-4 h-4" />
            </div>
          </div>

          {/* Toggle Fields List */}
          <div className="overflow-y-auto pr-1 flex-1">
            {filteredFields.map((label, index) => {
              const isDerived = derivedFields && derivedFields.includes(label);
              const selectedDerived = Object.entries(fieldStates).filter(
                ([field, isVisible]) =>
                  derivedFields && derivedFields.includes(field) && isVisible
              );
              const isLastDerived =
                isDerived && fieldStates[label] && selectedDerived.length === 1;

              const isDateColumn = label === "Date" || label === "Datetime";

              // Calculate selected table fields (non-derived fields excluding Date)
              const selectedTableFields = Object.entries(fieldStates).filter(
                ([field, isVisible]) =>
                  !derivedFields?.includes(field) && isVisible
              );

              // Check if this is the last table field selected
              const isLastTableField =
                !isDerived &&
                !isDateColumn &&
                fieldStates[label] &&
                selectedTableFields.length === 1;

              // Check if Date is the only non-derived field selected
              const isDateOnlyTableField =
                isDateColumn &&
                fieldStates[label] &&
                selectedTableFields.length === 0;

              // A field should be disabled if:
              // 1. It's the last derived field selected, OR
              // 2. It's the last table field selected, OR
              // 3. It's the Date field and no other table fields are selected
              const shouldDisable =
                isLastDerived ||
                isLastTableField ||
                isDateOnlyTableField ||
                isDateColumn;

              return (
                <div key={index} className="flex items-center gap-2 py-1">
                  {isLastDerived ? (
                    <CssTooltip
                      title="At least one aggregate/derived fields is required"
                      placement="top"
                      arrow
                    >
                      <div>
                        <CustomSwitch
                          checked={fieldStates[label] || false}
                          onChange={() =>
                            handleFieldToggle(label, shouldDisable, false)
                          }
                          disabled={true}
                        />
                      </div>
                    </CssTooltip>
                  ) : isLastTableField || isDateOnlyTableField ? (
                    <CssTooltip
                      title="At least one non aggregation/derived fields is required"
                      placement="top"
                      arrow
                    >
                      <div>
                        <CustomSwitch
                          checked={fieldStates[label] || false}
                          onChange={() =>
                            handleFieldToggle(label, shouldDisable, false)
                          }
                          disabled={true}
                        />
                      </div>
                    </CssTooltip>
                  ) : (
                    <CustomSwitch
                      checked={fieldStates[label] || false}
                      onChange={() =>
                        handleFieldToggle(label, shouldDisable, false)
                      }
                      disabled={false}
                    />
                  )}
                  <span className="text-sm text-gray-700">{label}</span>
                </div>
              );
            })}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button
              label="Cancel"
              type="button"
              buttonClassName={"w-24"}
              onClick={handleCancel}
            />
            <OutlinedButton
              label="Apply"
              type="button"
              buttonClassName={"w-24"}
              onClick={handleApply}
              disabled={saveloading}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ColumnSelector;
