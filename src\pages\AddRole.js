import { useEffect, useState } from "react";
import RolesForm from "../components/Forms/RolesForm";
import { useLocation } from "react-router";
import { useNavigate } from "react-router-dom";
import { roleService } from "../services/roles.service";
import Title from "../Title";
import BreadcrumbNavigation from "../components/BreadCrumps/BreadCrump";

const AddRole = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const queryString = window?.location?.search;
  const parameters = new URLSearchParams(queryString);
  const isEditPage = parameters?.get("edit");
  const editRoleItem = location.state;

  // console.log("123edit", isEditPage, location);
  return (
    <>
      <div className="flex mt-10 ">
        <BreadcrumbNavigation
          //linkOne={"Roles"}
          linkTwo={"Roles"}
          onlinkTwoClick={() => navigate("/app/rolemanagement")}
          title={isEditPage ? "Update Role" : "Add Role"}
        />
        {/* <div
          className="cursor-pointer"
          onClick={() => navigate("/app/rolemanagement")}
        >
          <Title title={isEditPage ? " < Update Role" : " < Add Role"} />
        </div> */}

        {/* <h1
          className="cursor-pointer"
          onClick={() => navigate("/app/rolemanagement")}
        >
          {"<"}
        </h1>
        <h1 className="ml-2 ">{isEditPage ? "Update Role" : "Add Role"}</h1> */}
      </div>
      <RolesForm
        style={{ background: "white" }}
        isEditPage={isEditPage}
        editRoleItem={editRoleItem}
      />
      {/* editRoleItem={editRoleItem}  */}
    </>
  );
};

export default AddRole;
