import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, ResponsiveContainer } from "recharts";
import { BASE_COLORS_PIE } from "../../common/constants";

// Generate extra unique colors if needed
const generateExtraColors = (count, existing) => {
  const colors = [];
  let i = 0;
  while (colors.length < count) {
    const hue = (i * 137.508) % 360; // golden angle
    const color = `hsl(${hue}, 65%, 55%)`;
    if (!existing.includes(color)) {
      colors.push(color);
    }
    i++;
  }
  return colors;
};

const getColors = (neededCount) => {
  if (neededCount <= BASE_COLORS_PIE.length) {
    return BASE_COLORS_PIE.slice(0, neededCount);
  }
  const extraNeeded = neededCount - BASE_COLORS_PIE.length;
  return [
    ...BASE_COLORS_PIE,
    ...generateExtraColors(extraNeeded, BASE_COLORS_PIE),
  ];
};

const PieChartGraph = ({ config, data = [] }) => {
  const { xAxis, yAxis } = config;

  // Remove "Select All" and process empty strings
  const filteredYAxis = yAxis.filter(
    (yKey) => yKey.toLowerCase() !== "select all"
  );

  // Process data to replace empty strings with "Empty String"
  const processedData = data?.map((item) => {
    const processedItem = { ...item };

    // Handle xAxis empty string
    if (processedItem[xAxis] === "") {
      processedItem[xAxis] = "Empty String";
    }

    // Handle yAxis empty strings (for any column names that might be empty)
    filteredYAxis.forEach((yKey) => {
      if (yKey === "") {
        // If the yAxis key itself is empty, we'll handle it in the rendering
        return;
      }
    });

    return processedItem;
  });

  // Filter out rows with all 0 values
  const chartData = processedData.filter((item) =>
    filteredYAxis.some((yKey) => item[yKey] > 0)
  );

  // Get colors
  const COLORS = getColors(chartData.length);

  // Keep track of hidden slices
  const [hiddenItems, setHiddenItems] = useState(new Set());

  // Reset legend (hidden items) whenever config or data change
  useEffect(() => {
    setHiddenItems(new Set());
  }, [config, data]);

  const toggleItem = (name) => {
    setHiddenItems((prev) => {
      const newSet = new Set(prev);
      const visibleCount = chartData.length - prev.size;

      if (newSet.has(name)) {
        // Unhiding is always allowed
        newSet.delete(name);
      } else if (visibleCount > 1) {
        // Hiding is allowed only if more than one item is visible
        newSet.add(name);
      }
      return newSet;
    });
  };

  // Custom tooltip with proper z-index and positioning
  const CustomTooltip = ({ active, payload, yKey, chartIndex }) => {
    if (active && payload && payload.length) {
      const { payload: item } = payload[0];
      const index = chartData.findIndex((d) => d[xAxis] === item[xAxis]);
      const fill = COLORS[index];

      return (
        <div
          style={{
            backgroundColor: "white",
            padding: "8px 12px",
            borderRadius: "6px",
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: "12px",
            whiteSpace: "nowrap",
            border: "1px solid #e5e7eb",
            zIndex: 9999 + chartIndex, // Ensure unique z-index for each chart
            position: "relative",
          }}
        >
          <p style={{ margin: 0, fontWeight: "bold", color: fill }}>
            {item[xAxis]}:{" "}
            <span style={{ color: "#374151" }}>{item[yKey]}</span>
          </p>
        </div>
      );
    }
    return null;
  };

  const [showFullLegend, setShowFullLegend] = useState(false);

  const renderCustomLegend = () => {
    const visibleCount = chartData.length - hiddenItems.size;
    const visibleItems = showFullLegend ? chartData : chartData.slice(0, 12);

    return (
      <div className="bg-white border border-gray-400 rounded-lg p-3 m-5">
        <div className="flex flex-wrap gap-6 items-center justify-start">
          {visibleItems.map((entry, index) => {
            const isHidden = hiddenItems.has(entry[xAxis]);
            const canHide = visibleCount > 1;
            const canClick = isHidden || canHide;

            return (
              <div
                key={`legend-${index}`}
                onClick={() => canClick && toggleItem(entry[xAxis])}
                className={`
                  flex items-center gap-2 transition-all duration-200
                  ${
                    canClick
                      ? "cursor-pointer"
                      : "cursor-not-allowed opacity-70"
                  }
                  ${canClick ? "hover:opacity-80" : ""}
                `}
                title={`${entry[xAxis]}${
                  !canClick
                    ? " (Last visible item - cannot hide)"
                    : isHidden
                    ? " (Click to show)"
                    : " (Click to hide)"
                }`}
              >
                <span
                  className={`
                    w-3 h-3 rounded-full flex-shrink-0
                    ${isHidden ? "opacity-30" : ""}
                  `}
                  style={{
                    backgroundColor: COLORS[index],
                  }}
                />
                <span
                  className="text-xs font-medium"
                  style={{
                    color: COLORS[index],
                    textDecoration: isHidden ? "line-through" : "none",
                  }}
                >
                  {entry[xAxis]}
                </span>
              </div>
            );
          })}
        </div>
        {chartData.length > 12 && (
          <div className="mt-3 text-center">
            <button
              onClick={() => setShowFullLegend((prev) => !prev)}
              className="text-blue-600 hover:text-blue-800 transition-colors text-xs font-medium"
            >
              {showFullLegend ? "Show Less ▲" : "Show All ▼"}
            </button>
          </div>
        )}
      </div>
    );
  };

  return (
    <div style={{ width: "100%", height: "auto" }}>
      {/* Grid layout for pies */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(3, 1fr)",
          gap: "20px",
          justifyItems: "center",
        }}
      >
        {filteredYAxis.map((yKey, chartIndex) => {
          // Display "Empty String" for empty yKey
          const displayYKey = yKey === "" ? "Empty String" : yKey;

          // Filter out hidden slices for this yKey
          const filteredData = chartData.filter(
            (item) => !hiddenItems.has(item[xAxis])
          );

          const total = filteredData.reduce(
            (sum, item) => sum + (item[yKey] || 0),
            0
          );

          // Show labels only when there are at most 5 slices
          const shouldShowLabels = filteredData.length <= 5;

          // Label renderer with font size 10px — show the slice value (yKey) instead of label
          const renderLabel = (props) => {
            if (!shouldShowLabels) return null;
            const value = props && props.payload ? props.payload[yKey] : null;
            const displayValue =
              value === null || value === undefined
                ? ""
                : Number.isInteger(value)
                ? value
                : Number(value).toFixed(2);

            // props.x / props.y are provided by recharts for label position
            return (
              <text
                x={props.x}
                y={props.y}
                fill="#000"
                fontSize={10}
                textAnchor="middle"
                dominantBaseline="central"
              >
                {displayValue}
              </text>
            );
          };

          // Custom label line renderer to avoid lines striking across the donut
          const renderLabelLine = (props) => {
            if (!props) return null;
            // Recharts typically provides mx, my (middle), ex, ey (end)
            const { mx, my, ex, ey } = props;
            if (ex == null || ey == null) return null;

            // Draw a short, dark connector and a small dot at the end so it's visible
            const stroke = "#374151"; // darker slate color
            const strokeWidth = 1.5;

            return (
              <g>
                <polyline
                  points={`${mx},${my} ${ex},${ey}`}
                  stroke={stroke}
                  strokeWidth={strokeWidth}
                  strokeOpacity={0.95}
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <circle cx={ex} cy={ey} r={2} fill={stroke} />
              </g>
            );
          };

          return (
            <div
              key={yKey}
              style={{
                width: 280,
                height: 280,
                position: "relative",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                isolation: "isolate", // Create new stacking context
              }}
            >
              {/* Title */}
              <div className="text-center text-black mb-2">{displayYKey}</div>

              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={filteredData}
                    dataKey={yKey}
                    nameKey={xAxis}
                    outerRadius={100}
                    innerRadius={45}
                    cy="50%"
                    cx="50%"
                    //  label={renderLabel}
                    // labelLine={shouldShowLabels ? renderLabelLine : false}
                  >
                    {filteredData.map((entry, index) => {
                      const globalIndex = chartData.findIndex(
                        (d) => d[xAxis] === entry[xAxis]
                      );
                      return (
                        <Cell
                          key={`cell-${yKey}-${index}`}
                          fill={COLORS[globalIndex]}
                        />
                      );
                    })}
                  </Pie>
                  <Tooltip
                    content={(props) => (
                      <CustomTooltip
                        {...props}
                        yKey={yKey}
                        chartIndex={chartIndex}
                      />
                    )}
                    wrapperStyle={{
                      zIndex: 9999 + chartIndex,
                      outline: "none",
                      pointerEvents: "none",
                    }}
                    cursor={false}
                    animationDuration={150}
                  />
                </PieChart>
              </ResponsiveContainer>

              {/* Total inside donut */}
              <div className="text-gray-500 absolute top-[55%] left-1/2 -translate-x-1/2 -translate-y-1/2 text-xs font-medium italic pointer-events-none">
                {total > 0
                  ? Number.isInteger(total)
                    ? total
                    : total.toFixed(2)
                  : "No records found"}
              </div>
            </div>
          );
        })}
      </div>

      {/* Shared Legend (one for all pies) - Only show if there's data */}
      {chartData.length > 0 && renderCustomLegend()}
    </div>
  );
};

export default PieChartGraph;
