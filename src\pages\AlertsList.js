import React, { useMemo, useState, useContext } from "react";
import Table from "../components/table/ReportTable";
import { EditingIcon, TrashIcon, Alert, SearchIcon } from "../icons";
import Pagination from "../components/Pagination/Pagination";
import SuccessDialog from "../popups/SuccessDialog";
import ErrorDialog from "../popups/ErrorDialog";
import DeleteDialog from "../popups/DeleteDialog";
import { AuthContext } from "../context/AuthContext";
import InfoModal from "../components/modals/InfoModal";
import Button from "../components/Button/OutlinedButton";
import { useQuery } from "react-query";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { DataContext } from "../context/DataContext";
import Title from "../Title";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import EditButton from "../components/Button/EditButton";
import { useAlerts } from "../hooks/useAlerts.hooks";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import BackButton from "../components/Button/Button";
import bgImage from "../assets/img/Records.png";
import theme from "../tailwind-theme";
import { useNavigate } from "react-router-dom";
import { alertServices } from "../services/alert-api";
import {
  formatDateToUTCAndLocal,
  formatDateCustom,
} from "../utils/fileDateFormator";
import { CssTooltip } from "../FormsUI/StyledComponent";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

const AlertsList = () => {
  const [searchStr, setSearchStr] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [openDialog, setOpenDialog] = useState(false);
  // const [alertList, setAlertList] = useState([]);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [edit, setEdit] = useState(false);
  const [id, setId] = useState("");
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [errorDialog, setErrorDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  //const pageSize = 10;
  const { resultPerPage } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(10);
  const navigate = useNavigate();
  const { roles } = useContext(AuthContext);

  const { setCurrentStep, setFormData, setStepCount } =
    useContext(multiStepFormContext);

  const {
    data: alertList,
    error: alertsError,
    isLoading: alertsLoading,
    refetch,
  } = useAlerts({
    page: currentPage,
    limit: limitPerPage,
    search: searchInput,
    // sorts,
    // filters,
  });

  const pageSize = 10;
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  // console.log("roles", roles);

  const permissions = roles?.resources?.filter(
    (res) => res?.name === "Alert Management"
  )[0].permissions;
  const handleKeyUp = (event) => {
    const value = event.target.value;
    setSearchStr(value);
    //setReload(true);

    if (value === "") {
      setCurrentPage(1);
      setSearchStr("");
      //setReload(true);
    } else {
      setCurrentPage(1);
    }
  };

  const handleCopy = (id) => {
    navigate("/app/alerts/add", { state: { id, isCopy: true } });
    setCurrentStep(0);
    setStepCount(0);
    setFormData([]);
  };

  const handleEdit = (id) => {
    navigate("/app/alerts/add", { state: { id, isEdit: true } });
    setCurrentStep(0);
    setStepCount(0);
    setFormData([]);
  };

  function handlePageChange(page) {
    setCurrentPage(page);
  }

  const handleOpenInfoModal = () => {
    setShowAlertConfirmation(true);
  };
  const handleCloseInfoModal = () => {
    setShowAlertConfirmation(false);
  };

  // console.log("alertlist", alertList);

  function openAlert() {
    setOpenDialog(true);
    navigate("/app/alerts/add");
    setCurrentStep(0);
    setStepCount(0);
    setFormData([]);
  }
  function closeAlert() {
    setOpenDialog(false);
  }

  const handleDelete = (rowData) => {
    // console.log("Recruiter Id", recruiter_id);
    setId(rowData.id);
    setDeleteDialog(true);
  };
  function handlePageChange(page) {
    setCurrentPage(page);
  }
  const handleLimitChange = (e) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
  };

  const columns = useMemo(() => [
    {
      header: "Alert Name",
      accessorKey: "name",
      Cell: ({ cell }) => {
        const value = cell.getValue();
        if (typeof value === "string" && value.length > 20) {
          return (
            <CssTooltip
              title={value}
              placement="top"
              arrow
              bgColor="#D9D9D9"
              color="#000000"
            >
              {value.substring(0, 15) + "..."}
            </CssTooltip>
          );
        }
        return value;
      },
    },
    {
      header: "Alert Type",
      accessorKey: "alertType",
    },
    {
      header: "Creation Date",
      accessorKey: "createdAt",
      Cell: ({ cell }) => {
        const date = cell.getValue();
        const parsedDate = new Date(date);
        if (parsedDate instanceof Date && !isNaN(parsedDate)) {
          return dayjs(parsedDate).format("DD-MMM-YYYY HH:mm:ss");
        }

        return "";
      },
    },
    {
      header: "Created by",
      accessorKey: "createdBy",
    },
    {
      header: "Last Updated by",
      accessorKey: "updatedBy",
    },
    {
      header: "",
      accessorKey: "action",
      size: 300,
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ row }) => (
        <>
          <div className="flex gap-8 mr-5">
            <EditButton
              label={"Copy"}
              icon={EditingIcon}
              onClick={() => {
                if (permissions.create === 0) {
                  setShowAlertConfirmation(true);
                  setMessage("Create/copy permission not allowed");
                } else {
                  handleCopy(row.original);
                }
              }}
            />
            <EditButton
              label={"Edit"}
              icon={EditingIcon}
              onClick={() => {
                if (permissions.update === 0) {
                  setShowAlertConfirmation(true);
                  setMessage("Update permission not allowed");
                } else {
                  handleEdit(row.original);
                }
              }}
            />
            <EditButton
              label={"Delete"}
              icon={TrashIcon}
              onClick={() => {
                if (permissions.delete === 0) {
                  setShowAlertConfirmation(true);
                  setMessage("Delete permission not allowed");
                } else {
                  handleDelete(row.original);
                }
              }}
            />
          </div>
        </>
      ),
    },
  ]);
  //console.log("users", users);

  if (alertsLoading)
    return (
      <h2 className="w-full h-screen flex justify-center items-center text-center p-6">
        Loading...
      </h2>
    );
  if (alertsError)
    return (
      <h2 className="w-full h-screen flex justify-center items-center text-center p-6">
        Something went wrong
      </h2>
    );
  return (
    <>
      <div className="bg-bgPrimary my-5">
        <div className="sticky top-0 bg-bgPrimary z-10">
          <div className="h-5"></div>
          <Title title={"List of Alerts"} />
          <div className="h-5"></div>
        </div>
        <div className="border border-listBorder bg-white p-3">
          <div className="mx-3 mt-3">
            <div className="mt-5">
              {!alertsLoading &&
              alertList?.totalCount === 0 &&
              searchInput === "" ? (
                <>
                  <div className="border border-outerBorder mb-5">
                    <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                      {"Oops ! no records to display."}
                    </div>
                    <div className="flex justify-center text-tabColor text-base font-semibold mt-5 ">{`You have not created any alerts yet.`}</div>
                    <div className="flex justify-center my-12">
                      <img
                        src={bgImage}
                        style={{
                          height: "10%",
                          width: "10%",
                          objectFit: "cover",
                        }}
                        alt="bg"
                      />
                    </div>
                    <div className="flex justify-center mb-5">
                      <Button
                        buttonClassName="text-xs w-36 text-white h-10 "
                        label={"+ Add New Alert"}
                        onClick={() => {
                          setEdit(false);
                          if (permissions.create === 1) {
                            openAlert();
                          } else {
                            handleOpenInfoModal(true);
                            setMessage("Create permission not allowed");
                          }
                        }}
                      ></Button>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  {" "}
                  <div className="mx-3">
                    <InputLabel label={"Alert Name"} />
                    <div class="w-full flex mt-3">
                      <div className=" w-1/2 ">
                        <input
                          type="text"
                          style={{
                            border: `1px solid ${theme.borderColor.outerBorder}`,
                            paddingLeft: "2rem",
                          }}
                          className="w-full px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                          placeholder="Search Alert"
                          value={searchInput}
                          onChange={(e) => {
                            setSearchInput(e.target.value);
                            setCurrentPage(1);
                          }}
                        />

                        <div
                          class="top-0 right-0  mr-4"
                          style={{
                            marginLeft: "0.25rem",
                            marginTop: "-1.7rem",
                          }}
                        >
                          <SearchIcon className="w-5 h-5" />
                        </div>
                      </div>
                      <div className="flex-grow flex justify-end items-center gap-3">
                        <BackButton
                          label={"Alert History"}
                          buttonClassName="w-full md:w-[180px]  border-errorBorder text-xs mb-3 rounded text-errorColor"
                          onClick={() => navigate("/app/alerts/alerts-history")}
                        ></BackButton>
                        <Button
                          label={"+ Add New Alert"}
                          buttonClassName="w-full md:w-[180px] text-xs mb-3 rounded"
                          onClick={() => {
                            setEdit(false);
                            if (permissions.create === 1) {
                              openAlert();
                            } else {
                              handleOpenInfoModal(true);
                              setMessage("Create permission not allowed");
                            }
                          }}
                        ></Button>
                      </div>
                    </div>
                  </div>
                  <div className="mt-3 mx-3">
                    <Table
                      columns={columns}
                      data={
                        Array.isArray(alertList?.data) ? alertList?.data : []
                      }
                      isLoading={isLoading}
                    />
                  </div>
                  {alertList?.data && alertList?.totalCount !== undefined && (
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        margin: "20px 0px 20px 20px",
                        width: "95%",
                      }}
                    >
                      <div className="flex ">
                        <div>
                          <ResultPerPageComponent
                            countPerPage={resultPerPage}
                            limit={limitPerPage}
                            handleLimitChange={handleLimitChange}
                          />
                        </div>

                        <div
                          style={{
                            display: "flex",
                            fontSize: "14px",
                            padding: "10px 0px 0px 10px",
                            color: theme.textColor.titleColor,
                          }}
                        >
                          {(currentPage - 1) * limitPerPage + 1} -{" "}
                          {Math.min(
                            limitPerPage * currentPage,
                            alertList?.totalCount
                          )}{" "}
                          of {alertList?.totalCount} rows
                        </div>
                      </div>
                      <Pagination
                        className="pagination-bar"
                        currentPage={currentPage}
                        totalCount={alertList?.totalCount}
                        pageSize={limitPerPage}
                        onPageChange={(page) => {
                          handlePageChange(page);
                        }}
                      />
                    </div>
                  )}{" "}
                </>
              )}
            </div>
          </div>
        </div>

        <DeleteDialog
          show={deleteDialog}
          onHide={() => setDeleteDialog(false)}
          onConfirm={() => {
            setIsLoading(true);
            alertServices
              .deleteAlert(id)
              .then((res) => {
                setDeleteDialog(false);
                // console.log("serach inside delete", searchStr);
                //setSearchStr("");
                //refreshData(currentPage, "");
                setCurrentPage(1);
                refetch();
                setSuccessDialog(true);
                setMessage("Alert deleted successfully");
              })

              .catch((error) => {
                setDeleteDialog(false);
                setErrorDialog(true);
                setMessage("Something went wrong ! Try Again");
              })
              .finally(() => {
                setIsLoading(false);
                //setSearchStr("");
              });
          }}
          title={"Are you sure to delete the Alert ?"}
          isLoading={isLoading}
        />
        <SuccessDialog
          show={suceessDialog}
          onHide={() => setSuccessDialog(false)}
          message={message}
        />
        <ErrorDialog
          show={errorDialog}
          onHide={() => setErrorDialog(false)}
          message={message}
        />
        <InfoModal
          icon={<Alert className="w-10 h-10 " />}
          show={showAlertConfirmation}
          onHide={handleCloseInfoModal}
          message={message}
        />
      </div>
    </>
  );
};

export default AlertsList;
