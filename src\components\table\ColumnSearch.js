import React from "react";
import { SearchIcon } from "../../icons";

function ColumnSearch({ column }) {
  const { filterValue, setFilter } = column;

  const isSpecialColumn =
    column.id === "Date" ||
    column.id === "Source Operator ID" ||
    column.id === "Source Operator" ||
    column.id === "Source Hub" ||
    column.id === "Destination MCC" ||
    column.id === "Total Submission" ||
    column.id === "Submission Success" ||
    column.id === "Submission Error" ||
    column.id === "Delivery Success" ||
    column.id === "Failure" ||
    column.id === "Delivery Failure" ||
    column.id === "Success(%)" ||
    column.id === "Failure(%)" ||
    column.id === "Protocol" ||
    column.id === "Source Protocol" ||
    column.id === "Traffic Type" ||
    column.id === "Interface Type" ||
    column.id === "Source MCC" ||
    column.id === "Source MNC" ||
    column.id === "Cost (EUR)" ||
    column.id === "Unit Rate" ||
    column.id === "Messaging Fee" ||
    column.id === "Currency" ||
    column.id === "Message Delivered" ||
    column.id === "Termination Rate" ||
    column.id === "Revenue(EUR)" ||
    column.id === "Operator ID" ||
    column.id === "Operator CC" ||
    column.id === "Operator MCC" ||
    column.id === "Operator MNC" ||
    column.id === "Source Country";

  const columnData =
    column.id === "Avg Submission Latency(sec)" ||
    column.id === "Avg Delivery Latency(sec)" ||
    column.id === "Destination Operator Name" ||
    column.id === "Destination Country Name" ||
    column.id === "Destination Country Code" ||
    column.id === "Submission Efficiency(%)" ||
    column.id === "Destination Operator ID" ||
    column.id === "Next Hop Success Percent" ||
    column.id === "Destination Op Visited ID" ||
    column.id === "Source Customer Name" ||
    column.id === "Customer/Supplier Traffic Type" ||
    column.id === "Destination MNP Cost (EUR)" ||
    column.id === "Source MNP Success Count" ||
    column.id === "Source MNP Failure Count" ||
    column.id === "Src MNP Average Cost (Euro)" ||
    column.id === "Source MNP Dip Cost (Euro)" ||
    column.id === "Dest MNP Success Count" ||
    column.id === "Dest MNP Failure Count" ||
    column.id === "Dest MNP Average Cost (Euro)" ||
    column.id === "Dest MNP Dip Cost (Euro)" ||
    column.id === "Dest MNP Currency" ||
    column.id === "Destination Operator Code" ||
    column.id === "Destination Operator (Roaming)" ||
    column.id === "Dest Country Name Roaming";

  return (
    <div className="flex items-center  py-2 w-full relative">
      <SearchIcon className="text-gray-400 mr-4 ml-1 w-4 h-4 absolute" />
      <input
        placeholder="Search"
        value={filterValue || ""}
        onChange={(e) => setFilter(e.target.value)}
        className={`border border-gray-300  text-xs px-6 h-8 ${
          isSpecialColumn
            ? "max-w-[120px]"
            : columnData
            ? "max-w-[170px]"
            : "max-w-[135px]"
        }`}
      />
    </div>
  );
}

export default ColumnSearch;
