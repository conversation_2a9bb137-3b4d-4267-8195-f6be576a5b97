import React from "react";
import { Link } from "react-router-dom";
import LoginForm from "../components/Forms/LoginForm";

function Login() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Link
        to="/"
        className="pl-8 pr-8 text-xl font-bold text-gray-800 bg-white"
      >
        <div className="bg-logo h-[92px] w-[150px] bg-contain bg-no-repeat" />
      </Link>
      <div className="flex flex-1 h-full items-center lg:mt-0">
        <div className="flex-1 h-full max-w-xl mx-auto overflow-hidden bg-white rounded-lg shadow-xl">
          <main className="flex items-center justify-center p-6 sm:p-12 md:flex-row">
            <div className="w-full">
              <h1
                className="mb-4 text-2xl font-sans font-semibold text-gray-700"
                style={{ textAlign: "center" }}
              >
                Welcome User!
                <hr />
              </h1>
              <br />

              <LoginForm />

              <hr className="my-8" />
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}

export default Login;
