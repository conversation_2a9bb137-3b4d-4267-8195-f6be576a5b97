import dayjs from "dayjs";
import React, { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
// import { ClickAwayListener } from "@mui/base";

const ReactDateRange = ({
  setSelectedFilter,
  setSelectedRange,
  reset,
  setReset,
}) => {
  const [open, setOpen] = useState(false);
  const [dateRange, setDateRange] = useState([null, null]);
  const [startDate, endDate] = dateRange;

  const handleOpenClick = (e) => {
    e.stopPropagation();
    setOpen(!open);
  };

  const handleDateChange = (dates) => {
    //console.log("dates", dates);
    setDateRange(dates);
  };

  const isApplyDisabled = startDate === null || endDate === null;

  const handleApply = () => {
    setSelectedRange(false);
    if (dateRange[0] !== null && dateRange[1] !== null) {
      const formattedStartDate = dayjs(dateRange[0]).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      const formattedEndDate = dayjs(dateRange[1]).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      setSelectedFilter({
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      });
    }

    setOpen(false); // Close the date picker
  };

  const CustomDatePickerInput = ({ value, onClick }) => (
    <button className="border-none bg-white text-black" onClick={onClick}>
      {value}
    </button>
  );

  return (
    <div className="relative flex items-center">
      <div className="absolute top-5 -right-40 z-50 w-full md:w-[500px]">
        <div className="border border-outerBorder rounded-sm bg-white ">
          <div className="mt-3 ml-2">
            <DatePicker
              customInput={<CustomDatePickerInput />}
              isOutsideRange={() => false}
              startDate={startDate}
              endDate={endDate}
              maxDate={new Date()}
              monthsShown={2}
              selectsRange
              inline
              style={{ backgroundColor: "white", display: "inline-flex" }}
              onChange={handleDateChange}
            />
          </div>
          <div className="flex flex-grow justify-end mr-3">
            <button
              className={`mt-2 w-[167px] h-9 bg-bgSecondary text-sm text-white rounded-[3px] mb-2 ${
                isApplyDisabled ? "opacity-50 cursor-not-allowed" : ""
              }`}
              onClick={handleApply}
              disabled={isApplyDisabled}
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReactDateRange;
