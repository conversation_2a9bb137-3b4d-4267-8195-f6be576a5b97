import React, { useContext, useState, useEffect } from "react";
import { Form, Formik, FieldArray } from "formik";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Select from "../components/FormsUI/Select";
import BackButton from "../components/Button/Button";
import Button from "../components/Button/OutlinedButton";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import { getPanelProperties } from "../services/dropdown-api";
import { useQuery } from "react-query";
import TextFieldWrapper from "../components/FormsUI/TextField";
import CustomDropDown from "../components/Dropdown/CustomeDropdown";
import * as Yup from "yup";
import {
  supplierList,
  customerList,
  destinationCountry,
  destinationName,
} from "../services/dropdown-api";
import { logicalOperator } from "../common/constants";
import SelectedFilters from "../common/selectedFilters";
import InfoModal from "../components/modals/InfoModal";
import {
  customerInterface,
  customerProtocol,
  supplierProtocol,
} from "../common/constants";
import { DataContext } from "../context/DataContext";
import { reportService } from "../services/staticreport.service";
import { filterLogicalOperators } from "../utils/formHelpers";

function PieChartFormTwo({ editDetail, configApiData }) {
  const [valueData, setValueData] = useState([]);
  const [customerListData, setCustomerListData] = useState([]);
  const [derivedFieldList, setDerivedFieldList] = useState([]);
  const [fieldList, setFieldList] = useState([]);
  const [response, setResponse] = useState("");
  const [conditionList, setConditionList] = useState([]);
  const [optionDataList, setOptionDataList] = useState([]);
  const [destnationNameList, setDestinationNameList] = useState([]);
  const [destinationCountryList, setDestinationCountryList] = useState([]);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [supplierInterconnect, setSupplierInterconnect] = useState([]);
  const [customerInterconnect, setCustomerInterconnect] = useState([]);
  const [message, setMessage] = useState("");
  const [msg, setMsg] = useState("");
  const {
    handleNextClick,
    setFormData,
    formData,
    handlePrevClick,
    handleNextClickStep,
    handlePrevStep,
  } = useContext(multiStepFormContext);
  const {
    previousValue,
    setCustomerBindList,
    customerBindList,
    supplierBindList,
    setSupplierBindList,
  } = useContext(DataContext);

  const [customerBillingLogic, setCustomerBillingLogic] = useState([]);
  const [supplierBillingLogic, setSupplierBillingLogic] = useState([]);
  const [lcrDataList, setLCRDataList] = useState([]);
  const [specLCRDataList, setSpecLCRDataList] = useState([]);
  const [sourcePrime, setSourcePrime] = useState([]);
  const [destinationPrime, setDestinationPrime] = useState([]);
  const [supplierInterfaceType, setSupplierInterfaceType] = useState([]);
  const [cdrStatus, setCDRStatus] = useState([]);

  useQuery(["/supplierData"], supplierList, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const supplierListData =
        res?.data
          ?.map((type) => ({ value: type.name, label: type.name }))
          .filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.label === item.label)
          ) || [];
      const supplierBindList =
        res?.data?.map((type) => ({
          value: type.supplier_bind,
          label: type.supplier_bind,
        })) || [];
      const supplierBillingLogic = res?.data
        ? [
            ...new Map(
              res.data.map(({ billing_logic }) => [
                billing_logic,
                { value: billing_logic, label: billing_logic },
              ])
            ).values(),
          ]
        : [];

      const supplierInterfaceType = res?.data
        ? [
            ...new Map(
              res.data.map(({ interface_type }) => [
                interface_type,
                { value: interface_type, label: interface_type },
              ])
            ).values(),
          ]
        : [];
      const supplierInterconnect = res?.data
        ? [
            ...new Map(
              res.data.map(({ ECPaaS }) => [
                ECPaaS,
                { value: ECPaaS, label: ECPaaS },
              ])
            ).values(),
          ]
        : [];

      setSupplierInterconnect(supplierInterconnect);
      setValueData(supplierListData);
      setSupplierBindList(supplierBindList);
      setSupplierInterfaceType(supplierInterfaceType);
      setSupplierBillingLogic(supplierBillingLogic);
    },
  });

  useQuery(["/customerData"], customerList, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const customerListData =
        res?.data
          ?.map((type) => ({ value: type.name, label: type.name }))
          .filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.label === item.label)
          ) || [];

      const customerBindList =
        res?.data?.map((type) => ({
          value: type.customer_bind,
          label: type.customer_bind,
        })) || [];

      const customerBillingLogic = res?.data
        ? [
            ...new Map(
              res.data.map(({ billing_logic }) => [
                billing_logic,
                { value: billing_logic, label: billing_logic },
              ])
            ).values(),
          ]
        : [];
      const customerInterconnect = res?.data
        ? [
            ...new Map(
              res.data.map(({ ECPaaS }) => [
                ECPaaS,
                { value: ECPaaS, label: ECPaaS },
              ])
            ).values(),
          ]
        : [];

      setCustomerInterconnect(customerInterconnect);
      setCustomerListData(customerListData);
      setCustomerBindList(customerBindList);
      setCustomerBillingLogic(customerBillingLogic);
    },
  });

  useQuery(["sourcePrime"], reportService.getSourcePrime, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const uniqueSourcePrime = [
        ...new Set(res?.data?.map((type) => type.description)),
      ].map((description) => ({
        value: description,
        label: description,
      }));

      setSourcePrime(uniqueSourcePrime);
    },
    onError: () => {
      setSourcePrime([]);
    },
  });

  useQuery(["lcrDataList"], reportService.getLCRData, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const data = res?.data || [];

      setLCRDataList(
        data.map((type) => ({
          label: type.lcr_name,
          value: type.lcr_name,
        }))
      );
      const uniqueLcrTypes = Array.from(
        new Set(data.map((type) => type.lcr_type).filter(Boolean))
      ).map((type) => ({
        value: type,
        label: type,
      }));

      setSpecLCRDataList(uniqueLcrTypes);
    },
    onError: () => {
      setLCRDataList([]);
      setSpecLCRDataList([]);
    },
  });

  useQuery(["cdrStatusList"], reportService.getCDRStatus, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      setCDRStatus(
        res?.data?.map((type) => ({
          value: type.description,
          label: type.description,
        }))
      );
    },
    onError: () => {
      setCDRStatus([]);
    },
  });

  useQuery(["destinationPrime"], reportService.getDestinationPrime, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const uniqueDestinationPrime = [
        ...new Set(res?.data?.map((type) => type.description)),
      ].map((description) => ({
        value: description,
        label: description,
      }));

      setDestinationPrime(uniqueDestinationPrime);
    },
    onError: () => {
      setDestinationPrime([]);
    },
  });

  useQuery(["/destinationCountryData"], destinationCountry, {
    refetchOnWindowFocus: false,

    onSuccess: (res) => {
      setDestinationCountryList(
        res?.data?.map((type) => ({ value: type, label: type })) || []
      );
    },
  });

  useQuery(["/destinationNameData"], destinationName, {
    onSuccess: (res) => {
      setDestinationNameList(
        res?.data?.map((type) => ({ value: type, label: type })) || []
      );
    },
  });

  const { data: panelProperty, isLoading } = useQuery(
    ["/panelproperty", formData?.type],
    getPanelProperties,
    {
      refetchOnWindowFocus: false,

      onSuccess: (res) => {
        //console.log("res", res);
        const fieldData =
          res?.data?.filters?.map((type) => {
            return {
              value: type.field,
              label: type.field,
            };
          }) || [];

        setFieldList(fieldData);

        const tableFields =
          res?.data?.columns?.derivedFields?.map((type) => {
            return {
              value: type,
              label: type,
            };
          }) || [];
        setDerivedFieldList(tableFields);
      },
    }
  );

  useEffect(() => {
    if (panelProperty && panelProperty?.data && response !== "") {
      const selectedCondition =
        panelProperty?.data?.filters?.find((field) => field?.field === response)
          ?.condition || [];
      const conditionType = selectedCondition.map((type) => ({
        value: type,
        label: type,
      }));
      setConditionList(conditionType);
    }
  }, [panelProperty, response]);

  useEffect(() => {
    let conditions = [];
    if (formData && formData.conditions) {
      conditions = formData.conditions;
    } else if (editDetail && editDetail.filters) {
      conditions = editDetail.filters.map((condition) => ({
        type1: condition.field,
      }));
    }

    const dataMap = {
      "Customer Name": customerListData,
      "Supplier Name": valueData,
      "Destination Operator Country": destinationCountryList,
      "Destination Operator Name": destnationNameList,
      "Customer Interface": customerInterface,
      "Customer Protocol": customerProtocol,
      "Supplier Protocol": supplierProtocol,
      "Source Operator Name": destnationNameList,
      "Customer Bind": customerBindList,
      "Source Prime": sourcePrime,
      "Destination Prime": destinationPrime,
      "Supplier Bind": supplierBindList,
      "Supplier Interface Type": supplierInterfaceType,
      "Customer Billing Logic": customerBillingLogic,
      "Supplier Billing Logic": supplierBillingLogic,
      "LCR Name": lcrDataList,
      "Spec LCR": specLCRDataList,
      Status: cdrStatus,
      "Customer Interconnect": customerInterconnect,
      "Supplier Interconnect": supplierInterconnect,
    };

    const newOptionDataList = conditions.map(({ type1 }) => {
      setResponse(type1);
      return dataMap[type1] && dataMap[type1].length > 0 ? dataMap[type1] : [];
    });
    setOptionDataList(newOptionDataList);
  }, [
    formData,
    editDetail,
    customerListData,
    valueData,
    destnationNameList,
    destinationCountryList,
    customerInterface,
    customerProtocol,
    supplierProtocol,
    customerBindList,
    sourcePrime,
    destinationPrime,
    supplierBindList,
    supplierInterfaceType,
    customerBillingLogic,
    supplierBillingLogic,
    lcrDataList,
    specLCRDataList,
    cdrStatus,
  ]);
  const validationSchema = Yup.object().shape({
    derivedField: Yup.array()
      .min(1, "Derived Field is required")
      .required("Derived Field is required"),
    conditions: Yup.array().of(
      Yup.object().shape({
        type1: Yup.string(),
        type2: Yup.string(),
        type3: Yup.string()
          .when("type1", {
            is: "Destination MCC",
            then: (schema) =>
              schema
                .matches(/^\d+$/, "Destination MCC must be a number") // number only
                .max(3, "Destination MCC must be between 1 to 3 digits")
                .test(
                  "not-zero",
                  "Destination MCC cannot be 000",
                  (value) => value !== "000"
                ),
          })
          .when("type1", {
            is: "Destination MNC",
            then: (schema) =>
              schema
                .matches(/^\d+$/, "Destination MNC must be a number") // number only
                .max(3, "Destination MNC must be between 1 to 3 digits"),
          }),
        type4: Yup.string(),
      })
    ),
  });

  const defaultDerivedField = (
    formData.derivedField ||
    editDetail?.dataColumns?.derivedFields ||
    []
  ).filter((field) =>
    panelProperty?.data?.columns?.derivedFields?.includes(field)
  );

  const handleSubmit = (values) => {
    const multipleCondition = configApiData?.FIELDS_WITH_MULTIPLE_CONDITIONS;
    const multipleConditionType1 =
      values.conditions.find((condition) =>
        multipleCondition?.includes(condition.type1)
      )?.type1 || "";

    const hasMultipleConditionField = multipleConditionType1 !== "";
    const numConditions = values.conditions.length;

    if (hasMultipleConditionField) {
      if (numConditions < 2) {
        setShowAlertConfirmation(true);
        setMsg(`Oops! ${multipleConditionType1} alone cannot display report.`);
        setMessage(
          `We need one more filter with ${multipleConditionType1} to generate the report.`
        );
        return;
      }

      const lastCondition = values.conditions[numConditions - 1];
      const isLastConditionEmpty =
        !lastCondition.type1 || !lastCondition.type2 || !lastCondition.type3;

      if (isLastConditionEmpty && numConditions === 2) {
        setShowAlertConfirmation(true);
        setMsg("");
        setMessage("All conditions must be filled out");
        return;
      }
    }

    const anyConditionEmptyType = values.conditions.find(
      (condition) =>
        condition.type1 !== "" &&
        (condition.type2 === "" || condition.type3 === "")
    );

    if (anyConditionEmptyType) {
      setShowAlertConfirmation(true);
      setMsg("");
      setMessage("All conditions must be filled out.");
      return;
    }

    handleNextClick();
    handleNextClickStep();
    setFormData({ ...formData, ...values });
  };
  const initialValues = {
    conditions: [{ type1: "", type2: "", type3: "", type4: "" }],
    derivedField: defaultDerivedField,
  };
  if (
    formData.type !== "" &&
    editDetail.visualizationType !== undefined &&
    formData.type !== editDetail.visualizationType &&
    !previousValue
  ) {
    initialValues.conditions = [{ type1: "", type2: "", type3: "", type4: "" }];
  } else if (
    !formData.conditions &&
    editDetail &&
    editDetail.filters &&
    editDetail.filters.length > 0
  ) {
    initialValues.conditions = editDetail.filters.map((filter) => ({
      type1: filter.field,
      type2: filter.condition,
      type3: filter.value,
      type4: filter.operator,
    }));
  } else if (formData && formData.conditions && previousValue) {
    initialValues.conditions = formData.conditions;
  }
  return (
    <>
      <div>
        {isLoading ? (
          <div className="flex justify-center items-center font-semibold">
            Loading...
          </div>
        ) : (
          <Formik
            initialValues={initialValues}
            enableReinitialize={true}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ values, errors, status, touched, setFieldValue }) => (
              <Form>
                <SelectedFilters
                  values={values}
                  fieldList={fieldList}
                  conditionList={values.conditions.map(
                    (condition) => condition.type2
                  )}
                  logicalOperator={logicalOperator}
                />

                <div className="md:flex gap-5 border border-outerBorder mx-12 pt-4 pl-4">
                  <div className="flex flex-col items-start gap-3">
                    <FieldArray
                      name="conditions"
                      render={({ insert, remove, push }) => {
                        return (
                          <>
                            <div className="flex flex-col md:flex-row items-center justify-center gap-3">
                              <div>
                                {values.conditions.map((condition, index) => {
                                  let conditionMatched = false;

                                  return (
                                    <div
                                      key={index}
                                      className="flex flex-col md:flex-row gap-5 items-center justify-center"
                                    >
                                      <div className="flex flex-col  mb-4">
                                        <InputLabel label={"Field"} />
                                        <Select
                                          className="w-full  md:w-[200px] lg:w-[210px]"
                                          name={`conditions.${index}.type1`}
                                          options={fieldList}
                                          placeholder={"Select Field"}
                                          onChange={(selectedOption) => {
                                            const selectedType1Value =
                                              selectedOption.value;
                                            setFieldValue(
                                              `conditions.${index}.type1`,
                                              selectedType1Value
                                            );
                                            setResponse(selectedType1Value);

                                            const dataMap = {
                                              "Customer Name": customerListData,
                                              "Supplier Name": valueData,
                                              "Destination Operator Country":
                                                destinationCountryList,
                                              "Destination Operator Name":
                                                destnationNameList,
                                              "Customer Bind": customerBindList,
                                              "Customer Interface":
                                                customerInterface,
                                              "Customer Protocol":
                                                customerProtocol,
                                              "Supplier Protocol":
                                                supplierProtocol,
                                              "Source Operator Name":
                                                destnationNameList,
                                              "Source Prime": sourcePrime,
                                              "Destination Prime":
                                                destinationPrime,
                                              "Supplier Bind": supplierBindList,
                                              "Supplier Interface Type":
                                                supplierInterfaceType,
                                              "Customer Billing Logic":
                                                customerBillingLogic,
                                              "Supplier Billing Logic":
                                                supplierBillingLogic,
                                              "LCR Name": lcrDataList,
                                              "Spec LCR": specLCRDataList,
                                              Status: cdrStatus,
                                              "Customer Interconnect":
                                                customerInterconnect,
                                              "Supplier Interconnect":
                                                supplierInterconnect,
                                            };

                                            const updatedOptionData =
                                              dataMap[selectedType1Value] || [];

                                            const newOptionDataList = [
                                              ...optionDataList,
                                            ];
                                            newOptionDataList[index] =
                                              updatedOptionData;
                                            setOptionDataList(
                                              newOptionDataList
                                            );
                                          }}
                                          isSearchable={true}
                                        />
                                      </div>
                                      <div className="flex flex-col mb-4">
                                        <InputLabel label={"Condition"} />
                                        {!values.conditions[index].type1 ? (
                                          <Select
                                            className="w-full  md:w-[200px] lg:w-[210px]"
                                            name={`conditions.${index}.type2`}
                                            options={[]}
                                            placeholder={"Select Condition"}
                                            isDisabled={
                                              !values.conditions[index].type1 ||
                                              values.conditions[index].type1 ===
                                                ""
                                            }
                                          />
                                        ) : (
                                          panelProperty?.data?.filters &&
                                          panelProperty.data.filters
                                            .filter(
                                              (field) =>
                                                field.field ===
                                                values.conditions[index].type1
                                            )
                                            .map((filter, filterIndex) => {
                                              const selectedCondition =
                                                filter.condition || [];
                                              const conditionType =
                                                selectedCondition.map(
                                                  (type) => ({
                                                    value: type,
                                                    label: type,
                                                  })
                                                );
                                              return (
                                                <Select
                                                  key={filterIndex}
                                                  className="w-full  md:w-[200px] lg:w-[210px]"
                                                  name={`conditions.${index}.type2`}
                                                  options={conditionType}
                                                  placeholder={
                                                    "Select Condition"
                                                  }
                                                  isDisabled={
                                                    !values.conditions[index]
                                                      .type1 ||
                                                    values.conditions[index]
                                                      .type1 === ""
                                                  }
                                                />
                                              );
                                            })
                                        )}
                                      </div>

                                      <div className="flex flex-col mb-4">
                                        <InputLabel label={"Value"} />

                                        {panelProperty?.data?.filters.map(
                                          (filter, filterIndex) => {
                                            const isDropdown =
                                              filter.datatype === "dropdown";
                                            const isMatched =
                                              values.conditions[index].type1 ===
                                              filter.field;

                                            if (isMatched && isDropdown) {
                                              conditionMatched = true;
                                              return (
                                                <Select
                                                  key={filter.field}
                                                  className="w-full  md:w-[200px] lg:w-[210px]"
                                                  name={`conditions.${index}.type3`}
                                                  options={
                                                    optionDataList[index]
                                                  }
                                                  placeholder={"Select Value"}
                                                  isDisabled={
                                                    !values.conditions[index]
                                                      .type1 ||
                                                    values.conditions[index]
                                                      .type1 === "" ||
                                                    !values.conditions[index]
                                                      .type2 ||
                                                    values.conditions[index]
                                                      .type2 === ""
                                                  }
                                                  isSearchable={true}
                                                />
                                              );
                                            } else if (
                                              isMatched &&
                                              !isDropdown
                                            ) {
                                              conditionMatched = true;
                                              return (
                                                <TextFieldWrapper
                                                  key={filter.field}
                                                  className="w-full  md:w-[200px] lg:w-[210px]"
                                                  name={`conditions.${index}.type3`}
                                                  placeholder={"Enter Value"}
                                                  disabled={
                                                    !values.conditions[index]
                                                      .type1 ||
                                                    values.conditions[index]
                                                      .type1 === "" ||
                                                    !values.conditions[index]
                                                      .type2 ||
                                                    values.conditions[index]
                                                      .type2 === ""
                                                  }
                                                />
                                              );
                                            }
                                          }
                                        )}

                                        {!conditionMatched && (
                                          <TextFieldWrapper
                                            className="w-full  md:w-[200px] lg:w-[210px] "
                                            name={`conditions.${index}.type3`}
                                            placeholder={"Enter Value"}
                                            disabled={
                                              !values.conditions[index].type1 ||
                                              values.conditions[index].type1 ===
                                                "" ||
                                              !values.conditions[index].type2 ||
                                              values.conditions[index].type2 ===
                                                ""
                                            }
                                          />
                                        )}
                                      </div>
                                      <div className="flex flex-col mb-4">
                                        <InputLabel label={"Operator"} />
                                        <Select
                                          className="w-full md:w-[130px] lg:w-[180px]"
                                          name={`conditions.${index}.type4`}
                                          options={filterLogicalOperators(
                                            logicalOperator,
                                            values,
                                            index
                                          )}
                                          placeholder={"Select Logic"}
                                          isDisabled={
                                            !values.conditions[index].type1 ||
                                            values.conditions[index].type1 ===
                                              ""
                                          }
                                        />
                                      </div>

                                      <div className="">
                                        <button
                                          type="button"
                                          className="rounded-2xl w-8 h-8 bg-gray-300 p-2 flex items-center justify-center"
                                          onClick={() => {
                                            if (
                                              values.conditions.length === 1
                                            ) {
                                              setFieldValue(
                                                `conditions.${index}.type1`,
                                                ""
                                              );
                                              setFieldValue(
                                                `conditions.${index}.type2`,
                                                ""
                                              );
                                              setFieldValue(
                                                `conditions.${index}.type3`,
                                                ""
                                              );
                                              setFieldValue(
                                                `conditions.${index}.type4`,
                                                ""
                                              );
                                            } else {
                                              remove(index);
                                              const newOptionDataList = [
                                                ...optionDataList,
                                              ];
                                              newOptionDataList.splice(
                                                index,
                                                1
                                              );
                                              setOptionDataList(
                                                newOptionDataList
                                              );
                                            }
                                          }}
                                        >
                                          <span className="text-xl font-bold">
                                            -
                                          </span>
                                        </button>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                            <div className="flex-grow flex justify-end">
                              <button
                                type="button"
                                className="w-8 h-8 p-2 "
                                onClick={() => {
                                  const allFieldsFilled =
                                    values.conditions.every(
                                      (condition) =>
                                        condition.type1 &&
                                        condition.type2 &&
                                        condition.type3 &&
                                        condition.type4
                                    );
                                  if (allFieldsFilled) {
                                    push({
                                      type1: "",
                                      type2: "",
                                      type3: "",
                                      type4: "",
                                    });
                                  }
                                }}
                                disabled={
                                  !values.conditions.every(
                                    (condition) =>
                                      condition.type1 &&
                                      condition.type2 &&
                                      condition.type3 &&
                                      condition.type4
                                  ) ||
                                  values.conditions.some(
                                    (condition) => condition.type4 === "no"
                                  )
                                }
                              >
                                <span className="text-sm font-bold">+Add</span>
                              </button>
                            </div>
                          </>
                        );
                      }}
                    />
                  </div>
                </div>
                <div className="mt-10 mx-16 lg:mx-auto lg:w-1/2 ">
                  <div className="relative border border-outerBorder p-8">
                    <div className="absolute -top-3 px-2 bg-white">
                      <InputLabel label={"Add Y axis"} isMandatory={true} />
                    </div>
                    <div className="flex flex-col items-center gap-3 mx-3 ">
                      <div className="flex flex-col">
                        <InputLabel label={"Derived Field"} />
                        <CustomDropDown
                          btnWidth="w-full lg:w-[400px]"
                          data={derivedFieldList}
                          btnName={"Select Derived Field"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("derivedField", selectedDetail);
                          }}
                          defaultSelectedData={defaultDerivedField}
                          optionDataList={configApiData?.MAX_Y_AXIS_FIELDS}
                        />
                        {errors.derivedField && touched.derivedField && (
                          <div className="text-red-500 text-xs">
                            {errors.derivedField}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex-grow flex justify-end items-center mx-20 mt-20 gap-4">
                  <BackButton
                    label={"Back"}
                    buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded"
                    onClick={() => {
                      handlePrevClick();
                      handlePrevStep();
                    }}
                  ></BackButton>
                  <Button
                    block="true"
                    type="submit"
                    label="Next"
                    value="submit"
                    buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded "
                  ></Button>
                </div>
              </Form>
            )}
          </Formik>
        )}
      </div>
      <InfoModal
        show={showAlertConfirmation}
        onHide={() => {
          setShowAlertConfirmation(false);
        }}
        message={message}
        msg={msg}
      />
    </>
  );
}

export default PieChartFormTwo;
