import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Button,
} from "@mui/material";
import theme from "../tailwind-theme";

const CustomDialog = () => {
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // Hardcoded data for the table rows
  const tableData = [
    {
      supplier: "Supplier_4571",
      destination: "Destination_98654322",
      todaySubmission: 5733,
      yesterdaySubmission: 3424,
      volumeVariation: "10%",
      todayDLR: "10%",
      yesterdayDLR: "10%",
      dlrVariation: "10%",
    },
    {
      supplier: "Supplier_123456",
      destination: "Destination_98654322",
      todaySubmission: 5751,
      yesterdaySubmission: 3456,
      volumeVariation: "07%",
      todayDLR: "07%",
      yesterdayDLR: "07%",
      dlrVariation: "07%",
    },
    {
      supplier: "Supplier_1234XYZ",
      destination: "Destination_98654322",
      todaySubmission: 5767,
      yesterdaySubmission: 3333,
      volumeVariation: "04%",
      todayDLR: "04%",
      yesterdayDLR: "04%",
      dlrVariation: "04%",
    },
  ];

  const headerStyle = { backgroundColor: "#DC3833", color: "#fff" };
  const cellStyleBlue = { backgroundColor: "#E9F5FD" };
  const cellStyleGreen = { backgroundColor: "#D3F1CB" };

  return (
    <div>
      <Button variant="contained" onClick={handleClickOpen}>
        Open Dialog
      </Button>
      <Dialog open={open} onClose={handleClose} maxWidth="lg">
        <DialogTitle
          style={{
            backgroundColor: "#2A57C9",
            color: "#fff",
            textAlign: "center",
          }}
        >
          Customer1234_12:00 - 12:15
        </DialogTitle>
        <div>
          <Table>
            <TableHead>
              <TableRow>
                {[
                  "Supplier",
                  "Destination",
                  "Today's Submission",
                  "Yesterday's Submission",
                  "Volume Variation",
                  "Today DLR",
                  "Yesterday DLR",
                  "DLR Variation",
                ].map((header, index) => (
                  <TableCell key={index} style={headerStyle}>
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {tableData.map((row, index) => (
                <TableRow key={index}>
                  <TableCell>{row.supplier}</TableCell>
                  <TableCell>{row.destination}</TableCell>
                  <TableCell style={cellStyleBlue}>
                    {row.todaySubmission}
                  </TableCell>
                  <TableCell style={cellStyleBlue}>
                    {row.yesterdaySubmission}
                  </TableCell>
                  <TableCell style={cellStyleGreen}>
                    {row.volumeVariation}
                  </TableCell>
                  <TableCell>{row.todayDLR}</TableCell>
                  <TableCell>{row.yesterdayDLR}</TableCell>
                  <TableCell style={cellStyleGreen}>
                    {row.dlrVariation}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </Dialog>
    </div>
  );
};

export default CustomDialog;
