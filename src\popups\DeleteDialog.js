import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import { CloseIcon, TrashIcon } from "../icons";
import Button from "../components/Button/OutlinedButton";
import CancelButton from "../components/Button/Button";

export default function DeleteDialog({
  show,
  onHide,
  onConfirm,
  isLoading,
  title,
}) {
  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 411,
          maxHeight: 435,
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onHide}
      className="p-6"
    >
      <DialogTitle className="mt-2 flex justify-end">
        <CloseIcon className="w-2.5 h-2.5 cursor-pointer" onClick={onHide} />
      </DialogTitle>

      <div className="flex justify-center items-center mt-2">
        <TrashIcon className="w-10 h-10" />
      </div>

      <div className="text-base mt-4 text-center">{title}</div>

      <div>
        <div className="text-center mt-10 gap-5 mb-4">
          <CancelButton
            onClick={onHide}
            label={"No"}
            buttonClassName="w-[100px] h-9 text-xs"
          />
          <Button
            onClick={onConfirm}
            loading={isLoading}
            type="submit"
            label={"Yes"}
            buttonClassName="w-[100px] h-9 text-xs ml-5"
          />
        </div>
      </div>
    </Dialog>
  );
}
