import React, { useC<PERSON>back, useMemo, useReducer } from "react";
import { useQuery, useMutation } from "react-query";
import { useNavigate } from "react-router-dom";
import { CircularProgress, Dialog } from "@mui/material";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
// Services
import { getAll, getDashboardById } from "../../services/dashboard-api";
import {
  getAllCards,
  getCardsById,
  getAllUser,
} from "../../services/cards-preview-api";
import {
  previewPanel,
  getAll as getAllPanels,
  getId,
} from "../../services/panels-api";
// Components
import TabNavigation from "../../components/Navigation/TabNavigation";
import ResponsiveLayout from "./ResponsiveLayout";
import ResponsiveLayoutCard from "./ResponsiveLayoutCard";
import CardData from "./CardData";
import PanelVisualization from "./PanelVisualization";
import BreadcrumbNavigation from "../../components/BreadCrumps/BreadCrump";
import { CssTooltip } from "../../components/StyledComponent";
// Icons and Assets
import { InfoIcon, ExpandIcon, CloseIcon } from "../../icons";
import bgImage from "../../assets/img/useranalysis.png";
// Constants and Utils
import {
  ConditionDisplay,
  USER_ANALYSIS_COLORS as COLORS,
  USER_ANALYSIS_DEFAULT_DIMENSION as DEFAULT_DIMENSION,
  USER_ANALYSIS_VISUALIZATION_TYPES as VISUALIZATION_TYPES,
} from "../../common/constants";
import {
  formatDateRange,
  buildRequestData,
  getGridDimensions,
} from "../../utils/userAnalysisUtils";

// Initial state for useReducer
const initialState = {
  searchQuery: "",
  selectedName: "",
  openExpandDialog: false,
  userId: "",
  searchClicked: false,
  dashboardDetails: [],
  cardDetails: [],
  panelDetails: [],
  dashboardId: "",
  cardId: "",
  panelId: "",
  cardDroppedData: [],
  previewResponses: [],
  cardById: null,
  panelById: null,
  responseData: [],
  users: [],
  date: {},
  previewLoading: false,
  errMsg: "",
};

// Reducer for state management
const userAnalysisReducer = (state, action) => {
  switch (action.type) {
    case "SET_SEARCH_QUERY":
      return { ...state, searchQuery: action.payload };
    case "SET_USER_ID":
      return { ...state, userId: action.payload };
    case "SET_SELECTED_NAME":
      return { ...state, selectedName: action.payload };
    case "SET_SEARCH_CLICKED":
      return { ...state, searchClicked: action.payload };
    case "SET_DASHBOARD_ID":
      return { ...state, dashboardId: action.payload };
    case "SET_CARD_ID":
      return { ...state, cardId: action.payload };
    case "SET_PANEL_ID":
      return { ...state, panelId: action.payload };
    case "SET_USERS":
      return { ...state, users: action.payload };
    case "SET_DASHBOARD_DETAILS":
      return { ...state, dashboardDetails: action.payload };
    case "SET_CARD_DETAILS":
      return { ...state, cardDetails: action.payload };
    case "SET_PANEL_DETAILS":
      return { ...state, panelDetails: action.payload };
    case "SET_CARD_DROPPED_DATA":
      return { ...state, cardDroppedData: action.payload };
    case "SET_PREVIEW_RESPONSES":
      return {
        ...state,
        previewResponses:
          typeof action.payload === "function"
            ? action.payload(state.previewResponses)
            : action.payload,
      };
    case "SET_CARD_BY_ID":
      return { ...state, cardById: action.payload };
    case "SET_PANEL_BY_ID":
      return { ...state, panelById: action.payload };
    case "SET_RESPONSE_DATA":
      return { ...state, responseData: action.payload };
    case "SET_DATE":
      return { ...state, date: action.payload };
    case "SET_PREVIEW_LOADING":
      return { ...state, previewLoading: action.payload };
    case "SET_ERROR_MESSAGE":
      return { ...state, errMsg: action.payload };
    case "SET_EXPAND_DIALOG":
      return { ...state, openExpandDialog: action.payload };
    case "RESET_SEARCH_STATE":
      return {
        ...state,
        cardId: "",
        dashboardId: "",
        dashboardDetails: [],
        responseData: [],
        cardDetails: [],
        cardDroppedData: [],
        previewResponses: [],
        panelById: null,
        userId: "",
      };
    case "RESET_DASHBOARD_STATE":
      return {
        ...state,
        cardDroppedData: [],
        previewResponses: [],
        responseData: [],
        cardId: "",
        panelId: "",
        panelById: null,
      };
    case "RESET_CARD_STATE":
      return {
        ...state,
        dashboardId: "",
        responseData: [],
        cardDroppedData: [],
        previewResponses: [],
        panelId: "",
        panelById: null,
      };
    case "RESET_PANEL_STATE":
      return {
        ...state,
        cardId: "",
        dashboardId: "",
        cardDroppedData: [],
        previewResponses: [],
      };
    default:
      return state;
  }
};

// Initialize dayjs plugin once
dayjs.extend(customParseFormat);

function UserAnalysis() {
  const [state, dispatch] = useReducer(userAnalysisReducer, initialState);
  const navigate = useNavigate();

  // Memoized filtered data
  const filteredUsers = useMemo(() => {
    if (!state.users?.length || !state.searchQuery) return state.users || [];

    const query = state.searchQuery.toLowerCase();
    return state.users.filter((userData) =>
      userData.name.toLowerCase().includes(query)
    );
  }, [state.users, state.searchQuery]);

  // Event handlers with useCallback for performance
  const handleSearchInputChange = useCallback((value) => {
    dispatch({ type: "SET_SEARCH_QUERY", payload: value });
    dispatch({ type: "RESET_SEARCH_STATE" });
  }, []);

  const handleSelectName = useCallback((id, name) => {
    dispatch({ type: "SET_USER_ID", payload: id });
    dispatch({ type: "SET_SELECTED_NAME", payload: name });
    dispatch({ type: "SET_SEARCH_QUERY", payload: name });
    dispatch({ type: "SET_SEARCH_CLICKED", payload: false });
  }, []);

  const handleClickSearch = useCallback(() => {
    dispatch({ type: "SET_SEARCH_CLICKED", payload: true });
  }, []);

  const handleDashboard = useCallback((id) => {
    dispatch({ type: "SET_DASHBOARD_ID", payload: id });
    dispatch({ type: "RESET_DASHBOARD_STATE" });
  }, []);

  const handleCards = useCallback((id) => {
    dispatch({ type: "SET_CARD_ID", payload: id });
    dispatch({ type: "RESET_CARD_STATE" });
  }, []);

  const handlePanel = useCallback((id) => {
    dispatch({ type: "SET_PANEL_ID", payload: id });
    dispatch({ type: "RESET_PANEL_STATE" });
  }, []);

  const handleExpandDialog = useCallback((isOpen) => {
    dispatch({ type: "SET_EXPAND_DIALOG", payload: isOpen });
  }, []);

  // Convenience handlers for expand dialog
  const handleExpandOpen = useCallback(
    () => handleExpandDialog(true),
    [handleExpandDialog]
  );
  const handleExpandClose = useCallback(
    () => handleExpandDialog(false),
    [handleExpandDialog]
  );

  // Mutations - moved before callbacks that use them
  const { mutate: previewPanelAPIData, isLoading: loadingData } =
    useMutation(previewPanel);

  // Optimized preview API call function
  const previewAPICall = useCallback(
    (previewData) => {
      previewData.forEach((payload) => {
        const { formattedStart, formattedEnd } = formatDateRange(
          payload.panelDetails.timePeriod
        );
        const reqData = buildRequestData(
          payload,
          formattedStart,
          formattedEnd,
          state.userId
        );

        previewPanel({ reqData })
          .then(({ data }) => {
            dispatch({
              type: "SET_PREVIEW_RESPONSES",
              payload: (prevResponses) => {
                const newData = (prevResponses || []).filter(
                  ({ dimension }) => dimension.order !== payload.dimension.order
                );

                newData.push({
                  data: data.data,
                  panelData: {
                    ...reqData,
                    data:
                      reqData.visualizationType ===
                      VISUALIZATION_TYPES.MULTIAXIS_GRAPH
                        ? data
                        : data?.data,
                  },
                  dimension: payload.dimension,
                  id: payload.panelDetails.id,
                  timePeriod: payload.panelDetails.timePeriod,
                  count: data?.totalCount,
                  pageCount: data.count,
                });
                return newData;
              },
            });
          })
          .catch((error) => {
            dispatch({
              type: "SET_PREVIEW_RESPONSES",
              payload: (prevResponses) => {
                const newData = (prevResponses || []).filter(
                  ({ dimension }) => dimension.order !== payload.dimension.order
                );

                newData.push({
                  data: [],
                  panelData: { ...reqData, data: "failed" },
                  dimension: payload.dimension,
                  id: payload.panelDetails.id,
                  timePeriod: payload.panelDetails.timePeriod,
                  errorMessage: error?.response?.data?.message,
                });
                return newData;
              },
            });
          });
      });
    },
    [state.userId]
  );

  // Optimized previewById function
  const previewById = useCallback(
    (data) => {
      const { formattedStart, formattedEnd } = formatDateRange(data.timePeriod);
      const reqData = buildRequestData(
        data,
        formattedStart,
        formattedEnd,
        state.userId
      );

      previewPanelAPIData(
        { reqData },
        {
          onSuccess: ({ data: responseData }) => {
            dispatch({ type: "SET_RESPONSE_DATA", payload: responseData });
            dispatch({
              type: "SET_DATE",
              payload: { startDate: formattedStart, endDate: formattedEnd },
            });
            dispatch({ type: "SET_ERROR_MESSAGE", payload: "" });
          },
          onError: (error) => {
            dispatch({ type: "SET_RESPONSE_DATA", payload: [] });
            dispatch({
              type: "SET_ERROR_MESSAGE",
              payload: error?.response?.data?.message,
            });
            dispatch({
              type: "SET_DATE",
              payload: { startDate: formattedStart, endDate: formattedEnd },
            });
          },
        }
      );
    },
    [state.userId, previewPanelAPIData]
  );

  // API calls - kept original format as requested
  useQuery(["userList"], getAllUser, {
    onSuccess: ({ data }) => {
      const newData = data?.data?.map((x) => ({
        id: x.id,
        name: x.name,
        role: x.role.name,
      }));
      dispatch({ type: "SET_USERS", payload: newData });
    },
  });

  useQuery(["dashboadList", "", "", "", state.userId], getAll, {
    enabled: !!state.userId,
    onSuccess: ({ data }) => {
      dispatch({ type: "SET_DASHBOARD_DETAILS", payload: data?.data });
    },
  });

  useQuery(["CardDataList", "", "", "", state.userId], getAllCards, {
    enabled: !!state.userId,
    onSuccess: ({ data }) => {
      dispatch({ type: "SET_CARD_DETAILS", payload: data?.data });
    },
  });

  useQuery(["PanelList", "", "", "", state.userId], getAllPanels, {
    enabled: !!state.userId,
    onSuccess: ({ data }) => {
      dispatch({ type: "SET_PANEL_DETAILS", payload: data?.data });
    },
  });

  const { data: dashboardData, isLoading: dashboardLoading } = useQuery(
    ["getDashboardById", state.dashboardId, state.userId],
    getDashboardById,
    {
      enabled: !!state.dashboardId,
      onSuccess: ({ data }) => {
        const cardData = data?.cards.map((card) => ({
          reportField: card.cardDetails.reportField,
          value: card.cardDetails.value,
          order: card.order,
        }));
        dispatch({ type: "SET_CARD_DROPPED_DATA", payload: cardData });

        const panels = data?.panels?.map((panel) => {
          const dimension = {
            w: 6,
            h: 3,
            x: 0,
            y: 0,
            order: panel.order,
          };
          return { ...panel, dimension };
        });
        dispatch({ type: "SET_PREVIEW_RESPONSES", payload: panels });
        previewAPICall(panels);
      },
      refetchOnWindowFocus: false,
    }
  );

  useQuery(["getCardsById", state.cardId, state.userId], getCardsById, {
    enabled: !!state.cardId,
    onSuccess: ({ data }) => {
      dispatch({ type: "SET_CARD_BY_ID", payload: data });
    },
  });

  useQuery(["getPanelById", state.panelId], getId, {
    enabled: !!state.panelId,
    onSuccess: ({ data }) => {
      dispatch({ type: "SET_PANEL_BY_ID", payload: data });
      previewById(data);
    },
    refetchOnWindowFocus: false,
  });

  // Memoized computations
  const getGridData = useCallback((index, dimension) => {
    return getGridDimensions(dimension);
  }, []);

  const showEmptyDashboard = useMemo(() => {
    // Only show empty message if dashboard has loaded and the API response contains empty arrays
    if (dashboardLoading || !dashboardData?.data) {
      return false;
    }

    const cards = dashboardData.data.cards || [];
    const panels = dashboardData.data.panels || [];

    return cards.length === 0 && panels.length === 0;
  }, [dashboardLoading, dashboardData]);

  const showPanelVisualization = useMemo(() => {
    return !loadingData && state.responseData && state.panelById;
  }, [loadingData, state.responseData, state.panelById]);

  // Memoized navigation sections data
  const navigationSections = useMemo(() => {
    if (
      !state.userId ||
      !state.dashboardDetails ||
      !state.cardDetails ||
      !state.panelDetails
    ) {
      return [];
    }

    return [
      {
        id: "dashboard",
        title: "Dashboard",
        count: state.dashboardDetails?.length || 0,
        content: (
          <div className="space-y-1">
            {state.dashboardDetails && state.dashboardDetails?.length > 0 ? (
              state.dashboardDetails.map((details, index) => (
                <div
                  className={`p-1 rounded-md cursor-pointer transition-colors duration-200 ${
                    state.dashboardId === details.id
                      ? "bg-blue-100 border border-blue-300"
                      : "hover:bg-gray-100"
                  }`}
                  key={details.id}
                  onClick={() => {
                    handleDashboard(details.id);
                  }}
                >
                  <span className="text-sm text-gray-800 font-medium">
                    {details.name}
                  </span>
                  {index !== state.dashboardDetails.length - 1 && (
                    <div className="border-b border-panelBorder mt-1 mb-1"></div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4 text-sm">
                No records to display
              </div>
            )}
          </div>
        ),
      },
      {
        id: "cards",
        title: "Cards",
        count: state.cardDetails?.length || 0,
        content: (
          <div className="space-y-1">
            {state.cardDetails && state.cardDetails.length > 0 ? (
              state.cardDetails.map((details, index) => (
                <div
                  className={`p-1 rounded-md cursor-pointer transition-colors duration-200 ${
                    state.cardId === details.id
                      ? "bg-blue-100 border border-blue-300"
                      : "hover:bg-gray-100"
                  }`}
                  key={details.id}
                  onClick={() => {
                    handleCards(details.id);
                  }}
                >
                  <span className="text-sm text-gray-800 font-medium">
                    {details.name}
                  </span>
                  {index !== state.cardDetails.length - 1 && (
                    <div className="border-b border-panelBorder mt-1 mb-1"></div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4 text-sm">
                No records to display
              </div>
            )}
          </div>
        ),
      },
      {
        id: "panels",
        title: "Panels",
        count: state.panelDetails?.length || 0,
        content: (
          <div className="space-y-1">
            {state.panelDetails && state.panelDetails?.length > 0 ? (
              state.panelDetails.map((details, index) => (
                <div
                  className={`p-1 rounded-md cursor-pointer transition-colors duration-200 ${
                    state.panelId === details.id
                      ? "bg-blue-100 border border-blue-300"
                      : "hover:bg-gray-100"
                  }`}
                  key={details.id}
                  onClick={() => {
                    handlePanel(details.id);
                  }}
                >
                  <span className="text-sm text-gray-800 font-medium">
                    {details.name}
                  </span>
                  {index !== state.panelDetails.length - 1 && (
                    <div className="border-b border-panelBorder mt-1 mb-1"></div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4 text-sm">
                No records to display
              </div>
            )}
          </div>
        ),
      },
    ];
  }, [
    state.userId,
    state.dashboardDetails,
    state.cardDetails,
    state.panelDetails,
    state.dashboardId,
    state.cardId,
    state.panelId,
    handleDashboard,
    handleCards,
    handlePanel,
  ]);

  return (
    <div className="flex flex-col mt-2 w-full h-full">
      <div className="w-full sticky top-0 z-10 bg-white">
        <BreadcrumbNavigation
          linkTwo={"Users"}
          onlinkTwoClick={() => navigate("/app/usermanagement")}
          title={"User Analysis"}
        />
      </div>
      <div className="flex justify-between w-full h-full">
        <div className="flex flex-row mr-5 w-full">
          {state.dashboardId ? (
            <div className="-ml-3 w-[68vw] h-[82%] relative mt-10">
              {showEmptyDashboard ? (
                <div className="text-center md:mx-64 md:my-32 font-bold text-xl">
                  No cards or panels have been created for this dashboard
                </div>
              ) : (
                <>
                  <ResponsiveLayoutCard
                    cardDroppedData={state.cardDroppedData}
                  />
                  <ResponsiveLayout
                    cardDroppedData={state.cardDroppedData}
                    panelDroppedData={state.previewResponses}
                    getGridData={getGridData}
                    setPreviewLoading={(loading) =>
                      dispatch({
                        type: "SET_PREVIEW_LOADING",
                        payload: loading,
                      })
                    }
                    previewLoading={state.previewLoading}
                    userAnalysis={true}
                  />
                </>
              )}
            </div>
          ) : null}

          {state.cardId ? (
            <div className="mt-3">
              <CardData card={state.cardById} />
            </div>
          ) : null}

          {loadingData && (
            <div className="mt-3 bg-white p-1 w-[65vw]">
              <div className="border-b border-panelBorder mt-2 mb-2"></div>
              <div className="my-3 flex justify-center items-center">
                <CircularProgress size={20} style={{ color: "black" }} />
              </div>
            </div>
          )}

          {showPanelVisualization && (
            <div className="mt-10">
              <div className="bg-white p-1 w-[65vw] h-full">
                <div className="flex justify-between items-center ml-3 mt-2">
                  <div className="text-xs">{state.panelById.name}</div>
                  <ExpandIcon
                    className="w-3 h-3 cursor-pointer mr-3"
                    onClick={handleExpandOpen}
                  />
                </div>
                <div className="border-b mt-2 mx-2 border-panelBorder mb-2"></div>
                <div className="text-headingColor text-xs font-medium mb-2 mx-2">
                  {"From :"} {state.date?.startDate || ""}
                  {" - "}
                  {"To :"} {state.date?.endDate || ""}
                </div>

                <div className="text-headingColor text-xs font-medium flex items-center mb-2 mx-2">
                  <span>Selected Filters:</span>
                  <CssTooltip
                    title={
                      <ConditionDisplay conditions={state.panelById?.filters} />
                    }
                    placement="left"
                    arrow
                  >
                    <InfoIcon className="ml-2 w-4 h-3.5" />
                  </CssTooltip>
                </div>
                <PanelVisualization
                  type={state.panelById.visualizationType}
                  data={
                    state.panelById.visualizationType ===
                    VISUALIZATION_TYPES.MULTIAXIS_GRAPH
                      ? state.responseData
                      : state.responseData?.data ?? []
                  }
                  colors={COLORS}
                  isChart={true}
                  dimension={DEFAULT_DIMENSION}
                  errMsg={state.errMsg}
                />
              </div>
            </div>
          )}

          {!state.dashboardId && !state.cardId && !state.panelById ? (
            <div className="mt-10 relative ml-[20%]">
              <div className="absolute top-12 left-24 w-full h-full flex justify-center text-black text-[10px] font-bold">
                <div>Search and analyze your users...</div>
              </div>
              <img
                src={bgImage}
                style={{
                  height: "80%",
                  width: "100%",
                  objectFit: "cover",
                }}
                alt="bg"
              />
            </div>
          ) : null}
        </div>
        <div className="bg-white p-2.5 rounded min-w-[18vw] max-w-[18vw] h-full">
          <div className="border border-panelBorder p-2 rounded">
            <input
              type="text"
              placeholder="Search user"
              className="border border-panelBorder rounded-sm px-1 w-full text-sm"
              style={{ outline: "none", borderColor: "transparent" }}
              value={state.searchQuery}
              onChange={(e) => handleSearchInputChange(e.target.value)}
              onClick={handleClickSearch}
            />
          </div>
          <div className="flex-col rounded-sm px-1 py-1 mt-2">
            {state.searchClicked && (
              <div
                className="max-h-96 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-500"
                style={{
                  scrollbarWidth: "thin",
                  scrollbarColor: "#9CA3AF #F3F4F6",
                }}
              >
                {filteredUsers === undefined ? (
                  <div>Loading</div>
                ) : (
                  <>
                    {filteredUsers.map((data) => (
                      <div
                        key={data.id}
                        className="cursor-pointer hover:bg-selectBackground p-1 break-words"
                        onClick={() => handleSelectName(data.id, data.name)}
                      >
                        <div className="flex flex-wrap items-start gap-1">
                          <span className="text-sm text-titleColor break-words">
                            {data.name} -
                          </span>
                          <span className="text-xs text-tabColor break-words">
                            {data.role}
                          </span>
                        </div>
                        <div className="border-b border-panelBorder mt-1 mb-1"></div>
                      </div>
                    ))}
                  </>
                )}
              </div>
            )}
          </div>

          {state.userId &&
          state.dashboardDetails &&
          state.cardDetails &&
          state.panelDetails ? (
            <TabNavigation sections={navigationSections} />
          ) : null}
        </div>
      </div>

      {/* Expand Dialog */}
      <Dialog
        open={state.openExpandDialog}
        onClose={handleExpandClose}
        fullWidth
        onClick={(event) => {
          if (event.target === event.currentTarget) {
            handleExpandClose();
          }
        }}
        sx={{
          "& .MuiDialog-container": {
            "& .MuiPaper-root": {
              width: "100%",
              maxWidth: "1000px",
              margin: 0,
            },
          },
        }}
      >
        <div className="mx-4 mb-5">
          <div className="mt-4 text-black flex items-center justify-end">
            <CloseIcon
              onClick={handleExpandClose}
              className="w-2.5 h-2.5 cursor-pointer"
            />
          </div>
          <div className="text-xs text-titleColor ml-2 whitespace-pre-wrap break-all flex-grow">
            {state.panelById?.name}
          </div>
          <div className="border-b border-panelBorder mb-4" />
          <div className="text-headingColor text-xs font-medium mb-2 flex justify-between">
            {"From :"} {state.date?.startDate || ""}
            {" - "}
            {"To :"} {state.date?.endDate || ""}
            {state.panelById?.visualizationType ===
            VISUALIZATION_TYPES.TABLE_REPORT ? (
              <div>
                {" No. of records : "} {state.responseData?.count}/
                {state.responseData?.totalCount}
              </div>
            ) : null}
          </div>

          <div className="text-headingColor text-xs font-medium flex items-center mb-2 mx-2">
            <span>Selected Filters:</span>
            <CssTooltip
              title={<ConditionDisplay conditions={state.panelById?.filters} />}
              placement="left"
              arrow
            >
              <InfoIcon className="ml-2 w-4 h-3.5" />
            </CssTooltip>
          </div>
          <PanelVisualization
            type={state.panelById?.visualizationType}
            data={
              state.panelById?.visualizationType ===
              VISUALIZATION_TYPES.MULTIAXIS_GRAPH
                ? state.responseData
                : state.responseData?.data ?? []
            }
            colors={COLORS}
            isChart={true}
            maxWidthh={"60vh"}
            multiAxis={true}
            isPanel={true}
            isExpand={true}
            errMsg={state.errMsg}
            isWidth={true}
          />
        </div>
      </Dialog>
    </div>
  );
}

export default React.memo(UserAnalysis);
