import React from "react";
import {
  <PERSON>mpo<PERSON><PERSON><PERSON>,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

const colors = ["#8BD1AE", "#F28F8F", "#EDDF82", "#A1C3FA"];

const MultiAxisChart = ({ data, dimension, ischart, multiAxis, errMsg }) => {
  const legendWrapperStyle = {
    position: "absolute",
    top: "5%",
    // left: "20%",
    transform: "translateY(-80%)",
    fontWeight: 400,
    fontSize: "12px",
  };

  function convertDatetimeFormat(datetime) {
    const date = new Date(datetime);
    const formattedDate = date.toLocaleString("en-US", {
      month: "short",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
    return formattedDate.replace(",", "");
  }

  // Convert sampledata to convertedData
  const convertedData = {
    ...data,
    data: data?.data?.map((item) => ({
      ...item,
      Datetime: convertDatetimeFormat(item.Datetime),
    })),
    "Y-Axis": data["Y-Axis"]?.flatMap((axis) =>
      axis?.name?.split(" and ")?.map((name) => ({ name, type: axis.type }))
    ),
  };
  // Calculate min and max values for each key
  const minMaxValues = {};
  convertedData["Y-Axis"]?.forEach((axis) => {
    const key = axis.name;
    minMaxValues[key] = {
      min: Math.min(...convertedData.data?.map((item) => item[key] - 1.5)),
      max: Math.max(...convertedData.data?.map((item) => item[key] + 0.5)),
    };
  });
  const yAxisWidths = convertedData["Y-Axis"]?.map((axis) => {
    const key = axis.name;
    const maxLength = Math.max(
      ...convertedData.data?.map((item) => String(Math.ceil(item[key])).length)
    );
    if (maxLength < 3) return ischart ? maxLength * 16 : maxLength * 24;
    return ischart ? maxLength * 10 : maxLength * 12;
  });

  if (!convertedData.data || convertedData.data.length === 0) {
    return (
      <div
        style={{
          width: "100%",
          height: 240,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          fontSize: 16,
          //  fontWeight: 600,
          // color: "#999999",
        }}
      >
        {errMsg ? errMsg : "No records to display"}
      </div>
    );
  }

  return (
    <ResponsiveContainer
      width={dimension ? dimension.w * 135 : "100%"}
      height={dimension ? dimension.h * 70 : 440}
    >
      <ComposedChart
        data={convertedData.data}
        margin={{
          top: 35,
          right: 20,
          bottom: ischart ? 40 : 50,
          left: ischart ? 10 : 20,
        }}
      >
        <CartesianGrid stroke="#f5f5f5" />
        <XAxis
          dataKey="Datetime"
          // scale="band"
          tick={{ fontSize: ischart ? 10 : 12, fontWeight: 600 }}
          angle={-45}
          textAnchor="end"
          interval={2}
        />
        <Tooltip
          formatter={(value) => {
            if (typeof value === "number") {
              return value.toLocaleString("en-US", {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2,
              });
            }
            return value;
          }}
          contentStyle={{
            fontWeight: "bold",
          }}
        />

        {!ischart ? (
          <Legend
            verticalAlign="top"
            layout="horizontal"
            wrapperStyle={legendWrapperStyle}
          />
        ) : null}
        {multiAxis ? (
          <Legend
            verticalAlign="top"
            layout="horizontal"
            wrapperStyle={legendWrapperStyle}
          />
        ) : null}

        {convertedData["Y-Axis"]?.map((axis, index) => {
          if (axis.type === "Line Graph") {
            return (
              <Line
                type="natural"
                dot={{ strokeWidth: 1.5, r: 3 }}
                strokeWidth={3.5}
                key={index}
                dataKey={axis.name}
                stroke={colors[index]}
                yAxisId={index}
              />
            );
          } else if (axis.type === "Bar Graph") {
            return (
              <Bar
                barSize={20}
                key={axis.name}
                dataKey={axis.name}
                fill={colors[index]}
                yAxisId={index}
                barGap={0}
                barCategoryGap={0}
              />
            );
          }
        })}

        {convertedData["Y-Axis"]?.map((axis, index) => {
          const { min, max } = minMaxValues[axis.name];
          const domainPadding = Math.max((max - min) / 10, 1); // Ensure at least 10 ticks
          return (
            <YAxis
              key={axis.name}
              yAxisId={index}
              tick={{ fontSize: ischart ? 10 : 12, fontWeight: 600 }}
              orientation={index === 0 ? "left" : "right"}
              //  domain={[min, max]} // Extend the upper limit for padding
              tickCount={10} // Ensure at least 10 ticks
              stroke={colors[index]}
              width={yAxisWidths[index]}
              tickFormatter={(value) =>
                typeof value === "number"
                  ? value.toLocaleString("en-US", {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 2,
                    })
                  : value
              }
            />
          );
        })}
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export default MultiAxisChart;
