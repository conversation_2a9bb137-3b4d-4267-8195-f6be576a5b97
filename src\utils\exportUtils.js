/**
 * Export permissions configuration utilities
 */

/**
 * Checks what export types are allowed based on total count and config
 * @param {number} totalCount - Total number of records
 * @param {Object} configApiData - Configuration data from API
 * @returns {Object} - Export permissions object
 */
export const getExportPermissions = (totalCount, configApiData) => {
  const {
    MAX_ROWS_FOR_CSV,
    MAX_ROWS_FOR_PDF,
    MAX_ROWS_FOR_EXCEL,
    INITIATE_OFFLINE_DOWNLOAD,
  } = configApiData;

  const isOfflineDownload = totalCount >= INITIATE_OFFLINE_DOWNLOAD;

  return {
    csv: isOfflineDownload || totalCount <= MAX_ROWS_FOR_CSV,
    pdf: isOfflineDownload ? false : totalCount <= MAX_ROWS_FOR_PDF,
    excel: isOfflineDownload ? false : totalCount <= MAX_ROWS_FOR_EXCEL,
  };
};

/**
 * Creates tooltip content for export limits
 * @param {Object} configApiData - Configuration data from API
 * @returns {JSX.Element} - Tooltip content
 */
export const createExportTooltipContent = (configApiData) => (
  <div className="text-sm">
    <div className="font-bold text-xs mb-1">
      Configured number of rows for download:
    </div>
    <div className="font-semibold text-xs mb-1">
      CSV:{" "}
      {configApiData?.MAX_ROWS_FOR_CSV?.toLocaleString("en-US") || "Loading..."}
    </div>
    <div className="font-semibold text-xs mb-1">
      EXCEL:{" "}
      {configApiData?.MAX_ROWS_FOR_EXCEL?.toLocaleString("en-US") ||
        "Loading..."}
    </div>
    <div className="font-semibold text-xs mb-1">
      PDF:{" "}
      {configApiData?.MAX_ROWS_FOR_PDF?.toLocaleString("en-US") || "Loading..."}
    </div>
    <div className="font-semibold text-xs mb-1">
      Offline (Only in CSV):{" "}
      {configApiData?.INITIATE_OFFLINE_DOWNLOAD?.toLocaleString("en-US") ||
        "Loading..."}
    </div>
  </div>
);

/**
 * Maps export type to API type and file extension
 * @param {string} type - Export type (CSV, EXCEL, PDF)
 * @returns {Object} - Object containing apiType and extension
 */
export const getExportTypeMapping = (type) => {
  const typeMapping = {
    CSV: { apiType: "CSV", extension: ".csv" },
    EXCEL: { apiType: "EXCEL", extension: ".xlsx" },
    PDF: { apiType: "PDF", extension: ".pdf" },
  };

  return typeMapping[type] || typeMapping.CSV;
};
