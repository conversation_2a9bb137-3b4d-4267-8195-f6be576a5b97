import React, {
  useState,
  useMemo,
  useEffect,
  useCallback,
  useContext,
  // useContext,
} from "react";
import moment from "moment";
import axios from "axios";
import { config } from "../assets/config/config";
import ThemedSuspense from "../components/ThemedSuspense";
import { DataContext } from "./DataContext";
import { ConfigService } from "../services/config-api";

const apiUrl = config.api.url;

// create context
export const AuthContext = React.createContext();

export const AuthProvider = ({ children }) => {
  const [isLoaded, setLoaded] = useState(false);
  const [user, setUser] = useState(null);
  const [roles, setRoles] = useState(null);
  const [accessToken, setAccessToken] = useState(null);
  const [fetchRole, setFetchRole] = useState(false);
  const [reset, setReset] = useState(false);
  const [configApiData, setConfigApiData] = useState();
  useEffect(() => {
    let config = {};
    config.headers = {};
    config.headers.Authorization = `Bearer ${accessToken?.token}`;
    const getUserRole = () => {
      try {
        axios.get(`${apiUrl}/v1/roles/${user?.roleId}`, config).then((res) => {
          setRoles(res.data);
          setFetchRole(false);
          setLoaded(true);
        });
      } catch (error) {
        //console.error(error);
        setLoaded(true);
      }
    };
    const configCall = () => {
      //console.log("ConfigCall");
      axios
        .get(`${apiUrl}/v1/users/config`, config)
        .then((data) => {
          //console.log("result", data);
          setConfigApiData(data?.data);
        })
        .catch((error) => {
          console.error("result-error", error);
        });
    };
    configCall();

    if (fetchRole === true && user?.roleId) {
      getUserRole();
    }
  }, [fetchRole, user, accessToken]);
  //console.log("configApiData", configApiData);

  const refreshTokens = useCallback(() => {
    return axios
      .post(`${apiUrl}/v1/auth/refresh-tokens`, {})
      .then((response) => {
        setAccessToken(response.data.token);
        setUser(response.data.user);
        let config = {};
        config.headers = {};
        config.headers.Authorization = `Bearer ${response.data.token.token}`;
        axios
          .get(`${apiUrl}/v1/roles/${response.data.user.roleId}`, config)
          .then((res) => {
            setRoles(res.data);
            setLoaded(true);
          });
        return response;
      })
      .catch((error) => {
        setLoaded(true);
        setUser(null);
        setAccessToken(null);
        setLoaded(true);
        return error;
      });
  }, []);

  const startSilentRefresh = useCallback(() => {
    if (accessToken) {
      const tokenExpires = moment(accessToken.expires);
      const tokenMaxAge = tokenExpires.diff(moment().add(1, "minutes"));
      setTimeout(() => {
        refreshTokens();
      }, tokenMaxAge);
    }
  }, [accessToken, refreshTokens]);

  const syncLogout = (event) => {
    if (event.key === "logout") {
      setAccessToken(null);
      setUser(null);
    }
  };

  useEffect(() => {
    const interceptorId = axios.interceptors.request.use(
      (config) => {
        config.withCredentials = true;
        config.credentials = "include";
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.request.eject(interceptorId);
    };
  }, [accessToken]);

  useEffect(() => {
    refreshTokens();
  }, [refreshTokens]);

  useEffect(() => {
    startSilentRefresh();
  }, [accessToken, startSilentRefresh]);

  useEffect(() => {
    window.addEventListener("storage", syncLogout);
    return function cleanup() {
      window.removeEventListener("storage", syncLogout);
    };
  }, []);

  const value = useMemo(() => {
    const register = (username, email, password) => {
      return axios
        .post(`${apiUrl}/v1/auth/register`, {
          name: username,
          email: email,
          password: password,
        })
        .then((response) => {
          setAccessToken(response.data.token);
          setUser(response.data.user);
          startSilentRefresh();
        });
    };

    const login = (email, password, callback = false) => {
      return axios
        .post(`${apiUrl}/v1/auth/login`, {
          email: email,
          password: password,
        })
        .then((response) => {
          if (response.data.resetPassword === true) {
            setReset(true);
            setAccessToken(response.data.token);
            setUser(response.data.user);
            // startSilentRefresh()
            let config = {};
            config.headers = {};
            config.headers.Authorization = `Bearer ${response.data?.token?.token}`;
            return axios
              .get(`${apiUrl}/v1/roles/${response?.data?.user?.roleId}`, config)
              .then((res) => {
                setRoles(res.data);
              });
          } else if (!response?.data?.otpSent) {
            setAccessToken(response.data.token);
            setUser(response.data.user);
            // startSilentRefresh()
            let config = {};
            config.headers = {};
            config.headers.Authorization = `Bearer ${response.data.token.token}`;

            return axios
              .get(`${apiUrl}/v1/roles/${response.data.user.roleId}`, config)
              .then((res) => {
                setRoles(res.data);
              });
          } else {
            callback && callback(response);
          }
        });
    };

    const logout = () => {
      setAccessToken(null);
      setUser(null);
      return axios
        .post(`${apiUrl}/v1/auth/logout`, {})
        .then((response) => {
          window.localStorage.setItem("logout", moment());
        })
        .catch((err) => {});
    };

    const otpVerificationCall = (email, OTP) => {
      return axios
        .post(`${apiUrl}/v1/auth/verify-otp`, {
          email: email,
          otp: OTP,
        })
        .then((response) => {
          if (response.data.resetPassword === true) {
            setReset(true);
            setAccessToken(response.data.token);
            setUser(response.data.user);
            // startSilentRefresh()
            let config = {};
            config.headers = {};
            config.headers.Authorization = `Bearer ${response.data.token.token}`;
            return axios
              .get(`${apiUrl}/v1/roles/${response.data.user.roleId}`, config)
              .then((res) => {
                setRoles(res.data);
              });
          } else {
            setAccessToken(response.data.token);
            setUser(response.data.user);
            // startSilentRefresh()
            let config = {};
            config.headers = {};
            config.headers.Authorization = `Bearer ${response.data.token.token}`;

            return axios
              .get(`${apiUrl}/v1/roles/${response.data.user.roleId}`, config)
              .then((res) => {
                setRoles(res.data);
              });
          }
        });
    };

    const forgotPassword = (email) => {
      return axios.post(`${apiUrl}/v1/auth/forgot-password`, {
        email: email,
      });
    };

    const resetPassword = (password, resetToken) => {
      return axios.post(
        `${apiUrl}/v1/auth/reset-password?token=${resetToken}`,
        {
          password: password,
        }
      );
    };

    const verifyEmail = (emailVerificationToken) => {
      return axios.post(
        `${apiUrl}/v1/auth/verify-email?token=${emailVerificationToken}`,
        {}
      );
    };

    return {
      user,
      setUser,
      register,
      setRoles,
      roles,
      login,
      logout,
      forgotPassword,
      resetPassword,
      verifyEmail,
      otpVerificationCall,
      reset,
      configApiData,
      setConfigApiData,
    };
  }, [user, roles, startSilentRefresh, reset]);

  if (!isLoaded) {
    return <ThemedSuspense />;
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
