import { useState, useContext, useMemo } from "react";
import Title from "../Title.js";
import { SearchIcon } from "../icons";
import Button from "../components/Button/OutlinedButton.js";
import { useNavigate } from "react-router-dom";
import "react-toastify/dist/ReactToastify.css";
import { AuthContext } from "../context/AuthContext";
import RolesList from "../RoleManagement/listView.js";
import bgImage from "../assets/img/Records.png";
import theme from "../tailwind-theme.js";

var style = {
  buttonStyle:
    "float-right   text-sm  text-white btn btn-danger bg-clip-padding border border-solid border-Bittersweet rounded transition ease-in-out  inline-block h-10 text-xs  font-semibold ",
  fieldstyle:
    "form-control w-1/2 mt-2 text-sm pl-5 text-black bg-white bg-clip-padding border border-solid border-Bittersweet rounded transition ease-in-out  m-0 inline-block h-12  font-semibold ",
};
const Roles = () => {
  const { roles } = useContext(AuthContext);
  const navigate = useNavigate();
  const [searchStr, setSearchStr] = useState("");
  const [reload, setReload] = useState(false);
  const [message, setMessage] = useState("");
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [currentPage, setCurentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [totalResults, setTotalResults] = useState(0);

  const handleSetPage = (page) => {
    setCurentPage(page);
  };

  const permissions = useMemo(() => {
    return roles?.resources?.filter((res) => res.name === "Role Management")[0]
      ?.permissions;
  }, [roles]);

  const handleAdd = () => {
    if (permissions?.create === 0) {
      handleSetMessage("Add permission not allowed");
      handleOpenInfoModal();
    } else {
      navigate("/app/rolemanagement/add");
    }
  };

  const handleOpenInfoModal = () => {
    setShowAlertConfirmation(true);
  };
  const handleCloseInfoModal = () => {
    setShowAlertConfirmation(false);
  };

  const handleSetMessage = (message) => {
    setMessage(message);
  };

  const handleKeyUp = (event) => {
    // if(event.target.value.length >= 2) {
    setSearchStr(event.target.value);
    setReload(true);
    setCurentPage(1);

    // }
  };

  const handleSuccessDialog = () => {
    setSuccessDialog(true);
  };

  return (
    <div className="">
      <div className="w-full mt-10">
        <Title title={"Roles List"} />
      </div>

      <div className="border border-listBorder bg-white  mt-8">
        {/* p-3 */}
        {!isLoading && totalResults === 0 && searchStr === "" ? (
          <>
            <div className="mx-3 mt-5">
              <div className="border border-outerBorder mb-5">
                <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                  {"Oops ! no records to display."}
                </div>
                <div className="flex justify-center text-tabColor text-base font-semibold mt-5 ">{`You have not added any roles yet.`}</div>
                <div className="flex justify-center my-12">
                  <img
                    src={bgImage}
                    style={{
                      height: "10%",
                      width: "10%",
                      objectFit: "cover",
                    }}
                    alt="bg"
                  />
                </div>
                <div className="flex justify-center mb-5">
                  <Button
                    buttonClassName="text-xs w-36 text-white h-10 "
                    label={"+Add New Role"}
                    onClick={handleAdd}
                  ></Button>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="mx-3">
              {/* className="mx-3" */}
              <div className="flex w-full pt-7 px-3">
                <div className=" float-left  w-1/2 ">
                  <input
                    type="text"
                    style={{
                      border: `1px solid ${theme.borderColor.outerBorder}`,
                      paddingLeft: "2rem",
                    }}
                    className="w-full px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                    placeholder="Search Role"
                    onKeyUp={handleKeyUp}
                  />
                  <div
                    class="top-0 right-0  mr-4"
                    style={{ marginLeft: "0.25rem", marginTop: "-1.7rem" }}
                  >
                    <SearchIcon className="w-5 h-5" />
                  </div>
                </div>
                <div className="flex-grow flex justify-end items-center gap-3">
                  <Button
                    buttonClassName="text-xs w-36 text-white h-10 "
                    label={"+Add New Role"}
                    onClick={handleAdd}
                  ></Button>
                </div>
              </div>
            </div>
          </>
        )}

        <RolesList
          setIsLoading={setIsLoading}
          isLoading={isLoading}
          totalResults={totalResults}
          setTotalResults={setTotalResults}
          reload={reload}
          searchStr={searchStr}
          // setReload={setReload}
          permissions={permissions}
          message={message}
          showAlertConfirmation={showAlertConfirmation}
          handleCloseInfoModal={handleCloseInfoModal}
          handleOpenInfoModal={handleOpenInfoModal}
          handleSetMessage={handleSetMessage}
          handleSuccessDialog={handleSuccessDialog}
          handleSetPage={handleSetPage}
          currentPage={currentPage}
        />
      </div>
    </div>
  );
};

export default Roles;
