import React, { useState, useRef, useEffect, memo, useMemo } from "react";
import ExpandMore from "@mui/icons-material/ExpandMore";
import ExpandLess from "@mui/icons-material/ExpandLess";
import SearchIcon from "@mui/icons-material/Search";
import { CircularProgress } from "@mui/material";
import { VariableSizeList as List, areEqual } from "react-window";
import { CloseIcon, BarIcon, LineIcon } from "../../icons";
import { CssTooltip } from "../StyledComponent";

const MultiAxisDropdown = ({
  isEdit,
  btnName,
  btnWidth,
  btnHeight,
  width,
  data,
  isLoading,
  reset,
  onSelectionChange,
  isMulti = true,
  disabled = false,
  defaultSelectedData,
  commonValues,
  isSearch = true,
  searchParameter = "value",
  isReports,
}) => {
  const [open, setOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [searchText, setSearchText] = useState("");
  const buttonRef = useRef(null);
  const dropdownRef = useRef(null);
  const onSelectionChangeRef = useRef(onSelectionChange);
  const isUpdatingRef = useRef(false);

  // Update the ref when the prop changes
  useEffect(() => {
    onSelectionChangeRef.current = onSelectionChange;
  }, [onSelectionChange]);

  const Styles = {
    btnPrimary: `bg-transparent text-gray-400 px-2 w-full border border-outerBorder flex min-h-[40px] items-center rounded-[5px] ${
      btnWidth ? btnWidth : "min-w-[400px]"
    } ${btnHeight ? btnHeight : "h-full"}`,
  };
  const itemHeight = 30;
  const maxListHeight = 150;

  const options = [
    { label: "Line", value: "Line Graph" },
    { label: "Bar", value: "Bar Graph" },
  ];

  const [selectObject, setSelectObject] = useState({});
  const [enabledList, setEnabledList] = useState([]);

  const handleOpen = (e) => {
    e.preventDefault();
    setOpen(!open);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        !event.target.classList.contains("custom-checkbox") &&
        !event.target.classList.contains("custom-select") &&
        !event.target.classList.contains("custom-search")
      ) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (commonValues) {
      const valuesArray = Array.isArray(commonValues)
        ? commonValues
        : commonValues.split(",");
      setSelectedOptions(valuesArray);
    } else {
      setSelectedOptions([]);
    }
  }, [commonValues]);

  const handleCheckboxChange = ({ target }, option) => {
    setEnabledList((oldState) => {
      let newState = [...oldState];
      if (target.checked) {
        newState.push(option.value);
        setSelectObject((oldState) => {
          let newState = { ...oldState };
          newState[option.value] = "Line Graph";
          return newState;
        });
      } else {
        newState = newState.filter((val) => val !== option.value);
        setSelectObject((oldState) => {
          let newState = { ...oldState };
          delete newState[option.value];
          return newState;
        });
      }
      return newState;
    });
    if (!isMulti) {
      if (selectedOptions.includes(option.value)) {
        setSelectedOptions([]);
      } else {
        setSelectedOptions([option.value]);
        // setSelectedValue(selectedValue);
      }
    } else {
      //  setSelectedValue(selectedValue);
      setSelectedOptions((prevSelectedOptions) => {
        if (prevSelectedOptions.includes(option.value)) {
          return prevSelectedOptions.filter(
            (selectedOption) => selectedOption !== option.value
          );
        } else {
          return [...prevSelectedOptions, option.value];
        }
      });
    }
  };

  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
  };
  useEffect(() => {
    setSelectedOptions([]);
    setSearchText("");
  }, [reset]);

  useEffect(() => {
    if (
      onSelectionChangeRef.current &&
      !isUpdatingRef.current &&
      Object.keys(selectObject).length > 0
    ) {
      onSelectionChangeRef.current(selectObject);
    }
  }, [selectObject]);

  useEffect(() => {
    if (
      defaultSelectedData &&
      Array.isArray(defaultSelectedData) &&
      defaultSelectedData.length > 0
    ) {
      isUpdatingRef.current = true;
      setSelectedOptions(defaultSelectedData.map((item) => item.name));
      let tempobj = {};
      defaultSelectedData.forEach(({ name, type }) => (tempobj[name] = type));
      setSelectObject(tempobj);
      setEnabledList(Object.keys(tempobj));
      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 0);
    }
  }, [defaultSelectedData]);

  const removeSelectedOption = (e, optionValue) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedOptions((prevSelectedOptions) =>
      prevSelectedOptions.filter(
        (selectedOption) => selectedOption !== optionValue
      )
    );
    setEnabledList((prevSelectedOptions) =>
      prevSelectedOptions.filter(
        (selectedOption) => selectedOption !== optionValue
      )
    );

    setSelectObject((prevSelectObject) => {
      const newSelectObject = { ...prevSelectObject };
      newSelectObject[optionValue] = "Line Graph";
      return newSelectObject;
    });
  };

  const combinedOptions = useMemo(() => {
    const filteredOptions =
      data?.length > 0
        ? data.filter((option) =>
            String(option[searchParameter])
              .toLowerCase()
              .includes(searchText.toLowerCase())
          )
        : [];
    return [
      ...selectedOptions
        .map((selectedOption) =>
          data?.find((option) => option.value === selectedOption)
        )
        .filter(Boolean),
      ...filteredOptions.filter((option) => {
        return (
          option &&
          option?.value &&
          option?.label &&
          !selectedOptions.includes(option?.value)
        );
      }),
    ];
  }, [data, selectedOptions, searchText, searchParameter]);

  const Row = memo(({ data, index, style }) => {
    const handleChange = (e) => {
      const { name, value } = e.target;
      setSelectObject((oldState) => ({ ...oldState, [name]: value }));
    };
    const currentOption = combinedOptions?.[index];
    const rowStyle = {
      ...style,
      borderBottom: "1px solid #E5E7EB",
      paddingBottom: "6px",
      marginBottom: "6px",
      paddingTop: "2px",
    };

    return (
      <div key={index} style={rowStyle}>
        <label className="flex justify-between gap-2 text-xs">
          <CssTooltip
            title={currentOption?.label || ""}
            arrow
            placement="top"
            disableHoverListener={
              !currentOption?.label || currentOption?.label.length <= 8
            }
          >
            <div className="text-xs pt-2 cursor-default">
              {currentOption?.label && currentOption.label.length > 8
                ? `${currentOption.label.substring(0, 15)}...`
                : currentOption?.label}
            </div>
          </CssTooltip>
          <div className="flex-grow flex items-center justify-end ">
            <select
              name={currentOption?.label}
              value={selectObject[currentOption?.label]}
              onChange={(e) => handleChange(e)}
              className={`w-[80px] h-6 text-xs border-2 border-grey rounded custom-select mr-8 ${
                !enabledList.includes(currentOption?.label)
                  ? "bg-gray-200 cursor-not-allowed"
                  : ""
              }`}
              disabled={!enabledList.includes(currentOption?.label)}
            >
              {options.map((option, index) => (
                <option key={index} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            <input
              type="checkbox"
              checked={enabledList.includes(currentOption?.value)}
              onChange={(e) => handleCheckboxChange(e, currentOption)}
              className="min-w-[20px] w-4 h-4 custom-checkbox"
              style={{ accentColor: "#707070" }}
            />
          </div>
        </label>
      </div>
    );
  }, areEqual);

  const truncate = (text, maxLength = 18) => {
    return text?.length > maxLength ? text.slice(0, maxLength) + "..." : text;
  };

  return (
    <div>
      <div
        className={` ${btnWidth ? "" : "min-w-[150px]"}  relative text-medium`}
      >
        <button
          ref={buttonRef}
          onClick={handleOpen}
          className={
            open
              ? `flex  ${Styles.btnPrimary} border-outerBorder`
              : Styles.btnPrimary
          }
          disabled={disabled}
        >
          <div className="flex w-11/12 flex-wrap  ">
            <div className="text-xs mt-1 flex flex-wrap  ">
              {(Array.isArray(selectedOptions) ? selectedOptions : []).length >
              0 ? (
                <CssTooltip
                  title={
                    <div className="max-h-[150px] overflow-y-auto max-w-[250px]">
                      {(Array.isArray(selectedOptions) ? selectedOptions : [])
                        .map((selectedValue) => {
                          const option = combinedOptions.find(
                            (opt) => opt.value === selectedValue
                          );
                          return option && option.label !== "Select All" ? (
                            <div key={selectedValue} className="text-xs mb-2">
                              {option.label}
                            </div>
                          ) : null;
                        })
                        .filter(Boolean)}
                    </div>
                  }
                  arrow
                  placement="top"
                >
                  <div className="flex whitespace-nowrap items-center cursor-pointer">
                    {(() => {
                      const selectedArray = Array.isArray(selectedOptions)
                        ? selectedOptions
                        : [];
                      const filteredOptions = selectedArray.filter((val) => {
                        const opt = combinedOptions.find(
                          (opt) => opt.value === val
                        );
                        return opt && opt.label !== "Select All";
                      });

                      const firstOption = filteredOptions[0];
                      const firstOptionData = combinedOptions.find(
                        (opt) => opt.value === firstOption
                      );
                      const remainingCount = filteredOptions.length - 1;

                      return (
                        <>
                          {firstOptionData && (
                            <span className="flex text-black items-center m-1 rounded h-6 px-2 bg-bgTeritary">
                              <span>{truncate(firstOptionData.label)}</span>
                              {selectObject.hasOwnProperty(firstOption) &&
                              typeof selectObject[firstOption] === "string" &&
                              selectObject[firstOption].includes(
                                "Bar Graph"
                              ) ? (
                                <BarIcon className="h-2.5 w-2.5 ml-2.5" />
                              ) : (
                                <LineIcon className="h-2.5 w-2.5 ml-2.5" />
                              )}
                              <CloseIcon
                                onClick={(e) =>
                                  removeSelectedOption(e, firstOptionData.value)
                                }
                                className="h-1.5 ml-1"
                              />
                            </span>
                          )}
                          {remainingCount > 0 && (
                            <span className="text-gray-500 mt-1">
                              +{remainingCount}
                            </span>
                          )}
                        </>
                      );
                    })()}
                  </div>
                </CssTooltip>
              ) : (
                <span className="text-gray-400">{btnName || "Select"}</span>
              )}
            </div>
          </div>
          <div>
            {open ? (
              <ExpandLess className="ml-2" />
            ) : (
              <ExpandMore className="ml-2" />
            )}
          </div>
        </button>
        {open ? (
          <div className="relative bottom-0 left-0 z-50 ">
            <div
              ref={dropdownRef}
              className={`bg-white shadow-md p-4 rounded absolute   ${
                btnWidth ? "min-w-full" : "min-w-[150px]"
              }`}
            >
              {isSearch && (
                <div className="mb-4 ">
                  <div
                    className={`flex items-center border border-gray-300 px-2 py-1  w-full `}
                  >
                    <SearchIcon className="text-gray-400 mr-4 ml-1" />
                    <input
                      type="text"
                      placeholder="Search"
                      value={searchText}
                      onChange={handleSearchChange}
                      className="border-none outline-none w-full text-xs custom-search"
                    />
                  </div>
                </div>
              )}
              {isLoading ? (
                <div className="flex justify-center">
                  <CircularProgress />
                </div>
              ) : (
                <div className="w-full max-h-72 ">
                  <List
                    width={width}
                    height={Math.min(
                      combinedOptions.length * itemHeight,
                      maxListHeight
                    )}
                    itemCount={combinedOptions?.length}
                    itemData={combinedOptions}
                    itemSize={(index) =>
                      combinedOptions[index]?.label?.length > 150 ? 75 : 30
                    }
                  >
                    {Row}
                  </List>
                </div>
              )}
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default MultiAxisDropdown;
