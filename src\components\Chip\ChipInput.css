/* App.css */

.input-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.chips-input {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  border: 1px solid #ccc;
  padding: 8px;
  border-radius: 4px;
  min-height: 40px;
  max-height: 200px;
  overflow-y: auto;
  width: 100%;
  font-size: 12px;
}

.chip {
  background-color: #e7f1fd;
  padding: 2px 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 12px;
}

.close {
  cursor: pointer;
  margin-left: 4px;
  font-weight: bold;
  color: black;
}

.chips-input input {
  border: none;
  outline: none;
  flex: 1;
  margin: 0;
  padding: 0;
  min-width: 40px;
  width: 100%;
  box-sizing: border-box;
}
