import React from "react";
import { Button } from "@mui/material";

const CommonButton = (props) => {
  const {
    variant,
    height,
    width,
    backgroundColor,
    borderRadius,
    border,
    onClick,
    children,
    color,
    noWrap,
    ...otherProps
  } = props;

  const configButton = {
    ...otherProps,
    fullWidth: true,
    variant: `${variant && variant ? variant : "contained"}`,
    sx: {
      border: border ? border : `1.5px solid ${"black"}`,
      borderRadius: borderRadius ? borderRadius : "4px",
      textTransform: "none",
      fontFamily: "Space Grotesk",
      fontSize: "14px",
      fontWeight: 400,
      whiteSpace: `${noWrap && noWrap ? "noWrap" : ""}`,
      backgroundColor: `${
        backgroundColor && backgroundColor ? backgroundColor : ""
      }`,
      color: `${color && color ? color : ""}`,
      width: `${width && width ? width : "100%"}`,
      height: height,
      "&:hover": {
        backgroundColor: `${
          backgroundColor && backgroundColor ? backgroundColor : ""
        }`,
        border: border ? border : `1.5px solid ${"black"}`,
      },
      "&:disabled": {
        cursor: "not-allowed",
        color: `${color && color ? color : "grey"}`,
        border: "0.5px solid grey",
      },
    },
  };
  return (
    <Button {...configButton} onClick={onClick}>
      {children}
    </Button>
  );
};

export default CommonButton;
