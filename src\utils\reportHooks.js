/**
 * Custom hooks for report functionality
 */
import { useEffect, useRef } from "react";

/**
 * Custom hook for handling click outside functionality
 * @param {boolean} isOpen - Whether the element is open
 * @param {Function} onClose - Function to call when clicking outside
 * @returns {Object} - Ref object to attach to the element
 */
export const useClickOutside = (isOpen, onClose) => {
  const ref = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (ref.current && !ref.current.contains(event.target)) {
        onClose();
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  return ref;
};

/**
 * Custom hook for search functionality with debouncing
 * @param {Function} onSearch - Function to call when search changes
 * @param {number} delay - Debounce delay in milliseconds
 * @returns {Object} - Search handlers and state
 */
export const useSearch = (onSearch, delay = 300) => {
  const timeoutRef = useRef(null);

  const handleSearch = (value) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      onSearch(value);
    }, delay);
  };

  const handleKeyUp = (event) => {
    const value = event.target.value;
    handleSearch(value);
  };

  const handleChange = (event) => {
    const value = event.target.value;
    // For immediate UI update, we might want to call onSearch without debouncing for change
    // But let's keep it consistent with keyup behavior
    handleSearch(value);
  };

  return {
    handleKeyUp,
    handleChange,
    handleSearch,
  };
};

/**
 * Custom hook for pagination functionality
 * @param {Function} onPageChange - Function to call when page changes
 * @param {Function} onLimitChange - Function to call when limit changes (page, limit)
 * @returns {Object} - Pagination handlers
 */
export const usePagination = (onPageChange, onLimitChange) => {
  const handlePageChange = (page) => {
    onPageChange(page);
  };

  const handleLimitChange = (e) => {
    const newLimit = e?.target?.value;
    onLimitChange(1, newLimit); // Reset to page 1 when limit changes
  };

  return {
    handlePageChange,
    handleLimitChange,
  };
};
