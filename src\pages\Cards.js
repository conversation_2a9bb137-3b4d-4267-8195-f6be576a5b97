import React, { useMemo, useState, useContext } from "react";
import ReportTable from "../components/table/ReportTable";
import { EditingIcon, TrashIcon, Alert, SearchIcon } from "../icons";
import AddCard from "../popups/AddCard";
import { cardServices } from "../services/cards-api";
import Pagination from "../components/Pagination/Pagination";
import SuccessDialog from "../popups/SuccessDialog";
import ErrorDialog from "../popups/ErrorDialog";
import DeleteDialog from "../popups/DeleteDialog";
import { AuthContext } from "../context/AuthContext";
import InfoModal from "../components/modals/InfoModal";
import Button from "../components/Button/OutlinedButton";
import { useQuery } from "react-query";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { DataContext } from "../context/DataContext";
import Title from "../Title";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import EditButton from "../components/Button/EditButton";
import bgImage from "../assets/img/Records.png";
import theme from "../tailwind-theme";

function Cards() {
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [users, setUsers] = useState([]);
  const [edit, setEdit] = useState(false);
  const [selectedDetails, setSelectedDetails] = useState();
  const [id, setId] = useState("");
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [errorDialog, setErrorDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  //const pageSize = 10;
  const { resultPerPage } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(10);

  const { roles } = useContext(AuthContext);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  // console.log("roles", roles);

  const permissions = roles?.resources?.filter(
    (res) => res?.name === "Card Management"
  )[0].permissions;

  const handleOpenInfoModal = () => {
    setShowAlertConfirmation(true);
  };
  const handleCloseInfoModal = () => {
    setShowAlertConfirmation(false);
  };

  //console.log("permissions", permissions);

  function openCard() {
    setOpenDialog(true);
  }
  function closeCard() {
    setOpenDialog(false);
  }
  const handleEdit = (rowData) => {
    setOpenDialog(true);
    setEdit(true);
    setSelectedDetails(rowData);
    //console.log("Edit clicked for row:", rowData);
  };
  const handleDelete = (rowData) => {
    // console.log("Recruiter Id", recruiter_id);
    setId(rowData.id);
    setDeleteDialog(true);
  };
  function handlePageChange(page) {
    setCurrentPage(page);
  }
  const handleLimitChange = (e) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
  };
  const {
    data: cardList,
    refetch,
    isLoading: cardLoading,
    isFetching,
  } = useQuery(
    ["cardList", currentPage, limitPerPage, searchInput],
    cardServices.getAll,
    {
      onSuccess: (data) => {
        //console.log("data", data);
        let newData = data?.data?.data?.map((x) => {
          return {
            createdAt: x.createdAt,
            //createdAt: formatDateTime(x.createdAt),
            // dayjs(x.createdAt).format("DD/MM/YYYY"),
            createdBy: x.createdBy,
            id: x.id,
            name: x.name,
            reportField: x.reportField,
            updatedAt: x.updatedAt,
          };
        });
        setUsers(newData);
      },
    }
  );
  const columns = useMemo(
    () => [
      {
        header: "Card Name",
        accessorKey: "name",
      },
      {
        header: "Creation Date",
        accessorKey: "createdAt",
      },
      {
        header: "Derived Field",
        accessorKey: "reportField",
      },

      {
        header: "",
        accessorKey: "action",
        enableSorting: false,
        enableColumnFilter: false,
        Cell: ({ row }) => (
          <>
            <div className=" ml-5 flex gap-10 ">
              <EditButton
                label={"Edit"}
                icon={EditingIcon}
                onClick={() => {
                  if (permissions.update === 0) {
                    setShowAlertConfirmation(true);
                    setMessage("Update permission not allowed");
                  } else {
                    handleEdit(row.original);
                  }
                }}
              />

              <EditButton
                label={"Delete"}
                icon={TrashIcon}
                onClick={() => {
                  if (permissions.delete === 0) {
                    setShowAlertConfirmation(true);
                    setMessage("Delete permission not allowed");
                  } else {
                    handleDelete(row.original);
                  }
                }}
              />
            </div>
          </>
        ),
      },
    ],
    []
  );
  //console.log("users", users);
  return (
    <>
      <div className="bg-bgPrimary">
        <div className="mt-5">
          <Title title={"List of My Cards"} />
        </div>
        <div className="border border-listBorder bg-white p-3 mt-5">
          <div className="mx-3 mt-3">
            <div className="mt-5">
              {!cardLoading &&
              cardList?.data?.totalCount === 0 &&
              searchInput === "" ? (
                <>
                  <div className="border border-outerBorder mb-5">
                    <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                      {"Oops ! no records to display."}
                    </div>
                    <div className="flex justify-center text-tabColor text-base font-semibold mt-5 ">{`You have not added any cards yet.`}</div>
                    <div className="flex justify-center my-12">
                      <img
                        src={bgImage}
                        style={{
                          height: "10%",
                          width: "10%",
                          objectFit: "cover",
                        }}
                        alt="bg"
                      />
                    </div>
                    <div className="flex justify-center mb-5">
                      <Button
                        buttonClassName="text-xs w-36 text-white h-10 "
                        label={"+Add New Card"}
                        onClick={() => {
                          setEdit(false);
                          if (permissions.create === 1) {
                            openCard();
                          } else {
                            handleOpenInfoModal(true);
                            setMessage("Create permission not allowed");
                          }
                        }}
                      ></Button>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  {" "}
                  <div className="mx-3">
                    <InputLabel label={" Card Name"} />
                    <div class="w-full flex mt-3">
                      <div className=" w-1/2 ">
                        <input
                          type="text"
                          style={{
                            border: `1px solid ${theme.borderColor.outerBorder}`,
                            paddingLeft: "2rem",
                          }}
                          className="w-full px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                          placeholder="Search Card"
                          value={searchInput}
                          onChange={(e) => {
                            setSearchInput(e.target.value);
                            setCurrentPage(1);
                          }}
                        />

                        <div
                          class="top-0 right-0  mr-4"
                          style={{
                            marginLeft: "0.25rem",
                            marginTop: "-1.7rem",
                          }}
                        >
                          <SearchIcon className="w-5 h-5" />
                        </div>
                      </div>
                      <div className="flex-grow flex justify-end items-center gap-3">
                        <Button
                          buttonClassName="text-xs w-36 text-white h-10 "
                          label={"+Add New Card"}
                          onClick={() => {
                            setEdit(false);
                            if (permissions.create === 1) {
                              openCard();
                            } else {
                              handleOpenInfoModal(true);
                              setMessage("Create permission not allowed");
                            }
                          }}
                        ></Button>
                      </div>
                    </div>
                  </div>
                  <div className="mt-5 mx-3">
                    <ReportTable
                      columns={columns}
                      data={Array.isArray(users) ? users : []}
                      isLoading={isFetching}
                    />
                  </div>
                  {cardList?.data &&
                    cardList?.data?.totalCount !== undefined && (
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          margin: "20px 0px 20px 20px",
                          width: "95%",
                        }}
                      >
                        <div className="flex ">
                          <div>
                            <ResultPerPageComponent
                              countPerPage={resultPerPage}
                              limit={limitPerPage}
                              handleLimitChange={handleLimitChange}
                            />
                          </div>

                          <div
                            style={{
                              display: "flex",
                              fontSize: "14px",
                              padding: "10px 0px 0px 10px",
                              color: theme.textColor.titleColor,
                            }}
                          >
                            {(currentPage - 1) * limitPerPage + 1} -{" "}
                            {Math.min(
                              limitPerPage * currentPage,
                              cardList?.data?.totalCount
                            )}{" "}
                            of {cardList?.data?.totalCount} rows
                          </div>
                        </div>
                        <Pagination
                          className="pagination-bar"
                          currentPage={currentPage}
                          totalCount={cardList?.data?.totalCount}
                          pageSize={limitPerPage}
                          onPageChange={(page) => {
                            handlePageChange(page);
                          }}
                        />
                      </div>
                    )}{" "}
                </>
              )}
            </div>
          </div>
        </div>
        <AddCard
          openCard={openDialog}
          closeCard={closeCard}
          edit={edit}
          selectedDetails={selectedDetails}
          refreshData={refetch}
        />
        <DeleteDialog
          show={deleteDialog}
          onHide={() => setDeleteDialog(false)}
          onConfirm={() => {
            setIsLoading(true);
            cardServices
              .deleteCard(id)
              .then((res) => {
                setDeleteDialog(false);
                setCurrentPage(1);
                refetch();
                setSuccessDialog(true);
                setMessage("Card deleted successfully");
              })

              .catch((error) => {
                setDeleteDialog(false);
                setErrorDialog(true);
                setMessage("Something went wrong ! Try Again");
                // console.error("Error deleting card:", error);
              })
              .finally(() => {
                setIsLoading(false);
                //setSearchStr("");
              });
          }}
          title={"Are you sure to delete the card ?"}
          isLoading={isLoading}
        />
        <SuccessDialog
          show={suceessDialog}
          onHide={() => setSuccessDialog(false)}
          message={message}
        />
        <ErrorDialog
          show={errorDialog}
          onHide={() => setErrorDialog(false)}
          message={message}
        />
        <InfoModal
          icon={<Alert className="w-10 h-10 " />}
          show={showAlertConfirmation}
          onHide={handleCloseInfoModal}
          message={message}
        />
      </div>
    </>
  );
}

export default Cards;
