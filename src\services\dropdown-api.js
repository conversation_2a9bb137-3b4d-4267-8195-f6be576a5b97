import axios from "axios";
//import { config } from "../assets/config/config";
import getAPIMap from "../routes/ApiUrls";

//const apiUrl = config.api.url;

export async function getVisualizationType(options) {
  let url = getAPIMap("visualizationType");
  let response = axios.get(url);
  return response;
}

export async function getPanelProperties(options) {
  //console.log("options", options);
  let url = getAPIMap("panelProperty") + `?type=${options.queryKey[1]}`;
  let response = axios.get(url);
  return response;
}

export async function supplierList(options) {
  //console.log("options", options);
  let url = getAPIMap("supplierData");
  let response = axios.get(url);
  return response;
}
export async function customerList(options) {
  //console.log("options", options);
  let url = getAPIMap("customerData");
  let response = axios.get(url);
  return response;
}
export async function destinationCountry(options) {
  const type = "country";
  let url = getAPIMap("destinationReports") + `?type=${type}`;
  let response = axios.get(url);
  return response;
}

export async function destinationName(options) {
  const type = "operator";
  let url = getAPIMap("destinationReports") + `?type=${type}`;
  let response = axios.get(url);
  return response;
}

export async function getdestination({ queryKey }) {
  const bilateralFlag = queryKey[1] ? "?search=bilateral_flag=1" : "";
  const url = getAPIMap("destinationReports") + bilateralFlag;

  const response = axios.get(url);
  return response;
}
