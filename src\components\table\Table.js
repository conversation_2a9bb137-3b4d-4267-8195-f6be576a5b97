import React, { useState } from "react";
import { useTable, useFilters } from "react-table";
import ColumnSearch from "./ColumnSearch";
import { CircularProgress } from "@mui/material";

const Table = ({
  columns,
  data,
  isLoading,
  onRowClick,
  dimension,
  isChart,
  align,
}) => {
  const [hoveredRowIndex, setHoveredRowIndex] = useState(null);

  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } =
    useTable({ columns, data }, useFilters);

  const containerStyles = {
    width: dimension ? dimension.w * 140 : "100%",
    height: dimension ? dimension.h * 70 : "100%",
    overflowX: "auto",
  };

  const tableStyles = {
    width: "100%",
    borderCollapse: "collapse",
    marginTop: "20px",
  };

  const cellStyles = {
    padding: "12px 15px 12px",
    textAlign: "left",
    fontSize: "12px",
    borderRight: "0",
    borderLeft: "0",
    fontWeight: 500,
    cursor: "pointer",
  };

  const headerStyles = {
    padding: "12px 12px 12px",
  };

  const shouldExcludeFilter = (column) => {
    return (
      column.id === "create" ||
      column.id === "view" ||
      column.id === "update" ||
      column.id === "delete" ||
      column.id === "toggle" ||
      column.id === "download" ||
      column.id === "isUserActive"
    );
  };

  return (
    <div style={containerStyles}>
      <table
        style={tableStyles}
        className={` ${
          !isLoading && (data.length === 0 || rows.length === 0)
            ? ""
            : "border border-tableBorder"
        }`}
        {...getTableProps()}
      >
        <thead>
          {headerGroups.map((headerGroup) => (
            <tr {...headerGroup.getHeaderGroupProps()}>
              {headerGroup.headers.map((column) => (
                <th
                  className="bg-tableHeader border-b border-tableCellBorder"
                  style={{ ...cellStyles, ...headerStyles }}
                  {...column.getHeaderProps()}
                >
                  <div className="flex flex-col w-auto ">
                    <div className="whitespace-nowrap">
                      {column.render("Header")}
                    </div>

                    {!isChart &&
                    column.Header !== "" &&
                    column.canFilter &&
                    !shouldExcludeFilter(column) ? (
                      <ColumnSearch column={column} />
                    ) : null}
                  </div>
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody {...getTableBodyProps()}>
          {isLoading && (
            <tr>
              <td
                colSpan={columns.length}
                style={{
                  ...cellStyles,
                }}
                className="border-b border-tableCellBorder"
              >
                <div className="my-3 flex justify-center items-center">
                  <CircularProgress size={30} />
                </div>
              </td>
            </tr>
          )}
          {!isLoading && (data.length === 0 || rows.length === 0) && (
            <tr>
              <td
                colSpan={columns.length}
                style={{
                  ...cellStyles,
                  textAlign: "center",
                  padding: "20px",
                  fontSize: "16px",
                  // fontWeight: "bold",
                }}
                // className="border border-solid border-transparent"
              >
                No records to display
              </td>
            </tr>
          )}
          {!isLoading &&
            data.length > 0 &&
            rows.map((row, index) => {
              prepareRow(row);
              return (
                <tr
                  key={index}
                  {...row.getRowProps()}
                  onMouseEnter={() => setHoveredRowIndex(index)}
                  onMouseLeave={() => setHoveredRowIndex(null)}
                  onClick={() => onRowClick && onRowClick(row)}
                  style={cellStyles}
                  className="hover:bg-rowHoverBackground border-b border-tableCellBorder"
                >
                  {row.cells.map((cell) => (
                    <td style={cellStyles} {...cell.getCellProps()}>
                      {cell.render("Cell")}
                    </td>
                  ))}
                </tr>
              );
            })}
        </tbody>
      </table>
    </div>
  );
};

export default Table;
