import React, { useMemo, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { NO_DATA_MESSAGE } from "../../common/constants";

// Custom Tooltip Component
const CustomTooltip = ({ active, payload, label, hiddenLines = new Set() }) => {
  if (!active || !payload || !payload.length) return null;

  // Filter out hidden lines from tooltip
  const visiblePayload = payload.filter(
    (entry) => !hiddenLines.has(entry.dataKey)
  );

  if (visiblePayload.length === 0) return null;

  // Limit to 9 items maximum
  const displayPayload = visiblePayload.slice(0, 12);
  const hasMore = visiblePayload.length > 12;

  return (
    <div className="bg-white p-4 border border-gray-300 rounded-lg shadow-lg text-xs min-w-64 max-w-80">
      <p className="mb-3 font-bold text-sm text-gray-800">{`Date: ${label}`}</p>
      <div className="space-y-2">
        {displayPayload.map((entry, index) => (
          <div
            key={index}
            className="flex justify-between items-center min-w-52"
          >
            <div className="flex items-center flex-1 mr-4">
              <span
                className="font-medium text-xs"
                style={{ color: entry.stroke }}
              >
                {entry.name}
              </span>
            </div>
            <span className="text-gray-900 font-bold text-xs flex-shrink-0">
              {entry.value?.toLocaleString() ?? 0}
            </span>
          </div>
        ))}
        {hasMore && (
          <div className="flex justify-center items-center pt-2 border-t border-gray-200">
            <span className="text-gray-500 text-xs italic">
              ... and {visiblePayload.length - 12} more
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

// Custom Legend Component
const CustomLegend = ({ payload = [], hiddenLines, onToggleLine }) => {
  const [showFullLegend, setShowFullLegend] = useState(false);

  const visibleItems = showFullLegend ? payload : payload.slice(0, 12);
  const visibleLineCount = payload.length - hiddenLines.size;

  return (
    <div className="bg-white border border-gray-400 rounded-lg p-2 m-5">
      <div className="flex flex-wrap gap-6 items-center justify-start">
        {visibleItems.map((entry, index) => {
          const isHidden = hiddenLines.has(entry.id);
          const canHide = visibleLineCount > 1;
          const canClick = isHidden || canHide;

          return (
            <div
              key={index}
              onClick={() => canClick && onToggleLine(entry.id)}
              className={`
                flex items-center gap-2 transition-all duration-200
                ${canClick ? "cursor-pointer" : "cursor-not-allowed opacity-70"}
                ${canClick ? "hover:opacity-80" : ""}
              `}
            >
              <span
                className={`
                  w-3 h-3 rounded-full flex-shrink-0
                  ${isHidden ? "opacity-30" : ""}
                `}
                style={{
                  backgroundColor: entry.color,
                }}
              />
              <span
                className={`
                  text-xs font-medium
                `}
                style={{
                  color: entry.color,
                  textDecoration: isHidden ? "line-through" : "none",
                }}
              >
                {entry.value}
              </span>
            </div>
          );
        })}
      </div>
      {payload.length > 12 && (
        <div className="mt-3 text-center">
          <button
            onClick={() => setShowFullLegend((prev) => !prev)}
            className="text-blue-600 hover:text-blue-800 transition-colors text-xs font-medium"
          >
            {showFullLegend ? "Show Less ▲" : "Show All ▼"}
          </button>
        </div>
      )}
    </div>
  );
};

// Main Chart Component
const LineChartGraph = ({ data = [], config = {} }) => {
  const [hiddenLines, setHiddenLines] = useState(new Set());

  const handleToggleLine = (lineId) => {
    setHiddenLines((prev) => {
      const newSet = new Set(prev);
      const currentVisibleCount = uniqueKeys.length - prev.size;

      // Prevent hiding if it's the last visible line
      if (newSet.has(lineId)) {
        // Unhiding - always allowed
        newSet.delete(lineId);
      } else {
        // Hiding - only allow if more than 1 line will remain visible
        if (currentVisibleCount > 1) {
          newSet.add(lineId);
        }
      }
      return newSet;
    });
  };

  // Reset hidden lines when data or config changes significantly
  React.useEffect(() => {
    setHiddenLines(new Set());
  }, [data.length, config?.xAxis, config?.yAxis, config?.fieldBasedValue]);

  const xAxisField = useMemo(() => {
    const field = config?.xAxis || "Date";
    // Filter out "Select All" from xAxisField
    return field === "Select All" ? "Date" : field;
  }, [config?.xAxis]);
  const yAxisFields = useMemo(() => {
    const fields = config?.yAxis || ["Count"];
    // Filter out "Select All" from yAxisFields
    return Array.isArray(fields)
      ? fields.filter((field) => field !== "Select All")
      : [fields].filter((field) => field !== "Select All");
  }, [config?.yAxis]);
  const groupByField = useMemo(() => {
    const field = config?.fieldBasedValue || "Customer";
    // Filter out "Select All" from groupByField
    return field === "Select All" ? "Customer" : field;
  }, [config?.fieldBasedValue]);

  const uniqueKeys = useMemo(() => {
    const keys = new Set();

    // Process data to handle empty strings
    const processedData = data.map((item) => {
      const processedItem = { ...item };

      // Handle empty string in xAxisField
      if (processedItem[xAxisField] === "") {
        processedItem[xAxisField] = "Empty String";
      }

      // Handle empty string in groupByField (fieldBasedValue)
      if (processedItem[groupByField] === "") {
        processedItem[groupByField] = "Empty String";
      }

      // Handle empty strings in yAxisFields
      yAxisFields.forEach((field) => {
        if (processedItem[field] === "") {
          processedItem[field] = "Empty String";
        }
      });

      return processedItem;
    });

    // Filter data to exclude "Select All" values
    const filteredData = processedData.filter(
      (item) =>
        item[xAxisField] !== "Select All" &&
        item[groupByField] !== "Select All" &&
        yAxisFields.some((field) => item[field] !== "Select All")
    );

    filteredData.forEach((item) => {
      const group = item[groupByField];
      yAxisFields.forEach((field) => {
        keys.add(`${group}_${field}`);
      });
    });
    return Array.from(keys).sort();
  }, [data, groupByField, yAxisFields, xAxisField]);

  const groupedData = useMemo(() => {
    if (!data?.length) return {};

    // Process data to handle empty strings and filter "Select All" values
    const processedData = data.map((item) => {
      const processedItem = { ...item };

      // Handle empty string in xAxisField
      if (processedItem[xAxisField] === "") {
        processedItem[xAxisField] = "Empty String";
      }

      // Handle empty string in groupByField (fieldBasedValue)
      if (processedItem[groupByField] === "") {
        processedItem[groupByField] = "Empty String";
      }

      // Handle empty strings in yAxisFields
      yAxisFields.forEach((field) => {
        if (processedItem[field] === "") {
          processedItem[field] = "Empty String";
        }
      });

      return processedItem;
    });

    // Filter data to exclude "Select All" values
    const filteredData = processedData.filter(
      (item) =>
        item[xAxisField] !== "Select All" &&
        item[groupByField] !== "Select All" &&
        yAxisFields.some(
          (field) => item[field] !== undefined && item[field] !== "Select All"
        )
    );

    const result = {};
    const allXValues = new Set();
    const allGroups = new Set();

    // Collect all x-axis values and group names from filtered data
    filteredData.forEach((item) => {
      if (item[xAxisField] !== "Select All") {
        allXValues.add(item[xAxisField]);
      }
      if (item[groupByField] !== "Select All") {
        allGroups.add(item[groupByField]);
      }
    });

    // Initialize with all combinations (fill missing as 0)
    allXValues.forEach((xVal) => {
      result[xVal] = { [xAxisField]: xVal };
      allGroups.forEach((group) => {
        yAxisFields.forEach((yField) => {
          const key = `${group}_${yField}`;
          result[xVal][key] = 0; // Synthetic zero value
        });
      });
    });

    // Fill in actual values where present from filtered data
    filteredData.forEach((item) => {
      const xVal = item[xAxisField];
      const group = item[groupByField];
      yAxisFields.forEach((yField) => {
        const key = `${group}_${yField}`;
        if (item[yField] !== undefined && item[yField] !== "Select All") {
          result[xVal][key] = item[yField];
        }
      });
    });

    return result;
  }, [data, xAxisField, yAxisFields, groupByField]);

  const chartData = useMemo(() => Object.values(groupedData), [groupedData]);

  // Calculate dynamic interval based on data length
  const tickInterval = useMemo(() => {
    const dataLength = chartData.length;
    if (dataLength <= 5) return 0; // Show all ticks for small datasets
    if (dataLength <= 10) return 1; // Show every other tick
    if (dataLength <= 20) return 2; // Show every 3rd tick
    if (dataLength <= 50) return 4; // Show every 5th tick
    if (dataLength <= 100) return 9; // Show every 10th tick
    return Math.floor(dataLength / 10); // For very large datasets, show ~10 ticks
  }, [chartData.length]);

  const colorMap = useMemo(() => {
    const map = {};
    uniqueKeys.forEach((key, index) => {
      // Generate unique colors using golden ratio for better distribution
      const hue = (index * 137.5) % 360;
      const saturation = 65 + (index % 3) * 10; // 65%, 75%, 85%
      const lightness = 45 + (index % 4) * 8; // 45%, 53%, 61%, 69%

      map[key] = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    });
    return map;
  }, [uniqueKeys]);

  // Create Y-axis configurations for each metric (no color coordination)
  const yAxisConfigs = useMemo(() => {
    return yAxisFields.map((metric, index) => {
      const orientation = index % 2 === 0 ? "left" : "right";
      const offset = Math.floor(index / 2) * 80;

      return {
        metric,
        yAxisId: `yAxis-${index}`,
        orientation,
        offset,
      };
    });
  }, [yAxisFields]);

  // Group lines by metric for rendering with appropriate yAxisId
  const linesByMetric = useMemo(() => {
    const grouped = {};

    uniqueKeys.forEach((key) => {
      const metric = key.split("_").pop();
      if (!grouped[metric]) {
        grouped[metric] = [];
      }
      grouped[metric].push(key);
    });

    return grouped;
  }, [uniqueKeys]);

  // Generate lines with appropriate yAxisId for each metric
  const generateLinesForMetric = (metric, metricIndex) => {
    const metricLines = linesByMetric[metric] || [];
    const yAxisId = `yAxis-${metricIndex}`;

    return metricLines
      .filter((key) => !hiddenLines.has(key)) // Filter out hidden lines
      .map((key) => ({
        key,
        dataKey: key,
        name: key.replace("_", " - "),
        stroke: colorMap[key],
        type: "monotone",
        strokeWidth: 2,
        dot: { r: 2 },
        activeDot: { r: 6 },
        yAxisId,
      }));
  };

  if (!data?.length)
    return (
      <div className="w-full h-60 flex justify-center items-center text-gray-500 font-medium">
        {NO_DATA_MESSAGE}
      </div>
    );

  // Generate all line configs for legend (don't filter any)
  const allLineConfigs = uniqueKeys.map((key) => ({
    key,
    dataKey: key,
    name: key.replace("_", " - "),
    stroke: colorMap[key],
    type: "monotone",
    strokeWidth: 2,
    dot: { r: 2 },
    activeDot: { r: 6 },
  }));

  return (
    <>
      <div className="w-full h-80">
        <ResponsiveContainer>
          <LineChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 0, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis
              dataKey={xAxisField}
              label={{
                value: xAxisField,
                position: "insideBottom",
                offset: -10,
              }}
              tick={{ fontSize: 12, fontStyle: "bold", fill: "black" }}
              tickMargin={5}
              interval={tickInterval}
            />

            {/* Render multiple Y-axes with labels (no color coordination) */}
            {yAxisConfigs.map((yAxisConfig) => (
              <YAxis
                key={yAxisConfig.yAxisId}
                yAxisId={yAxisConfig.yAxisId}
                orientation={yAxisConfig.orientation}
                offset={yAxisConfig.offset}
                width={60} // Fixed width for consistent spacing
                tick={{
                  fontSize: 10,
                  fill: "black",
                  fontWeight: "bold",
                }}
                axisLine={{
                  stroke: "black",
                  strokeWidth: 1,
                  opacity: 0.8,
                }}
                tickLine={{
                  stroke: "black",
                  strokeWidth: 1,
                  opacity: 0.6,
                }}
                tickFormatter={(value) => {
                  if (value >= 100000) {
                    return `${(value / 100000).toFixed(1)}L`;
                  } else if (value >= 1000) {
                    return `${(value / 1000).toFixed(1)}K`;
                  }
                  return value?.toString() || "0";
                }}
                label={{
                  value: yAxisConfig.metric,
                  angle: -90,
                  position:
                    yAxisConfig.orientation === "left"
                      ? "insideLeft"
                      : "insideRight",
                  offset: 20,
                  style: {
                    textAnchor: "middle",
                    fill: "black",
                    fontSize: "11px",
                    fontWeight: "bold",
                  },
                }}
              />
            ))}

            <Tooltip
              content={<CustomTooltip hiddenLines={hiddenLines} />}
              wrapperStyle={{ zIndex: 1000, outline: "none" }}
            />

            {/* Render Lines for each metric */}
            {yAxisFields.map((metric, index) =>
              generateLinesForMetric(metric, index).map((lineProps) => (
                <Line key={lineProps.key} {...lineProps} />
              ))
            )}
          </LineChart>
        </ResponsiveContainer>
      </div>
      <CustomLegend
        payload={allLineConfigs.map((line) => ({
          color: line.stroke,
          value: line.name,
          id: line.key,
        }))}
        hiddenLines={hiddenLines}
        onToggleLine={handleToggleLine}
      />
    </>
  );
};

export default LineChartGraph;
