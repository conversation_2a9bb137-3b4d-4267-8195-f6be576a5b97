import React from "react";

const IdleWarningModal = ({ isOpen, countdown, onStayLoggedIn, onLogout }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
        <div className="text-center">
          <div className="mb-4">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
              <svg
                className="h-6 w-6 text-yellow-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
          </div>

          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Session Timeout Warning
          </h3>

          <p className="text-sm text-gray-500 mb-4">
            You will be automatically logged out in{" "}
            <span className="font-semibold text-red-600">{countdown}</span>{" "}
            {countdown === 1 ? "second" : "seconds"} due to inactivity.
          </p>

          <div className="flex space-x-3 justify-center">
            <button
              type="button"
              className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-bgSecondary border border-transparent rounded-md  focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 "
              onClick={onStayLoggedIn}
            >
              Stay Logged In
            </button>

            <button
              type="button"
              className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-gray-500"
              onClick={onLogout}
            >
              Logout Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IdleWarningModal;
