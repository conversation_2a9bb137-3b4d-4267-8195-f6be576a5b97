import dayjs from "dayjs";
import {
  USER_ANALYSIS_TIME_RANGES as TIME_RANGES,
  USER_ANALYSIS_HOUR_MAPPINGS as HOUR_MAPPINGS,
  USER_ANALYSIS_VISUALIZATION_TYPES as VISUALIZATION_TYPES,
} from "../common/constants";

/**
 * Formats date range based on selected time range
 * @param {string} selectedRange - The selected time range
 * @returns {object} Object containing formattedStart and formattedEnd dates
 */
export const formatDateRange = (selectedRange) => {
  if (!selectedRange) return { formattedStart: "", formattedEnd: "" };

  const currentDateTime = dayjs();

  // Handle custom date ranges with "to" separator
  if (selectedRange.includes("to")) {
    const [startString, endString] = selectedRange.split("to");
    return {
      formattedStart: startString.trim(),
      formattedEnd: endString.trim(),
    };
  }

  // Handle hourly ranges
  if (HOUR_MAPPINGS[selectedRange]) {
    const hours = HOUR_MAPPINGS[selectedRange];
    const lastXHours = currentDateTime.subtract(hours, "hour");

    const formattedStart =
      selectedRange === TIME_RANGES.LAST_HOUR
        ? lastXHours.format("YYYY-MM-DD HH:mm:ss")
        : lastXHours.format("YYYY-MM-DD HH:00:00");

    const formattedEnd =
      selectedRange === TIME_RANGES.LAST_HOUR
        ? currentDateTime.format("YYYY-MM-DD HH:mm:ss")
        : currentDateTime.format("YYYY-MM-DD HH:00:00");

    return { formattedStart, formattedEnd };
  }

  // Handle specific time ranges
  const formatters = {
    [TIME_RANGES.TODAY]: () => ({
      formattedStart: currentDateTime
        .startOf("day")
        .format("YYYY-MM-DD HH:mm:ss"),
      formattedEnd: currentDateTime.format("YYYY-MM-DD HH:mm:ss"),
    }),
    [TIME_RANGES.YESTERDAY]: () => {
      const yesterday = currentDateTime.subtract(1, "day");
      return {
        formattedStart: yesterday.startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        formattedEnd: yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
      };
    },
    [TIME_RANGES.LAST_SEVEN_DAYS]: () => ({
      formattedStart: currentDateTime
        .subtract(6, "days")
        .startOf("day")
        .format("YYYY-MM-DD HH:00:00"),
      formattedEnd: currentDateTime.format("YYYY-MM-DD HH:59:59"),
    }),
    [TIME_RANGES.LAST_WEEK]: () => ({
      formattedStart: currentDateTime
        .subtract(1, "week")
        .startOf("week")
        .format("YYYY-MM-DD HH:00:00"),
      formattedEnd: currentDateTime
        .subtract(1, "week")
        .endOf("week")
        .format("YYYY-MM-DD HH:59:59"),
    }),
    [TIME_RANGES.LAST_30_DAYS]: () => ({
      formattedStart: currentDateTime
        .subtract(29, "days")
        .startOf("day")
        .format("YYYY-MM-DD HH:00:00"),
      formattedEnd: currentDateTime.endOf("day").format("YYYY-MM-DD HH:59:59"),
    }),
    [TIME_RANGES.LAST_MONTH]: () => ({
      formattedStart: currentDateTime
        .subtract(1, "month")
        .startOf("month")
        .format("YYYY-MM-DD HH:00:00"),
      formattedEnd: currentDateTime
        .subtract(1, "month")
        .endOf("month")
        .format("YYYY-MM-DD HH:59:59"),
    }),
    [TIME_RANGES.THIS_MONTH]: () => ({
      formattedStart: currentDateTime
        .startOf("month")
        .format("YYYY-MM-DD HH:mm:ss"),
      formattedEnd: currentDateTime.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
    }),
  };

  const formatter = formatters[selectedRange];
  return formatter ? formatter() : { formattedStart: "", formattedEnd: "" };
};

/**
 * Builds request data for API calls
 * @param {object} payload - The payload data
 * @param {string} formattedStart - Start date
 * @param {string} formattedEnd - End date
 * @param {string} userId - User ID
 * @returns {object} Built request data
 */
export const buildRequestData = (
  payload,
  formattedStart,
  formattedEnd,
  userId
) => {
  const panelDetails = payload.panelDetails || {};
  const dataColumns = payload.dataColumns || panelDetails.dataColumns || {};

  const reqData = {
    name: payload.name || panelDetails.name,
    visualizationType:
      payload.visualizationType || panelDetails.visualizationType,
    filters: [],
    dataColumns: {
      derivedFields: dataColumns.derivedFields || [],
    },
    startDate: formattedStart,
    endDate: formattedEnd,
    userAnalysis: true,
    userId,
  };

  const vizType = reqData.visualizationType;

  // Configure data columns based on visualization type
  if (vizType === VISUALIZATION_TYPES.BAR_GRAPH) {
    reqData.dataColumns["X-Axis"] = dataColumns["X-Axis"];
    reqData.dataColumns.noOfRecords = parseInt(
      dataColumns.noOfRecords || 0,
      10
    );
  } else {
    reqData.dataColumns.tableFields = dataColumns.tableFields;
  }

  // Add interval for line and multiaxis graphs
  if (
    vizType === VISUALIZATION_TYPES.LINE_GRAPH ||
    vizType === VISUALIZATION_TYPES.MULTIAXIS_GRAPH
  ) {
    reqData.interval = payload.interval || panelDetails.interval;
  }

  // Process filters
  const filters = payload.filters || panelDetails.filters || [];
  reqData.filters = filters.map((condition) => ({
    field: condition.field,
    condition: condition.condition,
    value: condition.value,
    operator: condition.operator,
  }));

  return reqData;
};

/**
 * Gets grid dimensions based on order
 * @param {object} dimension - Dimension object with order
 * @returns {object} Grid dimensions
 */
export const getGridDimensions = (dimension) => {
  const dim = { x: 0, y: 0, w: dimension.w, h: dimension.h };

  switch (dimension.order - 1) {
    case 0:
      return dim;
    case 1:
      return { ...dim, y: 3 };
    case 2:
      return { ...dim, x: 6 };
    case 3:
      return { ...dim, y: 3 };
    case 4:
      return { ...dim, x: 6, y: 3 };
    default:
      return dim;
  }
};
