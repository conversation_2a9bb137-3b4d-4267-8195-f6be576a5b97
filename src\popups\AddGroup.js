import React, { useEffect, useState } from "react";
import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import Dialog from "@mui/material/Dialog";
import TextFieldWrapper from "../components/FormsUI/TextField";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Box from "@mui/material/Box";
import Button from "../components/Button/OutlinedButton";
import CancelButton from "../components/Button/Button";
import ChipInput from "../components/Chip/ChipInput";
import { CloseIcon } from "../icons";
import SuccessDialog from "./SuccessDialog";
import ErrorDialog from "./ErrorDialog";
import { alertServices } from "../services/alert-api";
import Select from "../components/FormsUI/Select";
import { useQuery, useQueryClient } from "react-query";
import { RadioGroup, Radio } from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import {
  emailValidation,
  emailValidationNoRequired,
} from "../common/yupValidation";

export default function AddGroup({
  closeGroupDialog,
  openGroupDialog,
  edit,
  selectedDetails,
  refreshData,
}) {
  const onlySpaceRegex = /(?=.*[a-z])|(?=.*[A-Z])/;
  const [successDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [errorDialog, setErrorDialog] = useState(false);
  const [groups, setGroups] = useState([]);
  const [filteredGroups, setFilteredGroups] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [groupType, setGroupType] = useState("Master");

  const id = selectedDetails?.id;
  const queryClient = useQueryClient();

  useEffect(() => {
    if (edit && selectedDetails) {
      setGroupType(selectedDetails?.isSubGroup ? "Member" : "Master");
    }
  }, [edit, selectedDetails]);

  const handleChipRemove = (index, formikBag) => {
    const newChips = [...formikBag.values.chips];
    newChips.splice(index, 1);
    formikBag.setFieldValue("chips", newChips);
  };

  const handleChipAdd = (chip, formikBag) => {
    formikBag.setFieldValue("chips", [...formikBag.values.chips, chip]);
  };

  const handleChipDataRemove = (index, formikBag) => {
    const newChips = [...formikBag.values.chipData];
    newChips.splice(index, 1);
    formikBag.setFieldValue("chipData", newChips);
  };

  const handleChipDataAdd = (chip, formikBag) => {
    formikBag.setFieldValue("chipData", [...formikBag.values.chipData, chip]);
  };

  const masterValidationSchema = Yup.object({
    groupName: Yup.string()
      .required("Group Name is Required")
      .max(256, "Max length allowed is 256 characters")
      .min(2, "Min length allowed is 2 characters")
      .matches(/(?=.*[a-z])|(?=.*[A-Z])/, "Must contain at least one Alphabet"),
    chips: Yup.array()
      .test("is-min-of-6", "Min length allowed is 6", function (value) {
        if (!value) return true;
        return value.every((email) => Yup.string().min(6).isValidSync(email));
      })
      .test("is-max-of-256", "Max characters allowed is 256", function (value) {
        if (!value) return true;
        return value.every((email) => Yup.string().max(256).isValidSync(email));
      })
      .test("is-valid-emails", "Invalid email address", function (value) {
        if (!value) return true;
        return value.every((email) => Yup.string().email().isValidSync(email));
      }),
    existingGroups: Yup.array().test(
      "existingGroups",
      "At least one of emails or groups to be added",
      function (value) {
        const { chips } = this.parent;
        return (chips?.length || 0) >= 1 || (value?.length || 0) >= 1;
      }
    ),
  });

  const memberValidationSchema = Yup.object({
    groupName: Yup.string()
      .required("Group Name is Required")
      .max(256, "Max length allowed is 256 characters")
      .min(2, "Min length allowed is 2 characters")
      .matches(/(?=.*[a-z])|(?=.*[A-Z])/, "Must contain at least one Alphabet"),
    chipData: Yup.array()
      .min(1, "Email is required")
      .test("is-min-of-6", "Min length allowed is 6", function (value) {
        if (!value) return true;
        return value.every((email) => Yup.string().min(6).isValidSync(email));
      })
      .test("is-max-of-256", "Max characters allowed is 256", function (value) {
        if (!value) return true;
        return value.every((email) => Yup.string().max(256).isValidSync(email));
      })
      .test("is-valid-emails", "Invalid email address", function (value) {
        if (!value) return true;
        return value.every((email) => Yup.string().email().isValidSync(email));
      }),
  });

  const {
    data,
    isLoading: groupLoading,
    isFetching,
    refetch,
  } = useQuery(["groupList", "", "", "", true], alertServices.getAll, {
    onSuccess: (data) => {
      let filterData = data?.data?.data?.filter((x) => {
        return x.name !== selectedDetails?.name;
      });
      let newData = filterData.map((x) => {
        return { label: x.name, value: x.id };
      });

      setGroups(newData);
    },
  });

  useEffect(() => {
    if (groups && groups?.length > 0 && selectedDetails?.name) {
      let filter = groups?.filter((x) => {
        return x?.label !== selectedDetails?.name;
      });

      setFilteredGroups(filter);
    }
  }, [selectedDetails, groups]);

  return (
    <Box width={"100%"}>
      <Formik
        initialValues={{
          groupName: edit ? selectedDetails?.name : "",
          chips: edit ? selectedDetails?.members || [] : [],
          chipData: edit ? selectedDetails?.members || [] : [],
          existingGroups: edit
            ? selectedDetails?.subGroups?.map((x) => x.id) || []
            : [],
        }}
        validationSchema={
          groupType === "Master"
            ? masterValidationSchema
            : memberValidationSchema
        }
        enableReinitialize={true}
        onSubmit={(
          { groupName, chips, existingGroups, chipData },
          { setSubmitting }
        ) => {
          let payload = {
            groupName: groupName,
            chips: groupType === "Master" ? chips : chipData,
            subGroups: existingGroups,
            subGroupState: groupType === "Member" ? true : false,
          };
          if (edit) {
            setLoading(true);
            alertServices
              .updateGroup(payload, id)
              .then((res) => {
                refreshData(1, "");

                setSuccessDialog(true);
                setMessage("Group updated successfully");
                closeGroupDialog();
                refetch();
              })
              .catch(({ response }) => {
                setErrorDialog(true);
                setMessage(
                  response?.data?.message ||
                    "Something went wrong! Please check the connectivity!"
                );
                setSubmitting(false);
                closeGroupDialog();
              })
              .finally(() => {
                setLoading(false);
              });
          } else {
            setLoading(true);
            alertServices
              .createGroup(payload)
              .then((res) => {
                refreshData(1, "");
                queryClient.invalidateQueries("groupList");
                setSuccessDialog(true);
                setMessage("Group created successfully");
                closeGroupDialog();
                refetch();
              })
              .catch((err) => {
                setErrorDialog(true);
                setMessage(
                  err?.response?.data?.message ||
                    "Something went wrong! Please check the connectivity!"
                );
                setSubmitting(false);
                closeGroupDialog();
              })
              .finally(() => {
                setLoading(false);
              });
          }
        }}
      >
        {({ setFieldValue, resetForm }) => (
          <>
            <Dialog
              open={openGroupDialog}
              onClose={() => {
                closeGroupDialog();
                resetForm();
                setGroupType(
                  edit && selectedDetails?.isSubGroup ? "Member" : "Master"
                );
              }}
              fullWidth
              onClick={(event) => {
                if (event.target === event.currentTarget) {
                  closeGroupDialog();
                  resetForm();
                  setGroupType(
                    edit && selectedDetails?.isSubGroup ? "Member" : "Master"
                  );
                }
              }}
              sx={{
                "& .MuiDialog-container": {
                  "& .MuiPaper-root": {
                    width: "100%",
                    maxWidth: "450px",
                    margin: 0,
                  },
                },
              }}
            >
              <Form
                encType="multipart/form-data"
                onKeyPress={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                  }
                }}
              >
                <div className="mx-4 mb-5">
                  <div className="mt-4 text-black text-base font-medium flex items-center justify-between">
                    {edit ? "Edit Group" : "Add Group"}
                    <CloseIcon
                      onClick={() => {
                        closeGroupDialog();
                        resetForm();
                        setGroupType(
                          edit && selectedDetails?.isSubGroup
                            ? "Member"
                            : "Master"
                        );
                      }}
                      className="w-2.5 h-2.5 cursor-pointer"
                    />
                  </div>
                  <div className="mt-2 mb-3 border-b border-panelBorder" />
                  <div className="w-full mt-4">
                    <RadioGroup
                      row
                      value={groupType}
                      onChange={(e) => {
                        setGroupType(e.target.value);
                      }}
                    >
                      <FormControlLabel
                        value="Master"
                        disabled={edit}
                        control={
                          <Radio
                            sx={{
                              "& .MuiSvgIcon-root": {
                                fontSize: "16px",
                              },
                              "&.Mui-checked .MuiSvgIcon-root": {
                                color: edit ? "grey" : "black",
                              },
                            }}
                          />
                        }
                        label={
                          <span className="text-headingColor text-sm">
                            Master Group
                          </span>
                        }
                      />
                      <FormControlLabel
                        value="Member"
                        disabled={edit}
                        control={
                          <Radio
                            sx={{
                              "& .MuiSvgIcon-root": {
                                fontSize: "16px",
                              },
                              "&.Mui-checked .MuiSvgIcon-root": {
                                color: edit ? "grey" : "black",
                              },
                            }}
                          />
                        }
                        label={
                          <span className="text-headingColor text-sm">
                            Member Group
                          </span>
                        }
                      />
                    </RadioGroup>
                  </div>
                  <div className="w-full mt-4">
                    <InputLabel label={"Group Name"} isMandatory={true} />
                    <TextFieldWrapper
                      name="groupName"
                      placeholder={"Enter group name"}
                    />
                  </div>

                  {(edit && selectedDetails?.isSubGroup === false) ||
                  (groupType === "Master" && !edit) ? (
                    <>
                      <div className="w-full mt-4">
                        <div className="flex">
                          <InputLabel label={"Add Email IDs"} />
                          <InputLabel
                            label={"(Press enter to add email id)"}
                            labelClassName={"text-xs ml-1 mt-0.5"}
                          />
                        </div>
                      </div>{" "}
                      <Field
                        id="chips"
                        name="chips"
                        render={(fieldProps) => (
                          <ChipInput
                            chips={fieldProps.field.value}
                            onChipAdd={(chip) =>
                              handleChipAdd(chip, fieldProps.form)
                            }
                            onChipRemove={(index) =>
                              handleChipRemove(index, fieldProps.form)
                            }
                            isEdit={true}
                          />
                        )}
                      />
                      {groupType === "Master" ? (
                        <p
                          style={{ fontSize: "12px", color: "#D32F2F" }}
                          valid={false}
                        >
                          <ErrorMessage name="chips" />
                        </p>
                      ) : null}
                      <div className="w-full mt-4">
                        <InputLabel label={"Add group"} isMandatory={false} />
                        <Select
                          name="existingGroups"
                          placeholder={"Add Groups"}
                          options={groups}
                          onChange={(selectedOptions) => {
                            const selectedValues = selectedOptions?.map(
                              (option) => option.value
                            );
                            setFieldValue("existingGroups", selectedValues);
                          }}
                          isMulti
                          isSearchable={true}
                        />
                      </div>
                    </>
                  ) : (edit && selectedDetails?.isSubGroup === true) ||
                    (groupType === "Member" && !edit) ? (
                    <>
                      <div className="w-full mt-4">
                        <div className="flex">
                          <InputLabel label={"Add Email IDs"} />
                          <InputLabel
                            label={"(Press enter to add email id)"}
                            labelClassName={"text-xs ml-1 mt-0.5"}
                            isMandatory={true}
                          />
                        </div>
                      </div>
                      <Field
                        id="chips1"
                        name="chipData"
                        render={(fieldProps) => (
                          <ChipInput
                            chips={fieldProps.field.value}
                            onChipAdd={(chip) =>
                              handleChipDataAdd(chip, fieldProps.form)
                            }
                            onChipRemove={(index) =>
                              handleChipDataRemove(index, fieldProps.form)
                            }
                            isEdit={true}
                          />
                        )}
                      />
                      {groupType === "Member" ? (
                        <p
                          style={{ fontSize: "12px", color: "#D32F2F" }}
                          valid={false}
                        >
                          <ErrorMessage name="chipData" />
                        </p>
                      ) : null}
                    </>
                  ) : null}
                  <div className="text-center mt-10 gap-5 mb-2 mr-4">
                    <CancelButton
                      onClick={() => {
                        closeGroupDialog();
                        resetForm();
                        setGroupType(
                          edit && selectedDetails?.isSubGroup
                            ? "Member"
                            : "Master"
                        );
                      }}
                      label={"Cancel"}
                      buttonClassName="w-[100px] h-9 text-xs"
                    />
                    <Button
                      type="submit"
                      label={"Save"}
                      buttonClassName="w-[100px] h-9 text-xs ml-5"
                      loading={isLoading}
                    />
                  </div>
                </div>
              </Form>
            </Dialog>
            <SuccessDialog
              show={successDialog}
              onHide={() => {
                setSuccessDialog(false);
                resetForm();
              }}
              message={message}
            />
            <ErrorDialog
              show={errorDialog}
              onHide={() => {
                setErrorDialog(false);
                resetForm();
              }}
              message={message}
            />
          </>
        )}
      </Formik>
    </Box>
  );
}
