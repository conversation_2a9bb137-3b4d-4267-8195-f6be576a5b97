//import { t } from "i18next";

export const convertToDateObject = (dateString) => {
  let date, month, year;
  if (dateString) {
    let newFormat = dateString.split("-");
    if (newFormat.length === 2) {
      month = newFormat[0];
      year = newFormat[1];
    } else if (newFormat.length === 3) {
      date = newFormat[0];
      month = newFormat[1];
      year = newFormat[2];
    }
    return new Date(year, month - 1);
  }
};

/////////////////////Styles///////////////////////
export const templates = {
  candidate: "Candidate",
  recruiter: "Recruiter",
  platformAdmin: "Platform Admin",
};
////////////////////////////////////////////////////

export const commonRegex = /^[^<>+$;'"\\/~|:]+$/;
export const specialRegex = /^[^<>$\\~|]+$/;
export const passwordRegex = /[!@^*+()_=\\,.?\\-]+/;
export const passwordRestrictedRegex = /^[^<>&;$#%"'`/:|[\]{}]+$/;
export const onlySpaceRegex = /(?=.*[a-z])|(?=.*[A-Z])/;
export const alphanumericRegex = /^(?=.*[a-zA-Z])(?=.*\d).+$/;
export const linkedinRegex =
  /https:\/\/(?:[a-z]+\.)?linkedin\.com\/[a-zA-Z0-9-]+/;
export const websiteRegex =
  /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/;

export const alphaAndSpaceRegex = /^[A-Za-z][A-Za-z ]*$/;
export const onlyNumericsRegex = /^\d+$/; // only numbers are allowed

export const onlyNumSpaceNotAllowed = /^(?=.*[a-zA-Z]).*$/; //Only space,only special char,only numerics not allowed.Alphabets with space,numbers and special chars allowed.

//export const websiteRegex = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/;
export const passwordAllowedCharacters = " !@^*+()_=\\,.?\\-";
export const onlyZerosNotAllowedRegex = /^(?!0+$)[0-9]*$/;
export const onlyZerosRegex = /^(?!0+$)[0-9A-Za-z]/;
export const invalidPINRegex =
  /^(?![-\s])(?![A-Za-z]+$)(?=.*[0-9])[0-9A-Za-z\s-]*$/;

export const panelOptions = [
  { value: "line", label: "Line" },
  { value: "bar", label: "Bar" },
  { value: "table", label: "Table" },
  { value: "multi", label: "Multi-axis" },
  { value: "pie", label: "Pie" },
];

export const recordsPanel = [];
for (let i = 5; i <= 50; i += 5) {
  recordsPanel.push({ value: i.toString(), label: i.toString() });
}

export const logicalOperator = [
  { value: "and", label: "AND" },
  // { value: "or", label: "OR" },
  { value: "no", label: "NO" },
];

export const alertTypeOptions = [
  { label: "Delivery Drop", value: "Delivery Drop" },
  { label: "Error", value: "Error" },
  { label: "Volume Drop Or Spike", value: "Volume Drop or Spike" },
  { label: "Delivery Report Drop", value: "Delivery Report Drop" },
];

export const filterOptions = [
  { label: "Supplier", value: "Supplier" },
  { label: "Customer", value: "Customer" },
  { label: "Destination", value: "Destination" },
  { label: "Supplier-Destination", value: "Supplier-Destination" },
  { label: "Customer-Destination", value: "Customer-Destination" },
  {
    label: "Customer-Supplier-Destination",
    value: "Customer-Supplier-Destination",
  },
];

export const vloumeDropFilterOptions = [
  { label: "Customer", value: "Customer" },
  { label: "Destination", value: "Destination" },

  { label: "Customer-Destination", value: "Customer-Destination" },
];

export const alertCategoryOptions = [
  { label: "Major", value: "Major" },
  { label: "Minor", value: "Minor" },

  { label: "Critical", value: "Critical" },
];

export const deliveryReportDropFilterOptions = [
  { label: "Customer", value: "Customer" },

  { label: "Customer-Destination", value: "Customer-Destination" },
  { label: "Supplier", value: "Supplier" },
  { label: "Supplier-Destination", value: "Supplier-Destination" },
];

export const timePeriodOptions = [
  { label: "Yesterday", value: 1 },
  { label: "Last three days", value: 3 },
  { label: "Last five days", value: 5 },
  { label: "Last weekdays", value: 7 },
];

export const volumeTypeOptions = [
  { label: "Spike", value: "spike" },
  { label: "Drop", value: "drop" },
  { label: "Both", value: "both" },
];

export const timeIntervalOptions = [
  { label: "15 min", value: 15 },
  { label: "30 min", value: 30 },
  { label: "45 min", value: 45 },
  { label: "1 hour", value: 60 },
];

export const timeAcessOptions = {
  minute: ["Last Hour", "Calendar"],
  hour: [
    "Last Hour",
    "Today",
    "Yesterday",
    "Last 6 Hours",
    "Last 12 Hours",
    "Last 24 Hours",
    "Calendar",
  ],
  day: [
    "Last Seven Days",
    "Last Week",
    "Today",
    "Calendar",
    "Yesterday",
    "This Year",
    "Last Year",
  ],
  week: ["Last Seven Days", "Last Week", "Calendar", "This Year", "Last Year"],
  month: [
    "Last 30 Days",
    "This Month",
    "Last Month",
    "Calendar",
    "This Year",
    "Last Year",
  ],
};

export const defaultTimeRange = [
  "Last Hour",
  "Today",
  "Yesterday",
  "Last 6 Hours",
  "Last Seven Days",
  "Last 12 Hours",
  "Last Week",
  "Last 24 Hours",
  "Last 30 Days",
  "This Month",
  "Last Month",
  "Calendar",
];

export const protocolTypeOptions = [
  { label: "Select All", value: "Select All" },
  { label: "A2P", value: "A2P" },
  { label: "P2P", value: "P2P" },
];

export const interfaceTypeOptions = [
  { label: "Select All", value: "Select All" },
  { label: "SMPP", value: "SMPP" },
  { label: "SS7", value: "SS7" },
];

export const ConditionDisplay = ({ conditions }) => {
  return (
    <>
      {conditions?.map((conditionGroup, index) => {
        let conditionString;

        if ("type1" in conditionGroup) {
          conditionString = Object.entries(conditionGroup)
            .map(([key, value]) => {
              if (key.startsWith("type")) {
                return value;
              } else {
                return `{${value}}`;
              }
            })
            .filter(Boolean)
            .join(" ");
        } else {
          const {
            field,
            condition: conditionType,
            value,
            operator,
          } = conditionGroup;

          conditionString = [
            field,
            conditionType,
            value,
            operator && `{${operator}}`,
          ]
            .filter(Boolean)
            .join(" ");
        }

        if (conditionGroup.type4) {
          conditionString = conditionString.replace(
            conditionGroup.type4,
            `{${conditionGroup.type4}}`
          );
        }

        if (index === conditions.length - 1 && conditionGroup.type4) {
          conditionString = conditionString.replace(
            `{${conditionGroup.type4}}`,
            ""
          );
        }
        if (index === conditions.length - 1 && conditionGroup.operator) {
          conditionString = conditionString.replace(
            `{${conditionGroup.operator}}`,
            ""
          );
        }

        const isEmpty = Object.values(conditionGroup).every(
          (value) => value === ""
        );

        return (
          <div key={index} className="mb-1">
            <span className={`rounded-md p-1 text-xs ml-1`}>
              {/* {console.log("formData.conditions", conditions)} */}
              {isEmpty ? "No Filters added" : conditionString}
            </span>
          </div>
        );
      })}
    </>
  );
};
export const default_file_type = "CSV";
export const customerInterface = [
  { value: "SMPP", label: "SMPP" },
  { value: "SS7", label: "SS7" },
  { value: "SMPP_ES", label: "SMPP_ES" },
];
export const customerProtocol = [
  { value: "A2P", label: "A2P" },
  { value: "P2P", label: "P2P" },
];
export const destinationMsg = [
  { value: "AT", label: "AT" },
  { value: "MT", label: "MT" },
  { value: "IN", label: "IN" },
  { value: "CF", label: "CF" },
];
export const trafficType = [
  { value: "A2P", label: "A2P" },
  { value: "P2P", label: "P2P" },
];
export const supplierProtocol = [
  { value: "A2P", label: "A2P" },
  { value: "P2P", label: "P2P" },
  { value: "NA", label: "NA" },
];

export const transactionType = [
  { value: "Select All", label: "Select All" },
  { label: "SMPP to SMPP", value: "AO-AT, DR-AT" },
  { label: "SMPP to SS7", value: "AO-MT, DR-MT" },
  { label: "SS7 to SS7", value: "MT-IN, MT-CF, RI-IN, RI-CF" },
];

export const dynamicReports = {
  Customer: "Customer Wise Revenue Report",
  Supplier: "Supplier Wise Cost Report",
  Traffic: "WhatsApp Traffic Report",
  Facebook: "Facebook Traffic Report",
  SlabBasedCust: "Slab Based Billing Report (Customer)",
  SlabBasedSupp: "Slab Based Billing Report (Supplier)",
  SlabBasedCustMultiple: "Slab Based Billing Report (Customer) Multiple",
  SlabBasedSuppMultiple: "Slab Based Billing Report (Supplier) Multiple",
  SupplierMultiple: "Supplier Wise Cost Report Multiple",
  CustomerMultiple: "Customer Wise Revenue Report Multiple",
};

export const defaultTimeZone = [
  { label: "GMT+05:30 - Asia - Kolkata", value: "Asia/Kolkata" },
];

export const inspect_mode_enable =
  process.env.REACT_APP_INSPECT_MODE_ENABLE === "false";

export const CDRSearch_conditional_filter = 2;

export const filterLabelMap = {
  customer_name: "Customer Name",
  customer_bind: "Customer Bind",
  src_prime: "Source Prime",
  destination: "Destination",
  dest_prime: "Destination Prime",
  supplier: "Supplier Name",
  supplier_name: "Supplier Name",
  supplier_bind: "Supplier Bind",
  destination_operator_name: "Destination Operator Name",
  destination_country_name: "Destination Country Name",
  customer_interface_type: "Customer Interface Type",
  supplier_interface_type: "Supplier Interface Type",
  customer_billing_logic: "Customer Billing Logic",
  supplier_billing_logic: "Supplier Billing Logic",
  customer_traffic_type: "Customer Traffic Type",
  supplier_traffic_type: "Supplier Traffic Type",
  destination_mcc_final: "Destination MCC",
  destination_mnc_final: "Destination MNC",
  lcr_name: "LCR Name",
  spec_lcr: "Spec LCR",
  status: "Status",
  customer_kam: "Customer KAM",
  supplier_kam: "Supplier KAM",
  roamingDirectStatus: "Roaming and Direct Status",
  bilateral: "Bilateral",
  negative_report: "Negative Report",
};

// UserAnalysis constants
export const USER_ANALYSIS_COLORS = [
  "#EDDF82",
  "#82C3ED",
  "#82EDAD",
  "#ED8282",
];
export const USER_ANALYSIS_DEFAULT_DIMENSION = { w: 6, h: 4 };

export const USER_ANALYSIS_VISUALIZATION_TYPES = {
  BAR_GRAPH: "Bar Graph",
  LINE_GRAPH: "Line Graph",
  MULTIAXIS_GRAPH: "MultiAxis Graph",
  TABLE_REPORT: "Table Report",
};

export const USER_ANALYSIS_TIME_RANGES = {
  LAST_HOUR: "Last Hour",
  LAST_6_HOURS: "Last 6 Hours",
  LAST_12_HOURS: "Last 12 Hours",
  LAST_24_HOURS: "Last 24 Hours",
  TODAY: "Today",
  YESTERDAY: "Yesterday",
  LAST_SEVEN_DAYS: "Last Seven Days",
  LAST_WEEK: "Last Week",
  LAST_30_DAYS: "Last 30 Days",
  LAST_MONTH: "Last Month",
  THIS_MONTH: "This Month",
};

export const USER_ANALYSIS_HOUR_MAPPINGS = {
  [USER_ANALYSIS_TIME_RANGES.LAST_HOUR]: 1,
  [USER_ANALYSIS_TIME_RANGES.LAST_6_HOURS]: 6,
  [USER_ANALYSIS_TIME_RANGES.LAST_12_HOURS]: 12,
  [USER_ANALYSIS_TIME_RANGES.LAST_24_HOURS]: 24,
};

export const FILTER_PERIOD_REPORT = {
  reportName: "SMS Firewall Report",
};

export const REPORT_INFO_TOOLTIP = [
  `During calendar selection, to view reports spanning more than an hour, a day, a week, or a month, make sure to set the start time to 00:00.`,
  `In case of selection of last 6, 12, 24 hours; reports will be generated on an hourly basis with the start time of the selected hour.`,
  `For example, if the current user time range is from 13:01 to 13:59, then the report will be generated from 13:00 hour onwards.`,
];

export const views = [
  { label: "Table View", value: "table" },
  { label: "Graph View", value: "graph" },
];
export const smsFirewallViews = [
  { label: "Day wise", value: "Day" },
  { label: "Month wise", value: "Month" },
];

export const BASIC_COLORS = [
  { label: "Jet Black", value: "#343434" },
  { label: "Raisin Black", value: "#242424" },
  { label: "Taupe", value: "#483C49" },
  { label: "Dark Brown", value: "#654321" },
  { label: "Bistre", value: "#3D2B1F" },
  { label: "Smoky Gray", value: "#737373" },
  { label: "Gunmetal", value: "#2A3439" },
  { label: "Onyx", value: "#353839" },
  { label: "Charcoal Gray", value: "#6D6E71" },
  { label: "Dim Gray", value: "#696969" },
  { label: "Slate Gray", value: "#708090" },
  { label: "Teal", value: "#008080" },
  { label: "Pine Green", value: "#01796F" },
  { label: "Dark Cyan", value: "#008B8B" },
  { label: "Forest Green", value: "#228B22" },
  { label: "Dark Green", value: "#006400" },
  { label: "Olive", value: "#808000" },
  { label: "Dark Khaki", value: "#BDB76B" },
  { label: "Dark Goldenrod", value: "#B8860B" },
  { label: "Tuscan Sun", value: "#665005" },
  { label: "Dark Magenta", value: "#8B008B" },
  { label: "Indigo", value: "#4B0082" },
  { label: "Dark Violet", value: "#9400D3" },
  { label: "Plum", value: "#8E4585" },
  { label: "Crimson", value: "#DC143C" },
  { label: "Firebrick", value: "#B22222" },
  { label: "Dark Salmon", value: "#E9967A" },
  { label: "Sienna", value: "#A0522D" },
  { label: "Chocolate", value: "#D2691E" },
  { label: "Mahogany", value: "#C04000" },
  { label: "Dark Orange", value: "#FF8C00" },
  { label: "Ochre", value: "#CC7722" },
  { label: "Dark Turquoise", value: "#00CED1" },
  { label: "Cadet Blue", value: "#5F9EA0" },
  { label: "Cornflower Blue", value: "#6495ED" },
];
export const baseColors = [
  "#DC3545", // Red
  "#28A745", // Green
  "#6F42C1", // Purple
  "#007BFF", // Blue
  "#FD7E14", // Orange
  "#20C997", // Teal
  "#E83E8C", // Pink
  "#6C757D", // Gray
];

export const BASE_COLORS_PIE = [
  "#331978",
  "#EDDF82",
  "#5EC697",
  "#643276",
  "#DC33C0",
  "#AB76B7",
  "#DC9033",
  "#6DCBD2",
  "#83B776",
  "#3800D3",
  "#DC3833",
  "#F28F8F",
];

export const NO_DATA_MESSAGE = "No data to display";
