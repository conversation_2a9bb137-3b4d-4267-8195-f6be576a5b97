import React, { useState } from "react";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ArrowForwardIosSharpIcon from "@mui/icons-material/ArrowForwardIosSharp";

function AccordionGroup({ accordions }) {
  const [expandedAccordion, setExpandedAccordion] = useState(null);

  const handleAccordionChange = (accordionId) => {
    setExpandedAccordion((prevState) =>
      prevState === accordionId ? null : accordionId
    );
  };

  return (
    <>
      {accordions.map((accordion) => (
        <div key={accordion.id}>
          <Accordion
            expanded={expandedAccordion === accordion.id}
            onChange={() => handleAccordionChange(accordion.id)}
            sx={{
              boxShadow: "none",
              width: "200px",
              flexDirection: "row-reverse",
            }}
          >
            <AccordionSummary
              expandIcon={
                <ArrowForwardIosSharpIcon sx={{ fontSize: "0.7rem" }} />
              }
              sx={{
                flexDirection: "row-reverse",
                justifyContent: "flex-start",
                "& .MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
                  transform: "rotate(90deg)",
                },
                marginLeft: "-16px",
              }}
            >
              <div className="text-headingColor text-sm font-medium ml-3 flex">
                {accordion.title}{" "}
                <div className="p-1 ml-2 w-6 h-6 rounded-full bg-bgCheckboxSelection text-[white] text-xs flex items-center justify-center">
                  {accordion.count}
                </div>
              </div>
            </AccordionSummary>
            <AccordionDetails sx={{ padding: 0 }}>
              {accordion.content}
            </AccordionDetails>
          </Accordion>
        </div>
      ))}
    </>
  );
}

export default AccordionGroup;
