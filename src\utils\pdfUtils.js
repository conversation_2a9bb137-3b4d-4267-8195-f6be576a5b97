/**
 * PDF generation utilities for chart exports
 */

/**
 * Generates PDF from HTML element using html2canvas and jsPDF
 * @param {HTMLElement} targetElement - Element to convert to PDF
 * @param {string} filename - Output filename
 * @param {string} title - PDF title
 * @param {Function} onSuccess - Success callback
 * @param {Function} onError - Error callback
 */
export const generatePDFFromElement = async (
  targetElement,
  filename,
  title,
  onSuccess,
  onError
) => {
  try {
    const html2canvas = await import("html2canvas");
    const { jsPDF } = await import("jspdf");

    const canvas = await html2canvas.default(targetElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      width: targetElement.scrollWidth,
      height: targetElement.scrollHeight,
      scrollX: 0,
      scrollY: 0,
    });

    const imgData = canvas.toDataURL("image/png");

    // Calculate dimensions
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;

    // Create PDF in landscape mode for better chart visibility
    const pdf = new jsPDF({
      orientation: imgWidth > imgHeight ? "landscape" : "portrait",
      unit: "mm",
      format: "a4",
    });

    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    // Add some padding
    const padding = 10;
    const availableWidth = pdfWidth - 2 * padding;
    const availableHeight = pdfHeight - 2 * padding;

    // Calculate scale to fit the image within available space
    const scaleX = availableWidth / (imgWidth / 2); // Divide by 2 because scale was 2
    const scaleY = availableHeight / (imgHeight / 2);
    const scale = Math.min(scaleX, scaleY);

    // Calculate final dimensions
    const finalWidth = (imgWidth / 2) * scale;
    const finalHeight = (imgHeight / 2) * scale;

    // Center the image
    const x = (pdfWidth - finalWidth) / 2;
    const y = (pdfHeight - finalHeight) / 2;

    // Add title if needed
    pdf.setFontSize(16);
    pdf.text(title || "Report", pdfWidth / 2, 15, {
      align: "center",
    });

    // Add the image
    pdf.addImage(
      imgData,
      "PNG",
      x,
      Math.max(y, 25), // Ensure it's below the title
      finalWidth,
      finalHeight
    );

    // Add footer with date range
    pdf.setFontSize(8);

    pdf.save(filename);

    if (onSuccess) onSuccess();
  } catch (error) {
    console.error("Error generating PDF:", error);
    if (onError) onError(error);
  }
};
