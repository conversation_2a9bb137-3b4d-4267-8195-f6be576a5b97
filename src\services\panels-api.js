import axios from "axios";
//import { config } from "../assets/config/config";
import getAPIMap from "../routes/ApiUrls";

//const apiUrl = config.api.url;

export async function getAll(options) {
  let url = getAPIMap("panels");
  if (options.queryKey[1] !== "" && options.queryKey[1] !== undefined) {
    url += `?page=${options.queryKey[1]}`;
  }
  if (options.queryKey[2] !== "" && options.queryKey[2] !== undefined) {
    url += `&limit=${options.queryKey[2]}`;
  }
  if (options.queryKey[3] !== "" && options.queryKey[3] !== undefined) {
    url += `&search=${options.queryKey[3]}`;
  }
  if (options.queryKey[4] !== "" && options.queryKey[4] !== undefined) {
    url += `?createdBy=${options.queryKey[4]}`;
  }
  let response = axios.get(url);
  return response;
}
export async function getPanels(options) {
  let url =
    getAPIMap("panels") +
    `?page=${options.queryKey[1]}&limit=${options.queryKey[2]}`;
  if (options.queryKey[3] !== "" && options.queryKey[3] !== undefined) {
    url += `&search=${options.queryKey[3]}`;
  }
  if (options.queryKey[4] !== "" && options.queryKey[4] !== undefined) {
    url += `&type=${options.queryKey[4]}`;
  }
  let response = axios.get(url);
  return response;
}
export async function deletePanel(options) {
  let url = getAPIMap("panels") + `/${options.value}`;
  let response = axios.delete(url);
  return response;
}
export async function previewPanel(options) {
  let url = getAPIMap("panelPreview");
  let response = axios.post(url, options.reqData);
  return response;
}
export async function downloadPanel(options) {
  let url = getAPIMap("panelPreview");
  let response = axios.post(url, options.reqData, {
    responseType: "blob",
  });
  // console.log("url", url);
  return response;
}
export async function createPanel(options) {
  let url = getAPIMap("panels");
  //console.log(" Option", options);
  let response = axios.post(url, options.reqData);
  // console.log("url", url);
  return response;
}
export async function getId(options) {
  let url = getAPIMap("panels") + `/${options.queryKey[1]}`;
  let response = axios.get(url);

  return response;
}
export async function updatePanel(options) {
  let url = getAPIMap("panels") + `/${options.value}`;
  let response = axios.put(url, options.reqData);

  return response;
}
