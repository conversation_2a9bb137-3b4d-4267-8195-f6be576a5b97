import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import { CheckMarkIcon, CloseIcon } from "../icons";
import Button from "../components/Button/OutlinedButton";

function SuccessDialog({ show, onHide, message, btnName }) {
  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 411,
          maxHeight: 435,
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onHide}
      className="p-6 font-sans"
    >
      <DialogTitle className="mt-2 flex justify-end">
        <CloseIcon className="w-2.5 h-2.5 cursor-pointer" onClick={onHide} />
      </DialogTitle>

      <div className="flex justify-center items-center mt-2">
        <CheckMarkIcon className="w-10 h-10" />
      </div>
      <div className="text-base font-normal mt-4 flex justify-center items-center mx-7">
        {message}
      </div>
      <div>
        <div className="text-center mt-10 gap-5 mb-4">
          <Button
            onClick={onHide}
            type="submit"
            label={btnName ? btnName : "Okay"}
            buttonClassName="w-[100px] h-9 text-xs"
          />
        </div>
      </div>
    </Dialog>
  );
}

export default SuccessDialog;
