import React, { useContext } from "react";
import BarChartComponent from "../charts/BarCharts";
import { PreviewContext } from "../../context/PreviewContext";

export default function Performance({ isEdit, value }) {
  const { panelFormStepThreeResponse } = useContext(PreviewContext);
  let content;

  if (
    !panelFormStepThreeResponse ||
    !panelFormStepThreeResponse.data ||
    !panelFormStepThreeResponse.data.data ||
    !panelFormStepThreeResponse.data.data.x_axis ||
    panelFormStepThreeResponse.data.data.x_axis.length === 0 ||
    !panelFormStepThreeResponse.data.data.y_axis ||
    panelFormStepThreeResponse.data.data.y_axis.length === 0
  ) {
    content = (
      <>
        <div className=" w-full font-bold text-xl flex justify-center items-center p-3">
          No records to display
        </div>
      </>
    );
  } else {
    content = (
      <>
        <BarChartComponent chartData={panelFormStepThreeResponse.data.data} />
      </>
    );
  }

  return <>{content}</>;
}
