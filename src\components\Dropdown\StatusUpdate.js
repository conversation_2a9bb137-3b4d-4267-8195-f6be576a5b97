// import { Formik, Form } from "formik";
// import React from "react";
// import Select from "../FormsUI/RadioSelect";

// function StatusUpdate({ statusOptions, value }) {
//   const handleUpdate=()=>{

//   }
//   return (
//     <div>
//       <Formik
//         initialValues={{
//           statusDetails: value || "",
//         }}
//       >
//         {({ values, errors, status }) => (
//           <Form>
//             <div className="flex items-center">
//               <div className="w-[150px]">
//                 <Select
//                   name="statusDetails"
//                   options={statusOptions || []}
//                   handleUpdate={handleUpdate}
//                   onChange={(selectedOption) => {
//                     console.log("selectedOption", selectedOption);
//                     // setGlobalSearchSelect(selectedOption.value);
//                   }}
//                 />
//               </div>
//             </div>
//           </Form>
//         )}
//       </Formik>
//     </div>
//   );
// }

// export default StatusUpdate;
import { Formik, Form } from "formik";
import React, { useState } from "react";
import Select from "../FormsUI/RadioSelect";
import { useMutation, useQueryClient } from "react-query";
import axios from "axios";
import { alertServices } from "../../services/alert-api";
import SuccessDialog from "../../popups/SuccessDialog";
import ErrorDialog from "../../popups/ErrorDialog";

function StatusUpdate({ statusOptions, value, id }) {
  const queryClient = useQueryClient();

  const { mutate: handleStatusUpdateAPI, isLoading: isUpdateLoading } =
    useMutation(alertServices.handleStatusUpdate);

  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");

  const handleUpdate = (updatedStatus) => {
    handleStatusUpdateAPI(
      { status: updatedStatus, id },
      {
        onSuccess: (data) => {
          setSuccessDialog(true);
          setMessage("Status Updated Successfully ");
          queryClient.invalidateQueries("/alertNotify").then(() => {
            console.log(
              "StatusUpdate: invalidateQueries /alertNotify completed"
            );
          });
        },
        onError: (error) => {
          setErrorDialog(true);
          setMessage(error);
        },
      }
    );
  };

  return (
    <div>
      <Formik
        initialValues={{
          statusDetails: value || "",
        }}
      >
        {({ setFieldValue }) => (
          <Form>
            <div className="flex items-center">
              <div className="w-[150px]">
                <Select
                  name="statusDetails"
                  options={statusOptions || []}
                  handleUpdate={(value) => handleUpdate(value)}
                  onChange={(selectedOption) => {
                    setFieldValue("statusDetails", selectedOption?.value || "");
                  }}
                  menuListzIndex={9999}
                />
              </div>
            </div>
          </Form>
        )}
      </Formik>
      {successDialog && (
        <SuccessDialog
          show={successDialog}
          message={message}
          onHide={() => setSuccessDialog(false)}
        />
      )}
      {errorDialog && (
        <ErrorDialog
          show={errorDialog}
          message={message}
          onHide={() => setErrorDialog(false)}
        />
      )}
    </div>
  );
}

export default StatusUpdate;
