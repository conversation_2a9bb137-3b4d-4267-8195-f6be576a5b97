import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import ProtectedRoute from "./components/Routes/ProtectedRoute";
import ForwardRoute from "./components/Routes/ForwardRoute";
import "react-simple-toasts/dist/theme/dark.css";
import { inspect_mode_enable } from "./common/constants";
import TaskManager from "./components/Routes/TaskManager";
//import { config } from "./assets/config/config";

const baseName = process.env.REACT_APP_BASE_NAME;

function App() {
  const { i18n } = useTranslation();
  const val = i18n.resolvedLanguage;

  useEffect(() => {
    if (val === "ar") {
      document.body.dir = "rtl";
    } else {
      document.body.dir = "ltr";
    }

    if (inspect_mode_enable) {
      const handleRightClick = (event) => {
        // if (config.isDevelopment) {
        //   // To enable the right click in development mode
        // } else {
        // To prevent right click in production mode
        event.preventDefault();
        // }
      };

      const handleKeyDown = (event) => {
        if (event.key === "F12" || (event.key === "I" && event.ctrlKey)) {
          event.preventDefault();
        }
      };

      document.addEventListener("contextmenu", handleRightClick);
      document.addEventListener("keydown", handleKeyDown);

      return () => {
        document.removeEventListener("contextmenu", handleRightClick);
        document.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [val, i18n]);

  return (
    <>
      <TaskManager>
        <Router basename={baseName}>
          <Routes>
            <Route path="/auth/*" element={<ForwardRoute />} />
            <Route path="/app/*" element={<ProtectedRoute />} />
            <Route path="*" element={<Navigate to="/auth" />} />
          </Routes>
        </Router>
      </TaskManager>
    </>
  );
}

export default App;
