import axios from "axios";
//import { config } from "../assets/config/config";
import getAPIMap from "../routes/ApiUrls";
//const apiUrl = config.api.url;

// async function getLogDetails(
//   page,
//   resultsPerPage,
//   searchStr,
//   selectedRole,
//   selectedEvent,
//   download,
//   type,
//   startDate,
//   endDate
// ) {
//   let url = `${apiUrl}/v1/users/logs?limit=${resultsPerPage}&page=${page}&search=${
//     searchStr ? searchStr : ""
//   }&role=${selectedRole ? selectedRole : ""}&event=${
//     selectedEvent ? selectedEvent : ""
//   }&startDate=${startDate ? startDate : ""}&endDate=${
//     endDate ? endDate : ""
//   }&download=${download === true ? download : ""}&type=${type ? type : ""}`;
//   if (download) return axios.get(url, { responseType: "blob" });
//   return axios.get(url);
// }

export async function getLogDetails(options) {
  let url =
    getAPIMap("logs") +
    `?limit=${options.queryKey[2]}&page=${options.queryKey[1]}&search=${
      options.queryKey[3] ? options.queryKey[3] : ""
    }&role=${options.queryKey[5] ? options.queryKey[5] : ""}&event=${
      options.queryKey[4] ? options.queryKey[4] : ""
    }&startDate=${options.queryKey[6] ? options.queryKey[6] : ""}&endDate=${
      options.queryKey[7] ? options.queryKey[7] : ""
    }&download=${
      options.queryKey[8] === true ? options.queryKey[8] : ""
    }&type=${options.queryKey[9] ? options.queryKey[9] : ""}`;

  if (options.queryKey[8] === true) {
    return axios.get(url, { responseType: "blob" });
  } else {
    return axios.get(url);
  }
}

async function getEventDetails() {
  return axios.get(getAPIMap("eventList"));
}

async function getRoleDetails() {
  return axios.get(getAPIMap("rolesList"));
}

export const LogService = {
  getLogDetails,
  getEventDetails,
  getRoleDetails,
};
