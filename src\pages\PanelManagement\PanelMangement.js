import React, { useMemo, useState, useContext } from "react";
import { Formik, Form } from "formik";
import Table from "../../components/table/ReportTable";
import Select from "../../components/FormsUI/Select";
import InputLabel from "../../components/FormsUI/InputLabel/InputLabel";
import { useNavigate } from "react-router-dom";
import { useQuery, useMutation } from "react-query";
import { getPanels, deletePanel } from "../../services/panels-api";
import { EditingIcon, TrashIcon, SearchIcon } from "../../icons";
import Pagination from "../../components/Pagination/Pagination";
import DeleteDialog from "../../popups/DeleteDialog";
import SuccessDialog from "../../popups/SuccessDialog";
import ErrorDialog from "../../popups/ErrorDialog";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import { AuthContext } from "../../context/AuthContext";
import InfoModal from "../../components/modals/InfoModal";
import { getVisualizationType } from "../../services/dropdown-api";
import { DataContext } from "../../context/DataContext";
import ResultPerPageComponent from "../../components/Pagination/ResultsPerPage";
import EditButton from "../../components/Button/EditButton";
import Button from "../../components/Button/OutlinedButton";
import Title from "../../Title";
import bgImage from "../../assets/img/Records.png";
import theme from "../../tailwind-theme";
import { formatDateTime } from "../../common/commonFunctions";

function PanelMangement() {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [value, setValue] = useState("");
  const [visualType, setVisualType] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [isEdit, setIsEdit] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [type, setType] = useState("");
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const { roles } = useContext(AuthContext);
  const { resultPerPage } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(10);

  const { setCurrentStep, setFormData, setStepCount } =
    useContext(multiStepFormContext);

  const {
    data: panelList,
    isLoading,
    isFetching,
    refetch,
  } = useQuery(
    ["panelList", currentPage, limitPerPage, searchInput, type],
    getPanels,
    {
      onSuccess: (resp) => {
        //console.log(resp);
        let newData = resp?.data?.data?.map((x) => {
          return {
            //createdAt: formatDateTime(x.createdAt),
            //dayjs(x.createdAt).format("DD/MM/YYYY"),
            createdAt: x.createdAt,
            createdBy: x.createdBy,
            dataColumns: x.dataColumns,
            filters: x.filters,
            id: x.id,
            interval: x.interval,
            name: x.name,
            timePeriod: x.timePeriod,
            updatedAt: x.updatedAt,
            visualizationType: x.visualizationType,
          };
        });
        setTableData(newData);
      },
    }
  );

  const {} = useQuery(["/visualizationList"], getVisualizationType, {
    onSuccess: (res) => {
      const visualTypeList = res.data.map((type) => {
        return {
          value: type,
          label: type,
        };
      });

      const addOption = [
        {
          value: "",
          label: "All Panels",
        },
        ...visualTypeList,
      ];

      setVisualType(addOption);
    },
  });
  const permissions = roles?.resources?.filter(
    (res) => res.name === "Panel Management"
  )[0].permissions;
  //console.log("permissions", permissions);

  const { mutate: DeletePanel, isLoading: deleteLoading } =
    useMutation(deletePanel);

  function handlePageChange(page) {
    setCurrentPage(page);
  }

  const handleDelete = (id) => {
    // console.log(" Id", data);
    setValue(id);
    setDeleteDialog(true);
  };

  const handleEdit = (id) => {
    //console.log(" Id", id);
    setIsEdit(true);
    navigate("/app/panelmanagement/add", { state: { id, isEdit: true } });
    setCurrentStep(0);
    setStepCount(0);
    setFormData([]);
  };

  const columns = useMemo(
    () => [
      {
        header: "Panels",
        accessorKey: "name",
      },
      {
        header: "Creation Date",
        accessorKey: "createdAt",
      },
      {
        header: "Panel Type",
        accessorKey: "visualizationType",
      },
      {
        header: "",
        accessorKey: "action",
        enableSorting: false,
        enableColumnFilter: false,
        Cell: ({ row }) => (
          <>
            <div className="flex gap-8 mr-5">
              <div>
                <EditButton
                  label={"Edit"}
                  icon={EditingIcon}
                  onClick={() => {
                    if (permissions?.update === 0) {
                      setShowAlertConfirmation(true);
                      setMessage("Update permission not allowed");
                    } else {
                      handleEdit(row?.original?.id);
                    }
                  }}
                />
              </div>
              <div>
                <EditButton
                  label={"Delete"}
                  icon={TrashIcon}
                  onClick={() => {
                    if (permissions.delete === 0) {
                      setShowAlertConfirmation(true);
                      setMessage("Delete permission not allowed");
                    } else {
                      handleDelete(row.original.id);
                    }
                  }}
                />
              </div>
            </div>
          </>
        ),
      },
    ],
    []
  );

  const handleLimitChange = (e) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
  };

  return (
    <>
      <div className="bg-bgPrimary my-5">
        <div className="mt-10">
          <Title title={"List of Panels"} />
        </div>
        <div className="border border-listBorder bg-white p-3 mt-10">
          {!isLoading &&
          panelList?.data?.totalCount === 0 &&
          searchInput === "" &&
          type === "" ? (
            <>
              <div className="mx-3 mt-5">
                <div className="border border-outerBorder mb-5">
                  <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                    {"Oops ! no records to display."}
                  </div>
                  <div className="flex justify-center text-tabColor text-base font-semibold mt-5 ">{`You have not added any panels yet.`}</div>
                  <div className="flex justify-center my-12">
                    <img
                      src={bgImage}
                      style={{
                        height: "10%",
                        width: "10%",
                        objectFit: "cover",
                      }}
                      alt="bg"
                    />
                  </div>
                  <div className="flex justify-center mb-5">
                    <Button
                      buttonClassName="text-xs w-36 text-white h-10 "
                      label={"+Add New Panel"}
                      onClick={() => {
                        const canCreate =
                          roles.isSuperAdminRole || permissions.create === 1;
                        if (canCreate) {
                          navigate("/app/panelmanagement/add");
                          setCurrentStep(0);
                          setStepCount(0);
                          setFormData([]);
                        } else {
                          setShowAlertConfirmation(true);
                          setMessage("Create permission not allowed");
                        }
                      }}
                    ></Button>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="mx-3">
                <Formik initialValues={{ visualizationType: "" }}>
                  <Form>
                    <InputLabel label={"Panel Name"} />
                    <div className="flex gap-4">
                      <div className="">
                        <input
                          type="text"
                          style={{
                            border: `1px solid ${theme.borderColor.outerBorder}`,
                            paddingLeft: "2rem",
                          }}
                          className="min-w-[400px] px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                          placeholder="Search Panel"
                          value={searchInput}
                          onChange={(e) => {
                            setSearchInput(e.target.value);
                            setCurrentPage(1);
                          }}
                        />
                        <div
                          className="top-0 right-0  mr-4"
                          style={{
                            marginLeft: "0.25rem",
                            marginTop: "-1.7rem",
                          }}
                        >
                          <SearchIcon className="w-5 h-5" />
                        </div>
                      </div>
                      <div className="w-full md:w-[200px] ">
                        <Select
                          name="visualizationType"
                          options={visualType}
                          placeholder={"Select Panel type"}
                          onChange={(selectedOption) => {
                            const data = selectedOption.value;
                            //console.log("data123", data);
                            setType(data);
                            setCurrentPage(1);
                          }}
                          isSearchable={true}
                          menuListzIndex={9999}
                        />
                      </div>
                      <div className="flex-grow flex justify-end items-center gap-3">
                        <Button
                          buttonClassName="text-xs w-36 text-white h-10 "
                          label={"+Add New Panel"}
                          onClick={() => {
                            const canCreate =
                              roles.isSuperAdminRole ||
                              permissions.create === 1;
                            if (canCreate) {
                              navigate("/app/panelmanagement/add");
                              setCurrentStep(0);
                              setStepCount(0);
                              setFormData([]);
                            } else {
                              setShowAlertConfirmation(true);
                              setMessage("Create permission not allowed");
                            }
                          }}
                        ></Button>
                      </div>
                    </div>
                  </Form>
                </Formik>
                <div className="mt-7">
                  <Table
                    columns={columns}
                    data={Array.isArray(tableData) ? tableData : []}
                    isLoading={isFetching}
                  />
                </div>

                {panelList?.data && panelList.data.totalCount !== undefined && (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      // justifyContent: "flex-start",
                      marginTop: "20px",
                    }}
                  >
                    <div className="flex ">
                      <div>
                        {" "}
                        <ResultPerPageComponent
                          countPerPage={resultPerPage}
                          limit={limitPerPage}
                          handleLimitChange={handleLimitChange}
                        />
                      </div>

                      <div
                        style={{
                          display: "flex",
                          fontSize: "14px",
                          padding: "10px 0px 0px 10px",
                          color: theme.textColor.titleColor,
                        }}
                      >
                        {(currentPage - 1) * limitPerPage + 1} -{" "}
                        {Math.min(
                          limitPerPage * currentPage,
                          panelList.data.totalCount
                        )}{" "}
                        of {panelList.data.totalCount} rows
                      </div>
                    </div>
                    <div>
                      <Pagination
                        className="pagination-bar"
                        currentPage={currentPage}
                        totalCount={panelList?.data?.totalCount}
                        pageSize={limitPerPage}
                        onPageChange={(page) => {
                          handlePageChange(page);
                        }}
                      />{" "}
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
        <DeleteDialog
          show={deleteDialog}
          onHide={() => setDeleteDialog(false)}
          onConfirm={() => {
            DeletePanel(
              { value },
              {
                onSuccess: () => {
                  setDeleteDialog(false);
                  setMessage("Panel deleted successfully");
                  setSuccessDialog(true);
                  setCurrentPage(1);
                  setSearchInput("");
                  refetch();
                },
                onError: (error) => {
                  setDeleteDialog(false);
                  setMessage(error?.response?.data?.message);
                  setErrorDialog(true);
                },
              }
            );
          }}
          title={"Are you sure to delete the panel ?"}
          isLoading={deleteLoading}
        />
        <SuccessDialog
          show={suceessDialog}
          onHide={() => setSuccessDialog(false)}
          message={message}
        />
        <ErrorDialog
          show={errorDialog}
          onHide={() => setErrorDialog(false)}
          message={message}
        />
        <InfoModal
          show={showAlertConfirmation}
          onHide={() => {
            setShowAlertConfirmation(false);
          }}
          message={message}
        />
      </div>
    </>
  );
}

export default PanelMangement;
