import React, { useContext, useState, useEffect } from "react";
import { Form, Formik } from "formik";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Select from "../components/FormsUI/Select";
import { panelOptions } from "../common/constants";
import BackButton from "../components/Button/Button";
import Button from "../components/Button/OutlinedButton";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import Calendar from "../components/DatePicker/Calendar";
import { previewPanel } from "../services/panels-api";
import { useMutation } from "react-query";
import { PreviewContext } from "../context/PreviewContext";
import ErrorDialog from "../popups/ErrorDialog";
import * as Yup from "yup";
import dayjs from "dayjs";
import { convertToUTC } from "../common/commonFunctions";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { CssTooltip } from "../components/StyledComponent";
import { InfoIcon } from "../icons";
import { DataContext } from "../context/DataContext";

function PanelFormStepThree({ editDetail, isEdit }) {
  const {
    handleNextClick,
    setFormData,
    formData,
    handlePrevClick,
    handleNextClickStep,
    handlePrevStep,
  } = useContext(multiStepFormContext);
  const { setPreviousValue } = useContext(DataContext);

  const [selectedFilter, setSelectedFilter] = useState({
    startDate:
      formData?.period?.startDate ||
      editDetail?.timePeriod?.split(" to ")[0]?.toString() ||
      "",
    endDate:
      formData?.period?.endDate ||
      editDetail?.timePeriod?.split(" to ")[1]?.toString() ||
      "",
  });
  // const dateRegex = /^\d{2}-[A-Za-z]{3}-\d{4} \d{2}:\d{2}:\d{2}$/;
  const dateRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;

  const [selectedRange, setSelectedRange] = useState(
    formData.range !== undefined
      ? formData.range
      : editDetail !== undefined &&
        Object.keys(editDetail)?.length !== 0 &&
        dateRegex.test(editDetail?.timePeriod?.split(" to ")[0]?.toString())
      ? "Calendar"
      : editDetail?.timePeriod
  );

  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [intervalTypesState, setIntervalTypesState] = useState([]);
  const [errorMessage, setErrorMessage] = useState();
  const [editUpdated, setEditUpdated] = useState(false);
  const { mutate: previewPanelAPI, isLoading = { isLoading } } =
    useMutation(previewPanel);

  const { setPanelFormStepThreeResponse } = useContext(PreviewContext);

  const previewAPICall = () => {
    let reqData = {
      name: formData.panelname,
      visualizationType: formData.type,
      filters: [],
      dataColumns: {
        derivedFields: formData.derivedField,
      },
      startDate: formData.startDate,
      endDate: formData.endDate,
    };
    if (formData.type === "Bar Graph") {
      reqData.dataColumns["X-Axis"] = formData.field;
      reqData.dataColumns.noOfRecords = parseInt(formData.records);
    }
    if (formData.type === "Table Report") {
      reqData.dataColumns.tableFields = formData.field.filter(
        (option) => option !== "Select All"
      );
    }
    if (formData.type === "Line Graph" || formData.type === "MultiAxis Graph") {
      reqData.interval = formData.interval;
    }
    formData.conditions.forEach((condition) => {
      reqData.filters.push({
        field: condition.type1,
        condition: condition.type2,
        value: condition.type3,
        operator: condition.type4,
      });
    });
    previewPanelAPI(
      {
        reqData,
      },
      {
        onSuccess: (resp) => {
          setPanelFormStepThreeResponse(resp);
          handleNextClick();
          handleNextClickStep();
        },
        onError: (error) => {
          toast.error(
            error?.response?.data?.message
              ? error.response.data.message
              : error.message
          );
        },
      }
    );
  };

  const handleSelectedRange = () => {
    let intervals = [];
    let formattedStart, formattedEnd;
    const currentDateTime = dayjs();
    switch (selectedRange) {
      case "Last Hour":
        intervals = [{ label: "Minutes", value: "minute" }];
        const lastHour = currentDateTime.subtract(1, "hour");
        formattedStart = lastHour.format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
        break;
      case "Last 6 Hours":
        intervals = [
          { label: "Hours", value: "hour" },
          //  { label: "Minutes", value: "mins" },
        ];
        const last6hours = currentDateTime.subtract(6, "hour");

        formattedStart = last6hours.format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
        break;
      case "Last 12 Hours":
        intervals = [
          { label: "Hours", value: "hour" },
          // { label: "Minutes", value: "mins" },
        ];
        const last12hours = currentDateTime.subtract(12, "hour");

        formattedStart = last12hours.format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
        break;
      case "Last 24 Hours":
        intervals = [{ label: "Hours", value: "hour" }];
        const last24hours = currentDateTime.subtract(12, "hour");

        formattedStart = last24hours.format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
        break;
      case "Today":
        intervals = [{ label: "Hours", value: "hour" }];
        formattedStart = currentDateTime
          .startOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
        break;

      case "Yesterday":
        intervals = [{ label: "Hours", value: "hour" }];
        const yesterday = currentDateTime.subtract(1, "day");
        formattedStart = yesterday.startOf("day").format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;

      case "Last Seven Days":
        intervals = [
          { label: "Week", value: "week" },
          { label: "Days", value: "day" },
        ];
        formattedStart = currentDateTime
          .subtract(6, "days")
          .startOf("day")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:59:59");
        break;
      case "Last 30 Days":
        intervals = [
          { label: "Days", value: "day" },
          { label: "Week", value: "week" },
        ];
        formattedStart = currentDateTime
          .subtract(29, "days")
          .startOf("day")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime
          .endOf("day")
          .format("YYYY-MM-DD HH:59:59");
        break;
      case "Last Week":
        intervals = [
          { label: "Days", value: "day" },
          { label: "Week", value: "week" },
        ];
        formattedStart = currentDateTime
          .subtract(1, "week")
          .startOf("week")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime
          .subtract(1, "week")
          .endOf("week")
          .format("YYYY-MM-DD HH:59:59");
        break;
      case "Last Month":
        intervals = [
          { label: "Days", value: "day" },
          { label: "Week", value: "week" },
        ];
        formattedStart = currentDateTime
          .subtract(1, "month")
          .startOf("month")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime
          .subtract(1, "month")
          .endOf("month")
          .format("YYYY-MM-DD HH:59:59");
        break;
      case "This Month":
        intervals = [
          { label: "Days", value: "day" },
          { label: "Week", value: "week" },
        ];
        formattedStart = currentDateTime
          .startOf("month")
          .format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = currentDateTime
          .endOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        break;

      default:
        break;
    }
    if (selectedRange !== "Calendar") {
      setSelectedFilter({
        startDate:
          formattedStart ||
          formData?.period?.startDate ||
          editDetail?.timePeriod?.split(" to ")[0]?.toString(),
        endDate:
          formattedEnd ||
          formData?.period?.endDate ||
          editDetail?.timePeriod?.split(" to ")[1]?.toString(),
      });
      setIntervalTypesState(intervals);
    }

    if (
      editDetail?.timePeriod &&
      dateRegex.test(editDetail?.timePeriod?.split(" to ")[0]?.toString()) &&
      dateRegex.test(editDetail?.timePeriod?.split(" to ")[1]?.toString()) &&
      isEdit === true &&
      selectedRange === "Calendar" &&
      editUpdated !== true
    ) {
      //  const handleSetInterval = (formattedStart, formattedEnd) => {
      // let intervals = [];

      const startDate = dayjs(
        editDetail?.timePeriod?.split(" to ")[0]?.toString()
      );
      const endDate = dayjs(
        editDetail?.timePeriod?.split(" to ")[1]?.toString()
      );

      // Calculate the difference in weeks and months
      const minsDifference = endDate.diff(startDate, "minute");

      const weeksDifference = endDate.diff(startDate, "week");
      const monthsDifference = endDate.diff(startDate, "month");
      const hoursDifference = endDate.diff(startDate, "hour");
      const daysDifference = endDate.diff(startDate, "day");

      if (minsDifference <= 60) {
        intervals = [{ label: "Minute", value: "minute" }];
      } else if (
        (hoursDifference > 1 && hoursDifference < 24) ||
        (minsDifference > 60 && minsDifference <= 1440)
      ) {
        intervals = [{ label: "Hour", value: "hour" }];
      } else if (hoursDifference >= 24 && daysDifference < 7) {
        intervals = [{ label: "Day", value: "day" }];
      } else if (daysDifference >= 7 && daysDifference <= 14) {
        intervals = [
          { label: "Week", value: "week" },
          { label: "Day", value: "day" },
        ];
      } else if (weeksDifference >= 2 && monthsDifference < 1) {
        intervals = [{ label: "Week", value: "week" }];
      } else if (monthsDifference >= 1) {
        intervals = [{ label: "Month", value: "month" }];
      }

      setIntervalTypesState(intervals);
    }
    if (selectedRange !== "" || selectedRange !== undefined) {
      setErrorMessage();
      return;
    }
  };
  useEffect(() => {
    handleSelectedRange();
    // }
  }, [selectedRange]);

  const validationSchema = Yup.object().shape({
    range: Yup.string().required("Time Period is required"),
  });
  const handleNextButtonClick = (values) => {
    if (selectedRange === "" || selectedRange === undefined) {
      setErrorMessage("Time Period is required");
      return;
    } else {
      let newFormData = Object.assign(formData, selectedFilter, values);
      setFormData(newFormData);
      previewAPICall();
    }
  };
  const initialValues = {
    period: selectedFilter,
    range: selectedRange,
    interval: formData?.interval || editDetail?.interval || "",
  };

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        enableReinitialize={true}
      >
        {({ values, errors, status, handleChange, setFieldValue }) => (
          <Form>
            <div className=" flex flex-col items-center justify-center mb-48 h-full">
              <div className="w-full md:w-[550px] mt-10">
                <div className="flex">
                  <InputLabel label={"Time Period"} isMandatory={true} />
                  <CssTooltip
                    title={
                      <div className="text-xs p-1">
                        <p className="mb-1.5">
                          During calendar selection, to view reports spanning
                          more than an hour, a day, a week, or a month, make
                          sure to set the start time to 00:00.
                        </p>

                        <p className="mb-1.5">
                          In case of selection of last 6, 12, 24 hours; reports
                          will be generated on an hourly basis with the start
                          time of the selected hour.
                        </p>

                        <p className="mb-1.5">
                          For example, if the current user time range is from
                          13:01 to 13:59, then the report will be generated from
                          13:00 hour onwards
                        </p>
                      </div>
                    }
                    placement="top"
                    arrow
                  >
                    <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
                  </CssTooltip>
                </div>

                <Calendar
                  setSelectedFilter={setSelectedFilter}
                  setSelectedRange={setSelectedRange}
                  selectedRange={selectedRange}
                  selectedFilter={selectedFilter}
                  formData={formData}
                  panel={true}
                  isEdit={isEdit}
                  setEditUpdated={setEditUpdated}
                  setIntervalTypesState={setIntervalTypesState}
                  editDetail={editDetail}
                  handleSelectedRange={handleSelectedRange}
                  isTimeRange={true}
                />
                {errorMessage && (
                  <div className="text-errorColor text-xs">{errorMessage}</div>
                )}
              </div>
              {formData.type === "MultiAxis Graph" ||
              formData.type === "Line Graph" ? (
                <div className="w-full md:w-[550px] mt-7">
                  <InputLabel label={"Interval"} />
                  <Select
                    className="w-full"
                    name="interval"
                    options={intervalTypesState}
                    placeholder={"Select interval"}
                    setFormData={setFormData}
                  />
                </div>
              ) : null}
            </div>
            <div className="flex-grow flex justify-end items-center mx-20 mt-20 mb-20 gap-4">
              <BackButton
                label={"Back"}
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded"
                onClick={() => {
                  handlePrevClick();
                  handlePrevStep();
                  setPreviousValue(true);
                }}
              ></BackButton>
              <Button
                block="true"
                type="submit"
                label="Next"
                value="submit"
                disabled={errorMessage}
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded "
                loading={isLoading}
                onClick={() => handleNextButtonClick(values)}
              ></Button>
            </div>
          </Form>
        )}
      </Formik>
      <ToastContainer position="top-center" autoClose={3000} />

      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
    </div>
  );
}

export default PanelFormStepThree;
