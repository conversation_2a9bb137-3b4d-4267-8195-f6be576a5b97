import React, { useState } from "react";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import ArrowForwardIosSharpIcon from "@mui/icons-material/ArrowForwardIosSharp";
function AvailableItemsAccordion({ title, children, count }) {
  const [expandedAccordion, setExpandedAccordion] = useState(null);
  return (
    <Accordion
      expanded={expandedAccordion === title}
      onChange={() => {
        setExpandedAccordion((prevState) =>
          prevState === title ? null : title
        );
      }}
      sx={{
        boxShadow: "none",
        width: "200px",
        flexDirection: "row-reverse",
      }}
    >
      <AccordionSummary
        expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: "0.7rem" }} />}
        sx={{
          flexDirection: "row-reverse",
          justifyContent: "flex-start",
          "& .MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
            transform: "rotate(90deg)",
          },
          marginLeft: "-16px",
        }}
      >
        <div className="text-headingColor text-sm font-medium ml-3 flex">
          {title}{" "}
          <div className="p-1 ml-2 w-6 h-6 rounded-full bg-bgCheckboxSelection text-[white] text-xs flex items-center justify-center">
            {count}
          </div>
        </div>
      </AccordionSummary>
      {children}
    </Accordion>
  );
}

export default AvailableItemsAccordion;
