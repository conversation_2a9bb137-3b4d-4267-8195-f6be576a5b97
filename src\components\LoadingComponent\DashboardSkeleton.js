import React from "react";
import { Box, CircularProgress, Grid, Typography } from "@mui/material";

const DashboardSkeleton = ({ panelDroppedData }) => {
  console.log("panelDroppedData", panelDroppedData.length);

  const renderSkeleton = (index) => (
    <Grid item xs={12} md={index === 0 ? 12 : 6} key={index}>
      <Box sx={{ position: "relative" }}>
        <div
          style={{
            width: "100%",
            height: "300px",
            marginBottom: "16px",
            backgroundColor: "white",
            borderRadius: "8px",
          }}
        />

        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
        >
          <CircularProgress size={30} style={{ color: "black" }} />
        </Box>
      </Box>
    </Grid>
  );

  return (
    <Box sx={{ padding: 2 }}>
      <Grid container spacing={2}>
        {panelDroppedData.length === 0 && renderSkeleton(0)}
        {panelDroppedData.length === 1 && (
          <>
            {renderSkeleton(0)}
            {renderSkeleton(1)}
          </>
        )}
        {panelDroppedData.length === 2 && (
          <>
            {renderSkeleton(0)}
            {renderSkeleton(1)}
            {renderSkeleton(2)}
          </>
        )}
        {panelDroppedData.length === 3 && (
          <>
            {renderSkeleton(0)}
            {renderSkeleton(1)}
            {renderSkeleton(2)}
            {renderSkeleton(3)}
          </>
        )}
        {panelDroppedData.length === 4 && (
          <>
            {renderSkeleton(0)}
            {renderSkeleton(1)}
            {renderSkeleton(2)}
            {renderSkeleton(3)}
            {renderSkeleton(4)}
          </>
        )}
      </Grid>
    </Box>
  );
};

export default DashboardSkeleton;
