import axios from "axios";
//import { config } from "../assets/config/config";
import getAPIMap from "../routes/ApiUrls";
import { fileDownload } from "../utils/fileDownload";

//const apiUrl = config.api.url;

export const alertServices = {
  getAll,
  createGroup,
  updateGroup,
  deleteGroup,
  createAlert,
  updateAlert,
  deleteAlert,
  getMetaData,
  getAlertsList,
  getAlertsHistory,
  handleStatusUpdate,
  getAlertId,
  getErrDescription,
  getResultCodes,
  getNodeList,
};

async function getAll(options) {
  let url = getAPIMap("alertGroups");

  if (options.queryKey[1] && options.queryKey[1] !== "") {
    url = url + "?page=" + options.queryKey[1];
  }
  if (options.queryKey[2] && options.queryKey[2] !== "") {
    url = url + "&limit=" + options.queryKey[2];
  }
  if (options.queryKey[3] && options.queryKey[3] !== "") {
    url = url + "&search=" + encodeURIComponent(options.queryKey[3]);
  }
  if (options.queryKey[4] && options.queryKey[4] !== "") {
    url = url + "?subGroup=" + options.queryKey[4];
  }

  let response = axios.get(url);
  // console.log("response", response);
  return response;
}

async function createGroup(payload) {
  // console.log("payload", payload);
  return axios.post(getAPIMap("alertGroups"), {
    name: payload.groupName,
    members: payload.chips,
    subGroups: payload.subGroups,
    isSubGroup: payload.subGroupState,
  });
}
async function updateGroup(payload, id) {
  return axios.put(getAPIMap("alertGroups") + `/${id}`, {
    name: payload.groupName,
    members: payload.chips,
    subGroups: payload.subGroups,
    isSubGroup: payload.subGroupState,
  });
}

async function deleteGroup(id) {
  return axios.delete(getAPIMap("alertGroups") + `/${id}`);
}

async function getAlertsList({ page, limit, search, sorts, filters }) {
  const params = {
    page,
    limit,
    search,
    sorts: JSON.stringify(sorts),
    filters: JSON.stringify(filters),
  };
  let url = getAPIMap("alerts");
  const res = await axios.get(url, { params });
  return res;
}

async function getMetaData(options) {
  let url = getAPIMap("alertMetadata");

  if (options.queryKey[1] && options.queryKey[1] !== "") {
    url = url + "/" + options.queryKey[1];
  }

  let response = axios.get(url);
  return response;
}

async function createAlert(options) {
  let url = getAPIMap("alerts");
  let response = axios.post(url, options.reqData);
  return response;
}
export async function getAlertId(options) {
  let url = getAPIMap("alerts") + `/${options.queryKey[1]}`;
  let response = axios.get(url);
  return response;
}

export async function updateAlert(options) {
  let url = getAPIMap("alerts") + `/${options.alertId}`;
  let response = axios.put(url, options.reqData);
  return response;
}

async function deleteAlert(id) {
  return axios.delete(getAPIMap("alerts") + `/${id}`);
}

async function getAlertsHistory({
  page,
  limit,
  search,
  startDate,
  endDate,
  status,
  download,
  downloadType,
}) {
  const params = {
    page,
    limit,
    search,
    startDate,
    endDate,
    status,
    download: download ? 1 : 0,
    downloadType,
    sortBy: "timestamp:desc",
  };

  const res = await axios.get(getAPIMap("getAlertsHistory"), {
    params,
    ...(download ? { responseType: "blob" } : {}),
  });

  if (download) {
    const startDateFormatted = startDate.split(" ")[0];
    const endDateFormatted = endDate.split(" ")[0];
    const currentTime = new Date().toISOString().replace(/[:.-]/g, "_");
    const filename = `Alerts_${startDateFormatted} - ${endDateFormatted}_${currentTime}`;
    fileDownload(res, filename, downloadType);
  }
  return res;
}

export async function getAlertNameList({ queryKey }) {
  const [_, startDate, endDate, searchInput] = queryKey;

  const params = {
    startDate: startDate,
    endDate: endDate,
    search: searchInput,
    sortBy: "timestamp:desc",
  };

  let response = axios.get(getAPIMap("getAlertsHistory") + "/alert-name-list", {
    params,
  });
  return response;
}

export async function viewAlert(options) {
  let url = getAPIMap("getAlertsHistory") + `/view` + `/${options.queryKey[1]}`;
  let response = axios.get(url);
  return response;
}

async function handleStatusUpdate({ status, id }) {
  const alertHistoryIds = Array.isArray(id) ? id : [id];
  return axios.put(getAPIMap("getAlertsHistory"), {
    status: status,
    alertHistoryIds: alertHistoryIds,
  });
}

export async function getAlertNotify(options) {
  let url = getAPIMap("alertNotify");
  let response = axios.get(url);
  return response;
}

export async function getErrDescription(options) {
  let url =
    getAPIMap("alerts") +
    `/err-des-mapping/err-description?resultCode=${options.queryKey[1]}`;
  let response = axios.get(url, options.reqData);
  return response;
}

export async function getNodeList(options) {
  let url = getAPIMap("alerts") + `/node-list`;
  let response = axios.get(url, options.reqData);
  return response;
}

export async function getResultCodes(options) {
  // console.log("options", options);
  let url = getAPIMap("alerts") + `/err-des-mapping/result-code`;
  let response = axios.get(url);
  return response;
}
