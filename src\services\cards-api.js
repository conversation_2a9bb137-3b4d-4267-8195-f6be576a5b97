import axios from "axios";
//import { config } from "../assets/config/config";
import getAPIMap from "../routes/ApiUrls";

//const apiUrl = config.api.url;

export const cardServices = {
  getAll,
  create,
  update,
  deleteCard,
  getAllCardById,
};

async function getAll(options) {
  let url = getAPIMap("card");

  if (options.queryKey[1] && options.queryKey[1] !== "") {
    url = url + "?page=" + options.queryKey[1];
  }
  if (options.queryKey[2] && options.queryKey[2] !== "") {
    url = url + "&limit=" + options.queryKey[2];
  }
  if (options.queryKey[3] && options.queryKey[3] !== "") {
    url = url + "&search=" + encodeURIComponent(options.queryKey[3]);
  }

  let response = axios.get(url);
  // console.log("response", response);
  return response;
}
async function create(payload) {
  return axios.post(getAPIMap("card"), {
    name: payload.name,
    reportField: payload.derivedField,
  });
}
async function getAllCardById(payload) {
  return axios.post(getAPIMap("cardDetails"), {
    ...payload,
  });
}
async function update(payload, id) {
  return axios.put(getAPIMap("card") + `/${id}`, {
    name: payload.name,
    reportField: payload.derivedField,
  });
}
async function deleteCard(id) {
  return axios.delete(getAPIMap("card") + `/${id}`);
}
