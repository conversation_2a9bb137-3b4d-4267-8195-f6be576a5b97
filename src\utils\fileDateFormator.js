export function formatDate() {
  const date = new Date();

  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${hours}_${minutes}_${seconds}`;
}

export function formatDateToUTCAndLocal(inputDate) {
  const date = new Date(inputDate);

  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, "0"); // Months are zero-based
  const day = String(date.getUTCDate()).padStart(2, "0");
  const hours = String(date.getUTCHours()).padStart(2, "0");
  const minutes = String(date.getUTCMinutes()).padStart(2, "0");
  const seconds = String(date.getUTCSeconds()).padStart(2, "0");

  const localYear = date.getFullYear();
  const localMonth = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
  const localDay = String(date.getDate()).padStart(2, "0");
  const localHours = String(date.getHours()).padStart(2, "0");
  const localMinutes = String(date.getMinutes()).padStart(2, "0");
  const localSeconds = String(date.getSeconds()).padStart(2, "0");

  const formattedUTCDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds} UTC`;
  const formattedLocalDate = `${localYear}-${localMonth}-${localDay} ${localHours}:${localMinutes}:${localSeconds}`;

  return { utc: formattedUTCDate, local: formattedLocalDate };
}

export const getFormattedDate = (str, format = "YYYY-MM-DD") => {
  const date = new Date(str);
  const month = ("0" + (date.getMonth() + 1)).slice(-2);
  const day = ("0" + date.getDate()).slice(-2);
  const year = date.getFullYear();

  switch (format) {
    case "YYYY-MM-DD":
      return [year, month, day].join("-");
    case "DD-MM-YYYY":
      return [day, month, year].join("-");
    case "MM-DD-YYYY":
      return [month, day, year].join("-");
    default:
      return [year, month, day].join("-");
  }
};

export function getTodayGMT() {
  const now = new Date();
  const gmtDate = new Date(
    Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate())
  );
  return `${gmtDate.toISOString().split("T")[0]} 00:00:00`;
}

export function formatDateCustom(inputDate) {
  //sample date format: 17-Jan-2025 13:23:36
  const date = new Date(inputDate);

  // Get the day, month, year, and time components
  const day = String(date.getDate()).padStart(2, "0");
  const month = date.toLocaleString("en-GB", { month: "short" }); // "Jan", "Feb", etc.
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
}
