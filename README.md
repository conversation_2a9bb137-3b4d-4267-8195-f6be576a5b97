# Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles <PERSON>act in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you're on your own.

You don't have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn't feel obligated to use this feature. However we understand that this tool wouldn't be useful if you couldn't customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)

## Frontend configurations
Colors:

In the src folder -> open tailwind.config.js.As seen below stylings are defined in the file.
### Below are the property mappings that are provided in backgroundImage:

1.logo - To change the logo in the login page and header .

### Below are the property mappings that are provided in backgroundColor :

1.bgPrimary - Primary background for the application body .\
2.bgSecondary - Secondary color of the application mainly used for background color of the buttons .\
3.bgTertiary - Tertiary color of the application mainly used in steppers for heading and to show selected filters .\
4.bgouterBackground- Background color of the dialogs in the application .\
5.selectBackground - on hover  Background color of the lists in my  dashboard page .\
6.bgHeader - Application header background color .\
7.bgSidebar - Background color of the side nav bar .\
8.rowHoverBackground - On hover color of the table rows in the application .\
9.tableHeader - Tables header background color .\

### Below are the property mappings that are provided in borderColor:
1.listBorder- Border color of the listing tables .\
2.outerBorder-Border color used for calendar filter and selected filter components in the panel management step 2 .\
3.panelBorder- Border color used for headers in add dialogs and downloading.. text .\
4.errorBorder- Red border used for buttons .\
5.calendarBoreder - Border color used for calendar component and input text boxes .\
6.tabColor- Border color for action buttons in table .\
7.tableBorder-Border color of the tables	.\
8.tableCellBorder-Border color of the table rows .\

### Below are the property mappings that are provided in textColor:

1.headingColor-Text color for input labels and page headings .\
2.tabColor- 
3.errorColor- Text color of the error messages .\
4.titleColor - Text color for Dashboard list, edit and delete buttons in tables .\
5.tooltipTextColor- Text color of tooltip .\
6.tabTextColor- Default tab text color when the color is not passed specifically.


### Font:
To update the font family:
Please upload the font files in src-> assets-> font folder
Then change the url and font-family name  in index.css file for the below lines of code

