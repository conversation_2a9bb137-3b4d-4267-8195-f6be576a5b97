import LineChartGraph from "../components/charts/LineChartGraph";
import BarChartGraph from "../components/charts/BarChartGraph";
import GaugeChartGraph from "../components/charts/GaugeChartGraph";
import ScatterPlotChartGraph from "../components/charts/ScatterPlotChartGraph";
import PieChartComponent from "../components/charts/PieChartGraph";
import HeatMapChartGraph from "../components/charts/HeatMapChartGraph";
import MultiAxisChartGraph from "../components/charts/MultiAxisChartGraph";
import OutlinedButton from "../components/Button/Button";
import graphImage from "../assets/img/graph.png";

const DynamicReportGraphView = ({
  visualizationType,
  data,
  config,
  totalCount,
  configApiData,
  onSetupClick,
}) => {
  // Common record count UI
  const renderRecordCount = () => (
    <div className="flex justify-end mr-5">
      <span className="text-xs font-medium text-gray-600">
        No. of records:{" "}
        <span className="font-bold text-gray-800">
          {totalCount >= configApiData.GRAPH_PAGE_LIMIT
            ? configApiData.GRAPH_PAGE_LIMIT
            : totalCount}
          /{totalCount}
        </span>
      </span>
    </div>
  );

  // Chart mapping
  const chartComponents = {
    line: (
      <LineChartGraph data={data} config={config} totalCount={totalCount} />
    ),
    bar: <BarChartGraph data={data} config={config} />,
    pie: <PieChartComponent data={data} config={config} />,
    gauge: <GaugeChartGraph data={data} config={config} />,
    scatter: <ScatterPlotChartGraph data={data} config={config} />,
    heat: (
      <div style={{ position: "relative", zIndex: 1 }}>
        <HeatMapChartGraph data={data} config={config} />
      </div>
    ),
    multiAxis: <MultiAxisChartGraph data={data} config={config} />,
  };

  if (!visualizationType) {
    return (
      <>
        <label className="text-xl font-bold text-[#6E6565]">
          Let’s set up your graph preferences.
        </label>
        <img
          src={graphImage}
          alt="Graph Representation"
          className="mx-auto py-5"
        />
        <OutlinedButton
          label="Create Visualization"
          type="button"
          buttonClassName="w-48 border-errorBorder text-errorColor"
          onClick={onSetupClick}
        />
      </>
    );
  }

  return (
    <>
      {renderRecordCount()}
      {chartComponents[visualizationType] || null}
    </>
  );
};

export default DynamicReportGraphView;
