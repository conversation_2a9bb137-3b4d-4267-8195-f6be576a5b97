import LoadingButton from "@mui/lab/LoadingButton";
import React from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
const AddButton = ({ buttonClassName, textClassName, ...rest }) => {
  const { t } = useTranslation();
  return (
    <LoadingButton
      {...rest}
      // Below line is commented so that the text can be transformedto uppercase as well.Please set as buttonclassname for the required styles
      style={{ textTransform: "none" }}
      variant="outlined"
      className={twMerge(
        `h-10 border-2  border-panelBorder rounded-10px font-medium text-center text-medium text-headingColor hover:bg-transparent  ${buttonClassName}`
      )}
    >
      {t(`${rest.label}`)}
    </LoadingButton>
  );
};

export default AddButton;
