import { lazy } from "react";
// use lazy for better code splitting, a.k.a. load faster
const Home = lazy(() => import("../pages/Home"));
const Roles = lazy(() => import("../pages/Roles"));
const Users = lazy(() => import("../pages/Users"));
const CreateDashboard = lazy(() => import("../pages/CreateDashboard"));
const Logs = lazy(() => import("../pages/Logs"));
const Reports = lazy(() => import("../pages/ReportsTab"));
const AddRole = lazy(() => import("../pages/AddRole"));
const Cards = lazy(() => import("../pages/Cards"));
const Group = lazy(() => import("../pages/Groups"));
const OTPVerification = lazy(() => import("../pages/OTPVerification"));
const AddAlerts = lazy(() => import("../pages/AddAlerts"));
const PanelMangement = lazy(() =>
  import("../pages/PanelManagement/PanelMangement")
);
const DashboardManagement = lazy(() =>
  import("../pages/DashboardManagement/DashboardDetails")
);
const DashboardCreation = lazy(() =>
  import("../pages/DashboardManagement/DashboardCreation")
);
const MyDashboard = lazy(() =>
  import("../pages/DashboardManagement/MyDashboard")
);
const DashboradEdit = lazy(() =>
  import("../pages/DashboardManagement/DashboradEdit")
);
const MyDashboardList = lazy(() =>
  import("../pages/DashboardManagement/MyDashboardList")
);
const TableView = lazy(() => import("../pages/PanelManagement/TableView"));
const StaticReports = lazy(() => import("../pages/StaticReports"));
const DynamicReports = lazy(() => import("../pages/DynamicReports"));
const UserAnalysis = lazy(() =>
  import("../pages/DashboardManagement/UserAnalysis")
);
const Alerts = lazy(() => import("../pages/AlertsList"));
const AlertHistory = lazy(() => import("../pages/AlertHistory"));
const CDRSearch = lazy(() => import("../pages/CDRSearch"));
const CDRSearchDownloads = lazy(() => import("../pages/CdrSearchDownloads"));

/**
 * :warning: These are internal routes!
 * They will be rendered inside the app, using the default `containers/Layout`.
 * If you want to add a route to, let's say, a landing page, you should add
 * it to the `App`'s router, exactly like `Login`, `CreateAccount` and other pages
 * are routed.
 *
 * If you're looking for the links rendered in the SidebarContent, go to
 * `routes/sidebar.js`
 */
const routes = [
  {
    path: "/home",
    component: Home,
  },
  {
    path: "rolemanagement",
    component: Roles,
  },
  {
    path: "cardmanagement",
    component: Cards,
  },
  {
    path: "groupmanagement",
    component: Group,
  },
  {
    path: "panelmanagement",
    component: PanelMangement,
  },
  {
    path: "usermanagement",
    component: Users,
  },
  {
    path: "dashboard/details",
    component: DashboardManagement,
  },
  {
    path: "createdashboard",
    component: CreateDashboard,
  },
  {
    path: "logs",
    component: Logs,
  },
  {
    path: "reports",
    component: Reports,
  },
  {
    path: "/reports/static-reports",
    component: StaticReports,
  },
  {
    path: "/reports/dynamic-reports",
    component: DynamicReports,
  },
  {
    path: "editrole",
    component: AddRole,
  },

  {
    path: "createdashboard",
    component: CreateDashboard,
  },
  {
    path: "logs",
    component: Logs,
  },
  {
    path: "reports",
    component: Reports,
  },
  {
    path: "editrole",
    component: AddRole,
  },
  {
    path: "/alerts",
    component: Alerts,
  },
  {
    path: "/alerts/alerts-history",
    component: AlertHistory,
  },
  { path: "alerts/add", component: AddAlerts },
  {
    path: "/rolemanagement/add",
    component: AddRole,
  },
  {
    path: "/panelmanagement/add",
    component: TableView,
  },
  {
    path: "/dashboard/details/create",
    component: DashboardCreation,
  },
  {
    path: "/dashboard/mydashboard/:id",
    component: MyDashboard,
  },
  {
    path: "/dashboard/details/edit/:id",
    component: DashboradEdit,
  },
  {
    path: "/dashboard/details/mydashboard",
    component: MyDashboardList,
  },
  {
    path: "/usermanagement/useranalysis",
    component: UserAnalysis,
  },
  {
    path: "/verify-otp",
    component: OTPVerification,
  },
  {
    path: "/cdrsearch",
    component: CDRSearch,
  },
  {
    path: "/offline/downloads",
    component: CDRSearchDownloads,
  },
];
export default routes;
