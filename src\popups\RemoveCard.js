import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "../components/Button/OutlinedButton";
import CancelButton from "../components/Button/Button";
import DialogActions from "@mui/material/DialogActions";
import { CloseIcon } from "../icons";

const RemoveCard = (props) => {
  const {
    onCancelClick,
    open,
    width = 450,
    handleClose,
    message,
    message1,
    ...other
  } = props;

  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width,
          textAlign: "center",
          position: "relative",
        },
      }}
      maxWidth="xs"
      open={open}
      {...other}
      className="p-6 rounded-md"
    >
      <CloseIcon
        onClick={onCancelClick}
        className="text-tertiary w-2 h-2 absolute top-3 right-4 cursor-pointer"
      />

      <div className="mt-[34px] mx-3 mb-3 border-b border-panelBorder" />
      <div className="text-tabColor text-base mb-4">{message}</div>
      <div className="text-black text-lg"> {message1}</div>
      <div className="text-center mt-10 gap-5 mb-10">
        {/* <CancelButton
          onClick={onCancelClick}
          label={"Okay"}
          buttonClassName="w-[100px] h-10 text-xs  "
        ></CancelButton> */}
        <Button
          onClick={onCancelClick}
          label={"Okay"}
          buttonClassName="w-[100px] h-10 text-xs ml-5"
        ></Button>
      </div>
    </Dialog>
  );
};
export default RemoveCard;
