import * as React from "react";
import Box from "@mui/material/Box";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import StepConnector from "@mui/material/StepConnector";
import Typography from "@mui/material/Typography";
import { useTranslation } from "react-i18next";
import { DataContext } from "../../context/DataContext";

const stepsConfig = {
  panel: ["Add", "Verify", "Classify", "Submit"],
  alerts: ["Basic Details", "Node Details", "Advanced Filters", "Send Email"],
};

const CustomStepConnector = () => (
  <StepConnector
    sx={{
      "&.Mui-active .MuiStepConnector-line": { borderColor: "#DC3833" },
      "&.Mui-completed .MuiStepConnector-line": { borderColor: "#DC3833" },
      "& .MuiStepConnector-line": { borderColor: "#d9d9d9", borderWidth: 2 },
    }}
  />
);

const StepperHeader = (props) => {
  const { module, atStep } = props;
  const steps = stepsConfig[module] || [];
  const { t } = useTranslation();
  const { alertDetails } = React.useContext(DataContext) || {};

  const formatString = (str) =>
    str
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (char) => char.toUpperCase())
      .trim();

  return (
    <Box sx={{ width: "100%" }}>
      <Stepper
        activeStep={atStep}
        alternativeLabel
        connector={<CustomStepConnector />}
      >
        {steps.map((label, index) => (
          <Step
            key={label}
            sx={{
              "& .MuiStepLabel-root .Mui-completed": { color: "#DC3833" },
              "& .MuiStepLabel-label.Mui-completed.MuiStepLabel-alternativeLabel":
                {
                  color: "common.black",
                },
              "& .MuiStepLabel-root .Mui-active": { color: "#DC3833" },
              "& .MuiStepLabel-label.Mui-active.MuiStepLabel-alternativeLabel":
                {
                  color: "common.black",
                },
              "& .MuiStepLabel-root .Mui-active .MuiStepIcon-text": {
                fill: "white",
              },
              "& .MuiStepIcon-root": { width: 30, height: 30 },
              ...(module === "panel" && {
                "& .MuiStepLabel-label": { display: "none" }, // Hide text labels for panel
              }),
            }}
          >
            <div>
              {" "}
              <StepLabel>{t(label)}</StepLabel>
              {module === "alerts" && index < atStep && (
                <Box
                  sx={{
                    mt: 1,
                    marginX: "auto",

                    display: "flex",
                    // justifyContent: "center",
                    minHeight: "100px",
                    // paddingLeft: "50px",
                    maxWidth: "150px",
                    //textAlign: "left",
                  }}
                >
                  <ul
                    style={{
                      listStyleType: "disc",
                      color: "grey",
                      //padding: 0,
                      padding: "0 0px 0 40px",
                      margin: 0,
                    }}
                  >
                    {alertDetails?.[index]?.map((detail, idx) => (
                      <li key={idx}>
                        <Typography variant="caption" color="textSecondary">
                          {t(formatString(detail))}
                        </Typography>
                      </li>
                    ))}
                  </ul>
                </Box>
              )}
            </div>
          </Step>
        ))}
      </Stepper>
    </Box>
  );
};

export default StepperHeader;
