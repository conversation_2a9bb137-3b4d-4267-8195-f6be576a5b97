export const SelectedFiltersDisplay = ({ conditions }) => {
  const normalizeConditions = (conditions) => {
    if (!Array.isArray(conditions)) return [];

    return conditions.map((cond) => {
      if ("field" in cond) {
        return {
          field: cond.field,
          condition: cond.condition,
          value: cond.value,
          operator: cond.operator,
        };
      } else if ("type1" in cond) {
        return {
          field: cond.type1,
          condition: cond.type2,
          value: cond.type3,
          operator: cond.type4,
        };
      }
      return cond;
    });
  };

  const normalized = normalizeConditions(conditions);
  const isEmpty = normalized.every((cond) =>
    Object.values(cond).every((v) => v === "")
  );

  return (
    <>
      {isEmpty ? (
        <div className="mb-1">
          <span className="rounded-md p-1 text-xs ml-1 text-gray-500">
            No Filters Selected
          </span>
        </div>
      ) : (
        normalized.map((condition, index) => {
          const { field, condition: cond, value, operator } = condition;
          const isLast = index === normalized.length - 1;
          return (
            <div key={index} className="mb-1">
              <span className="rounded-md p-1 text-xs ml-1 text-gray-800">
                {`${field} ${cond} ${value}`} {!isLast && `{${operator}}`}
              </span>
            </div>
          );
        })
      )}
    </>
  );
};
