import { Mo<PERSON>, But<PERSON> } from "react-bootstrap";

function InputModal({
  icon,
  title,
  body,
  show,
  onHide,
  onConfirm,
  cancelHide,
  confirmButonName,
  align,
}) {
  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        {title ? <Modal.Title>{title}</Modal.Title> : ""}
      </Modal.Header>
      <Modal.Body>{body}</Modal.Body>
      <Modal.Footer style={{ alignItems: align }}>
        {cancelHide && cancelHide === "true" ? (
          ""
        ) : (
          <Button variant="secondary" onClick={onHide}>
            Cancel
          </Button>
        )}

        <Button variant="danger" onClick={onConfirm}>
          {confirmButonName ? confirmButonName : "Delete"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default InputModal;
