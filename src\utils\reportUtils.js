import dayjs from "dayjs";
import { defaultTimeRange, timeAcessOptions } from "../common/constants";

/**
 * Builds request data payload for API calls
 * @param {Object} params - Parameters for building request data
 * @returns {Object} - Request data object
 */
export const buildRequestData = ({
  data,
  formattedStart,
  formattedEnd,
  timeZone,
  reportFilters = {},
  searchStr = "",
  intervalData,
  isDownload = false,
  limitPerPage,
  currentPage,
  activeView,
  configApiData,
  graphFilters,
}) => {
  let reqData = {
    reportName: "Dynamic Report",
    name: data.name,
    visualizationType: data.visualizationType,
    filters: [],
    dataColumns: {
      derivedFields: data.dataColumns ? data.dataColumns.derivedFields : [],
    },
    startDate: formattedStart,
    endDate: formattedEnd,
    timezone: timeZone,
    reportFilters: reportFilters,
    search: searchStr,
    ...(intervalData ? { interval: intervalData } : {}),
  };

  // Add download parameter for Table Reports when downloading
  if (isDownload && data.visualizationType === "Table Report") {
    reqData.download = 1;
  }

  if (data.visualizationType === "Bar Graph") {
    reqData.dataColumns["X-Axis"] = data.dataColumns["X-Axis"];
    reqData.dataColumns.noOfRecords = parseInt(data.dataColumns.noOfRecords);
  } else {
    reqData.dataColumns.tableFields = data?.dataColumns?.tableFields;
  }

  if (data.visualizationType === "Table Report") {
    if (activeView === "graph" && graphFilters) {
      reqData.graphFilters = graphFilters;
    }
    reqData.limit =
      activeView === "table" ? limitPerPage : configApiData?.GRAPH_PAGE_LIMIT;
    reqData.page = currentPage;
  }

  // Add filters
  data.filters.forEach((condition) => {
    reqData.filters.push({
      field: condition.field,
      condition: condition.condition,
      value: condition.value,
      operator: condition.operator,
    });
  });

  return reqData;
};

/**
 * Generates table columns configuration from response data
 * @param {Object} responseData - API response data
 * @param {string} visualizationType - Type of visualization
 * @returns {Array} - Array of column configurations
 */
export const generateTableColumns = (responseData, visualizationType) => {
  if (visualizationType !== "Table Report") {
    return [];
  }

  if (!responseData?.data || responseData?.data?.length === 0) {
    return [];
  }

  const firstItem = responseData?.data[0];
  if (!firstItem) {
    return [];
  }

  const keys = Object.keys(firstItem);

  return keys.map((key) => ({
    header: key,
    accessorKey: key,
    Cell: ({ row }) => {
      const value = row.original[key];
      if (key === "Date") {
        return dayjs(value, { format: "YYYY-MM-DDTHH:mm:ss" }).isValid()
          ? dayjs(value).format("DD/MM/YYYY HH:mm:ss")
          : value;
      }
      return value;
    },
  }));
};

/**
 * Gets time range options based on viewBy parameter
 * @param {Array} viewBy - ViewBy array
 * @returns {Array} - Time range options
 */
export const getTimeRangeOptions = (viewBy) => {
  if (viewBy.length === 5) {
    return defaultTimeRange;
  } else {
    let newRange = viewBy.map((x) => {
      return timeAcessOptions[x];
    });
    return [...new Set(newRange.flat(1))];
  }
};

/**
 * Creates API call deduplication key
 * @param {Object} params - Parameters for creating unique key
 * @returns {string} - Unique key string
 */
export const createApiCallKey = (params) => {
  return JSON.stringify(params);
};

/**
 * Checks user permissions for download functionality
 * @param {Object} user - User object
 * @param {Array} roles - User roles
 * @param {string} reportName - Report name
 * @param {string} creater - Report creator
 * @returns {number} - Permission level (0 or 1)
 */
export const checkDownloadPermission = (user, roles, reportName, creater) => {
  const matchingReports = roles?.dynamicReports?.filter((report) => {
    return report.name === reportName;
  });

  return user.isSuperAdmin || creater === "Other User"
    ? 1
    : matchingReports[0]?.permissions?.download || 0;
};
