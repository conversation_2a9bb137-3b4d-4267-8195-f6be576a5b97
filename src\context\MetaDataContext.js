import React, { createContext, useContext, useState } from "react";
import { useQuery } from "react-query";
import { reportService } from "../services/staticreport.service";
import { getdestination } from "../services/dropdown-api";
import { DataContext } from "./DataContext";

export const MetaDataContext = createContext();

export const MetaDataProvider = ({ children }) => {
  const [customers, setCustomers] = useState([]);
  const [customerBind, setCustomerBind] = useState([]);
  const [customerProtocol, setCustomerProtocol] = useState([]);
  const [customerBillingLogic, setCustomerBillingLogic] = useState([]);
  const [customerInterfaceType, setCustomerInterfaceType] = useState([]);
  const [customerInterconnect, setCustomerInterconnect] = useState([]);

  const [suppliers, setSuppliers] = useState([]);
  const [supplierBind, setSupplierBind] = useState([]);
  const [supplierProtocol, setSupplierProtocol] = useState([]);
  const [supplierBillingLogic, setSupplierBillingLogic] = useState([]);
  const [supplierInterfaceType, setSupplierInterfaceType] = useState([]);
  const [supplierInterconnect, setSupplierInterconnect] = useState([]);

  const [destinationCountryList, setDestinationCountryList] = useState([]);
  const [destinationNameList, setDestinationNameList] = useState([]);
  const [lcrDataList, setLCRDataList] = useState([]);
  const [specLCRDataList, setSpecLCRDataList] = useState([]);
  const [cdrStatus, setCDRStatus] = useState([]);
  const [sourcePrime, setSourcePrime] = useState([]);
  const [destinationPrime, setDestinationPrime] = useState([]);
  const [customerAirtelKamList, setCustomerAirtelKamList] = useState([]);
  const [supplierAirtelKamList, setSupplierAirtelKamList] = useState([]);

  const [xAxis, setXAxis] = useState([]);
  const [yAxis, setYAxis] = useState([]);
  const [zAxis, setZAxis] = useState([]);
  const [colorBy, setColorBy] = useState([]);
  const [bubbleSize, setBubbleSize] = useState([]);

  const { bilateralData } = useContext(DataContext);

  const withSelectAll = (list) => [
    { value: "Select All", label: "Select All" },
    ...(list || []),
  ];

  // ✅ Customer data
  useQuery(["CustomerList"], reportService.getCustomers, {
    onSuccess: ({ data }) => {
      const filteredCustomers = data.filter(({ id, name }) => id && name);
      const filteredCustomerBind = data.filter(
        ({ customer_bind }) => customer_bind
      );
      const filteredCustomerProtocol = data.filter(
        ({ protocol_type }) => protocol_type
      );
      const filteredCustomerBilling = data.filter(
        ({ billing_logic }) => billing_logic
      );
      const filteredInterface = data.filter(
        ({ interface_type }) => interface_type
      );
      const filteredAirtelKam = data.filter(({ airtel_kam }) => airtel_kam);
      const filteredInterconnect = data.filter(({ ECPaaS }) => ECPaaS);

      const customerList = filteredCustomers
        .map(({ id, name }) => ({ value: name, label: name }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const customerBindList = filteredCustomerBind
        .map(({ customer_bind }) => ({
          value: customer_bind,
          label: customer_bind,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const customerProtocolList = filteredCustomerProtocol
        .map(({ protocol_type }) => ({
          value: protocol_type,
          label: protocol_type,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const customerBilling = filteredCustomerBilling
        .map(({ billing_logic }) => ({
          value: billing_logic,
          label: billing_logic,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const interfaceType = filteredInterface
        .map(({ interface_type }) => ({
          value: interface_type,
          label: interface_type,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const airtelKamList = filteredAirtelKam
        .map(({ airtel_kam }) => ({
          value: airtel_kam,
          label: airtel_kam,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );
      const interconnect = filteredInterconnect
        .map(({ ECPaaS }) => ({
          value: ECPaaS,
          label: ECPaaS,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      setCustomerBind(withSelectAll(customerBindList));
      setCustomers(withSelectAll(customerList));
      setCustomerProtocol(withSelectAll(customerProtocolList));
      setCustomerBillingLogic(withSelectAll(customerBilling));
      setCustomerInterfaceType(withSelectAll(interfaceType));
      setCustomerAirtelKamList(withSelectAll(airtelKamList));
      setCustomerInterconnect(withSelectAll(interconnect));
    },
    onError: () => {
      setCustomers([]);
      setCustomerBind([]);
      setCustomerProtocol([]);
      setCustomerAirtelKamList([]);
    },
  });

  // ✅ Supplier data
  useQuery(["SupplierList"], reportService.getSuppliers, {
    onSuccess: ({ data }) => {
      const filteredSuppliers = data.filter(({ id, name }) => id && name);
      const filteredSupplierBind = data.filter(
        ({ supplier_bind }) => supplier_bind
      );
      const filteredSupplierProtocol = data.filter(
        ({ protocol_type }) => protocol_type
      );
      const filteredSupplierBilling = data.filter(
        ({ billing_logic }) => billing_logic
      );
      const filteredInterface = data.filter(
        ({ interface_type }) => interface_type
      );

      const filteredAirtelKam = data.filter(({ airtel_kam }) => airtel_kam);
      const filteredInterconnect = data.filter(({ ECPaaS }) => ECPaaS);

      const supplierList = filteredSuppliers
        .map(({ id, name }) => ({
          value: name,
          label: name,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const supplierBindList = filteredSupplierBind
        .map(({ supplier_bind }) => ({
          value: supplier_bind,
          label: supplier_bind,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const supplierProtocolList = filteredSupplierProtocol
        .map(({ protocol_type }) => ({
          value: protocol_type,
          label: protocol_type,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const supplierBilling = filteredSupplierBilling
        .map(({ billing_logic }) => ({
          value: billing_logic,
          label: billing_logic,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const interfaceType = filteredInterface
        .map(({ interface_type }) => ({
          value: interface_type,
          label: interface_type,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      const airtelKamList = filteredAirtelKam
        .map(({ airtel_kam }) => ({
          value: airtel_kam,
          label: airtel_kam,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );
      const interconnect = filteredInterconnect
        .map(({ ECPaaS }) => ({
          value: ECPaaS,
          label: ECPaaS,
        }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      setSuppliers(withSelectAll(supplierList));
      setSupplierBind(withSelectAll(supplierBindList));
      setSupplierProtocol(withSelectAll(supplierProtocolList));
      setSupplierBillingLogic(withSelectAll(supplierBilling));
      setSupplierInterfaceType(withSelectAll(interfaceType));
      setSupplierAirtelKamList(withSelectAll(airtelKamList));
      setSupplierInterconnect(withSelectAll(interconnect));
    },
    onError: () => {
      setSuppliers([]);
      setSupplierBind([]);
      setSupplierProtocol([]);
    },
  });

  // ✅ Destination Country
  useQuery(["destinationCountryData", bilateralData], getdestination, {
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      const uniqueCountries = Array.from(
        new Set(data.map((item) => item.country_name))
      ).map((country) => ({
        value: country,
        label: country,
      }));

      const uniqueOperators = Array.from(
        new Set(data.map((item) => item.operator_name))
      ).map((operator) => ({
        value: operator,
        label: operator,
      }));

      setDestinationCountryList(withSelectAll(uniqueCountries));
      setDestinationNameList(withSelectAll(uniqueOperators));
    },

    onError: () => {
      setDestinationCountryList([]);
      setDestinationNameList([]);
    },
  });

  // ✅ LCR Data List
  useQuery(["lcrDataList"], reportService.getLCRData, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const data = res?.data || [];

      // Regular LCR List using lcr_name
      setLCRDataList(
        withSelectAll(
          data.map((type) => ({
            label: type.lcr_name,
            value: type.lcr_name,
          }))
        )
      );

      // Special LCR List using unique lcr_type with Set
      const uniqueLcrTypes = Array.from(
        new Set(data.map((type) => type.lcr_type).filter(Boolean)) // filter(Boolean) removes null/undefined
      ).map((type) => ({
        value: type,
        label: type,
      }));

      setSpecLCRDataList(withSelectAll(uniqueLcrTypes));
    },
    onError: () => {
      setLCRDataList([]);
      setSpecLCRDataList([]);
    },
  });

  // ✅ CDR Status List
  useQuery(["cdrStatusList"], reportService.getCDRStatus, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      setCDRStatus(
        withSelectAll(
          res?.data?.map((type) => ({
            value: type.description,
            label: type.description,
          }))
        )
      );
    },
    onError: () => {
      setCDRStatus([]);
    },
  });

  useQuery(["sourcePrime"], reportService.getSourcePrime, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const uniqueSourcePrime = [
        ...new Set(res?.data?.map((type) => type.description)),
      ].map((description) => ({
        value: description,
        label: description,
      }));

      setSourcePrime(withSelectAll(uniqueSourcePrime));
    },
    onError: () => {
      setSourcePrime([]);
    },
  });

  useQuery(["destinationPrime"], reportService.getDestinationPrime, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const uniqueDestinationPrime = [
        ...new Set(res?.data?.map((type) => type.description)),
      ].map((description) => ({
        value: description,
        label: description,
      }));

      setDestinationPrime(withSelectAll(uniqueDestinationPrime));
    },
    onError: () => {
      setDestinationPrime([]);
    },
  });

  useQuery(
    ["derivedFieldsXAxis", { allDerivedFields: true }],
    reportService.getDerivedField,
    {
      refetchOnWindowFocus: false,
      onSuccess: (res) => {
        const derivedFieds = res?.data.map((field) => ({
          value: field,
          label: field,
        }));

        setXAxis(withSelectAll(derivedFieds));
        setYAxis(withSelectAll(derivedFieds));
        setBubbleSize(derivedFieds);
      },
      onError: () => {
        setXAxis([]);
      },
    }
  );

  useQuery(
    ["nonAggregatedFields", { allDerivedFields: true }],
    reportService.getNonAggregatedField,
    {
      refetchOnWindowFocus: false,
      onSuccess: (res) => {
        const derivedFieds = res?.data.map((field) => ({
          value: field,
          label: field,
        }));
        setColorBy(derivedFieds);
      },
      onError: () => {
        setXAxis([]);
      },
    }
  );

  return (
    <MetaDataContext.Provider
      value={{
        customers,
        customerBind,
        customerProtocol,
        suppliers,
        supplierBind,
        supplierProtocol,
        destinationCountryList,
        destinationNameList,
        lcrDataList,
        cdrStatus,
        supplierBillingLogic,
        customerBillingLogic,
        specLCRDataList,
        customerInterfaceType,
        supplierInterfaceType,
        sourcePrime,
        destinationPrime,
        customerAirtelKamList,
        supplierAirtelKamList,
        customerInterconnect,
        supplierInterconnect,
        xAxis,
        yAxis,
        bubbleSize,
        colorBy,
      }}
    >
      {children}
    </MetaDataContext.Provider>
  );
};
