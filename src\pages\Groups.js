import React, { useMemo, useState, useContext } from "react";
import Table from "../components/table/ReportTable";
import { EditingIcon, TrashIcon, SearchIcon } from "../icons";
import { alertServices } from "../services/alert-api";
import InfoModal from "../components/modals/InfoModal";
import Pagination from "../components/Pagination/Pagination";
import SuccessDialog from "../popups/SuccessDialog";
import ErrorDialog from "../popups/ErrorDialog";
import DeleteDialog from "../popups/DeleteDialog";
import AddGroup from "../popups/AddGroup";
import { AuthContext } from "../context/AuthContext";
import { CssTooltip } from "../components/StyledComponent";
import { DataContext } from "../context/DataContext";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import Button from "../components/Button/OutlinedButton";
import EditButton from "../components/Button/EditButton";
import { useQuery, useQueryClient } from "react-query";
import Title from "../Title";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import bgImage from "../assets/img/Records.png";
import theme from "../tailwind-theme";

function Groups() {
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [users, setUsers] = useState([]);
  const [modified, setModified] = useState([]);
  const [edit, setEdit] = useState(false);
  const [selectedDetails, setSelectedDetails] = useState();
  const [id, setId] = useState("");
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [errorDialog, setErrorDialog] = useState(false);
  const [searchStr, setSearchStr] = useState("");
  const [reload, setReload] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [limitPerPage, setLimitPerPage] = useState(10);
  const [searchInput, setSearchInput] = useState("");
  const { resultPerPage } = useContext(DataContext);
  const queryClient = useQueryClient();
  const { roles } = useContext(AuthContext);

  const permissions = roles?.resources?.filter(
    (res) => res.name === "Group Management"
  )[0].permissions;

  function openGroupDialog() {
    setOpenDialog(true);
  }
  function closeGroupDialog() {
    setOpenDialog(false);
  }
  const handleEdit = (rowData) => {
    setOpenDialog(true);
    setEdit(true);
    setSelectedDetails(rowData);
  };
  const handleDelete = (rowData) => {
    setId(rowData.id);
    setDeleteDialog(true);
  };

  const {
    data: groupList,
    isLoading: groupLoading,
    isFetching,
    refetch,
  } = useQuery(
    ["groupListPopup", currentPage, limitPerPage, searchInput],
    alertServices.getAll,
    {
      onSuccess: (data) => {
        let newData = data?.data?.data?.map((x) => ({
          ...x,
          //createdAt: formatDateTime(x.createdAt),
          //dayjs(x.createdAt).format("DD/MM/YYYY"),
          groupMembers: x?.members.concat(x?.subGroups?.map((y) => y.name)),
        }));
        setUsers(data);
        setModified(newData);
      },
    }
  );

  //   console.log("searchStr", searchStr);
  function handlePageChange(page) {
    setCurrentPage(page);
  }

  const columns = useMemo(
    () => [
      {
        header: "Group Name",
        accessorKey: "name",
      },
      {
        header: "Creation Date",
        accessorKey: "createdAt",
      },
      {
        header: "Mail Ids / Groups",
        accessorKey: "groupMembers",
        Cell: ({ row }) => (
          <div
            style={{
              whiteSpace: "pre-wrap",
              wordWrap: "break-word",
              maxWidth: "300px",
              paddingRight: "4rem",
            }}
            className="cursor-pointer"
          >
            <CssTooltip
              title={`Group name: ${
                row.original.name
              } \n Members: ${row.original.members.join(
                ", "
              )}\n Groups: ${row?.original?.subGroups?.map((x) => x.name)} `}
              placement="bottom"
              arrow
            >
              <div className="">
                {row.original.members?.[0]
                  ? row.original.members?.[0]
                  : row.original.subGroups?.[0]?.name}
                ...
              </div>
            </CssTooltip>
          </div>
        ),
      },

      {
        header: "",
        accessorKey: "action",
        enableSorting: false,
        enableColumnFilter: false,
        Cell: ({ row }) => (
          <>
            <div className="flex gap-3">
              <div className="flex gap-8 mr-5">
                <EditButton
                  label={"Edit"}
                  icon={EditingIcon}
                  onClick={() => {
                    if (permissions?.update === 0) {
                      setShowAlertConfirmation(true);
                      setMessage("Update permission not allowed");
                    } else {
                      handleEdit(row.original);
                    }
                  }}
                />

                <EditButton
                  label={"Delete"}
                  icon={TrashIcon}
                  onClick={() => {
                    if (permissions?.delete === 0) {
                      setShowAlertConfirmation(true);
                      setMessage("Delete permission not allowed");
                    } else {
                      handleDelete(row.original);
                    }
                  }}
                />
              </div>
            </div>
          </>
        ),
      },
    ],
    []
  );

  const handleLimitChange = (e) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
  };

  return (
    <>
      <div className="bg-bgPrimary my-5">
        <div className="mt-10">
          <Title title={"List of My Groups"} />
        </div>
        <div className="border border-listBorder bg-white p-3 mt-10">
          {!groupLoading &&
          groupList?.data?.totalCount === 0 &&
          searchInput === "" ? (
            <>
              <div className="mx-3 mt-5">
                <div className="border border-outerBorder mb-5">
                  <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                    {"Oops ! no records to display."}
                  </div>
                  <div className="flex justify-center text-tabColor text-base font-semibold mt-5 ">{`You have not added any groups yet.`}</div>
                  <div className="flex justify-center my-12">
                    <img
                      src={bgImage}
                      style={{
                        height: "10%",
                        width: "10%",
                        objectFit: "cover",
                      }}
                      alt="bg"
                    />
                  </div>
                  <div className="flex justify-center mb-5">
                    <Button
                      buttonClassName="text-xs w-36 text-white h-10 "
                      label={"+Add New Group"}
                      onClick={() => {
                        setEdit(false);
                        if (permissions?.create === 1) {
                          openGroupDialog();
                        } else {
                          setShowAlertConfirmation(true);
                          setMessage("Create permission not allowed");
                        }
                      }}
                    ></Button>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="mx-3">
                <InputLabel label={" Group Name"} />
                <div class="w-full flex mt-3">
                  <div className=" w-1/2 ">
                    <input
                      type="text"
                      style={{
                        border: `1px solid ${theme.borderColor.outerBorder}`,
                        paddingLeft: "2rem",
                      }}
                      className="w-full px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                      placeholder="Search group"
                      value={searchInput}
                      onChange={(e) => {
                        setSearchInput(e.target.value);
                        setCurrentPage(1);
                      }}
                    />
                    <div
                      class="top-0 right-0  mr-4"
                      style={{ marginLeft: "0.25rem", marginTop: "-1.7rem" }}
                    >
                      <SearchIcon className="w-5 h-5" />
                    </div>
                  </div>
                  <div className="flex-grow flex justify-end items-center gap-3">
                    <Button
                      buttonClassName="text-xs w-36 text-white h-10 "
                      label={"+Add New Group"}
                      onClick={() => {
                        setEdit(false);
                        if (permissions?.create === 1) {
                          openGroupDialog();
                        } else {
                          setShowAlertConfirmation(true);
                          setMessage("Create permission not allowed");
                        }
                      }}
                    ></Button>
                  </div>
                </div>
                <div className="mt-5">
                  <Table
                    columns={columns}
                    data={Array.isArray(modified) ? modified : []}
                    isLoading={isFetching}
                  />
                </div>
                {users?.data && users.data.totalCount !== undefined && (
                  <div
                    // style={{
                    //   display: "flex",
                    //   justifyContent: "center",
                    //   marginTop: "20px",
                    // }}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      margin: "20px 0px 20px 0px",
                      width: "100%",
                    }}
                  >
                    <div className="flex ">
                      <div>
                        {" "}
                        <ResultPerPageComponent
                          countPerPage={resultPerPage}
                          limit={limitPerPage}
                          handleLimitChange={handleLimitChange}
                        />
                      </div>

                      <div
                        style={{
                          display: "flex",
                          fontSize: "14px",
                          padding: "10px 0px 0px 10px",
                          color: theme.textColor.titleColor,
                        }}
                      >
                        {(currentPage - 1) * limitPerPage + 1} -{" "}
                        {Math.min(
                          limitPerPage * currentPage,
                          users?.data?.totalCount
                        )}{" "}
                        of {users?.data?.totalCount} rows
                      </div>
                    </div>
                    <Pagination
                      className="pagination-bar"
                      currentPage={currentPage}
                      totalCount={users?.data?.totalCount}
                      pageSize={limitPerPage}
                      onPageChange={(page) => {
                        handlePageChange(page);
                      }}
                    />
                  </div>
                )}
              </div>
            </>
          )}
        </div>
        <AddGroup
          openGroupDialog={openDialog}
          closeGroupDialog={closeGroupDialog}
          edit={edit}
          selectedDetails={selectedDetails}
          refreshData={refetch}
        />
        <DeleteDialog
          show={deleteDialog}
          onHide={() => setDeleteDialog(false)}
          onConfirm={() => {
            setIsLoading(true);
            alertServices
              .deleteGroup(id)
              .then((res) => {
                setDeleteDialog(false);
                setSearchStr("");
                refetch();
                queryClient.invalidateQueries("groupList");
                setCurrentPage(1);

                setSuccessDialog(true);

                setMessage("Group deleted successfully");
              })
              .catch((error) => {
                setDeleteDialog(false);
                setErrorDialog(true);
                setMessage("Something went wrong");
                // console.error("Error deleting card:", error);
              })
              .finally(() => {
                setIsLoading(false);
              });
          }}
          title={"Are you sure to delete the group?"}
          isLoading={isLoading}
        />
        <SuccessDialog
          show={suceessDialog}
          onHide={() => setSuccessDialog(false)}
          message={message}
        />
        <ErrorDialog
          show={errorDialog}
          onHide={() => setErrorDialog(false)}
          message={message}
        />
        <InfoModal
          //icon={<Alert className="w-10 h-10 " />}
          show={showAlertConfirmation}
          onHide={() => {
            setShowAlertConfirmation(false);
          }}
          message={message}
        />
      </div>
    </>
  );
}

export default Groups;
