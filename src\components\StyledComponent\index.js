import styled from "@emotion/styled";
import TextField from "@mui/material/TextField";
import Tooltip, { tooltipClasses } from "@mui/material/Tooltip";
import theme from "../../tailwind-theme";

const CssTextField = styled(TextField)((props) => ({
  "& label.Mui-focused": {
    color: "#2D2D2D",
  },
  "& .MuiOutlinedInput-root": {
    height: "40px",
    fontSize: "12px",
    fontWeight: "500",
    backgroundColor: "white",

    "& fieldset": {
      border: `1px solid ${theme.borderColor.inputTextBoxBorder}`,
      borderRadius: "5px",
    },

    "&:hover fieldset": {
      border: `1px solid ${theme.borderColor.inputTextBoxBorder}`,
    },
    "& input:disabled+fieldset": "transparent",

    "& input:disabled": {
      opacity: "40%",
    },

    "& .MuiOutlinedInput-input.Mui-disabled": {
      WebkitTextFillColor: "rgba(0, 0, 0, 1)",
    },

    "&.Mui-focused fieldset": {
      border: `1px solid ${theme.borderColor.inputTextBoxBorder}`,
    },
  },
}));

const CssTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({}) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: `${theme.backgroundColor.toolTipBackground}`,
    color: `${theme.textColor.tooltipTextColor}`,
    fontSize: 12,
    // border: "2px solid #7070708C",
    padding: "8px 12px",
    //boxShadow: "0px 3px 6px #00000029",
    borderRadius: 5,
  },
  [`& .${tooltipClasses.arrow}`]: {
    //color: theme.palette.common.white,
    "&::before": {
      backgroundColor: `${theme.backgroundColor.toolTipBackground}`,
      //boxShadow: "3px 3px 3px 3px #00000029",
      //border: "0.5px solid #7070708C",
    },
  },
}));

export { CssTextField, CssTooltip };
