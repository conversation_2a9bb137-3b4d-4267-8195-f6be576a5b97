import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from "recharts";
import { ExpandMore, ExpandLess } from "@mui/icons-material";

function generateColor(index) {
  const hue = (index * 137.508) % 360;
  return `hsl(${hue}, 70%, 50%)`;
}

const BarChartComponent = ({
  chartData,
  isChart,
  dimension,
  isWidth,
  isExpand,
  errMsg,
}) => {
  const [isLegendExpanded, setIsLegendExpanded] = useState(true);

  const barColors = React.useMemo(() => {
    const initialColors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];
    const yAxisLength = chartData?.y_axis?.[0]
      ? Object.keys(chartData.y_axis[0]).length
      : 0;
    const additionalColors = Array.from(
      { length: Math.max(0, yAxisLength - 4) },
      (_, i) => generateColor(i + 4)
    );
    return initialColors.concat(additionalColors);
  }, [chartData]);

  const transformedData =
    chartData?.x_axis?.length > 0 && chartData?.y_axis
      ? chartData.x_axis
          .map((item, index) => {
            if (typeof item === "object" && chartData.y_axis[index]) {
              const xAxisKey = Object.keys(item)[0];
              return {
                name: item[xAxisKey],
                ...chartData.y_axis[index],
              };
            }
            return null;
          })
          .filter(Boolean)
      : [];

  const seriesKeys =
    chartData?.y_axis?.length > 0 && chartData?.y_axis[0]
      ? Object.keys(chartData.y_axis[0])
      : [];

  const formatYAxis = (tick) => {
    if (tick >= 1000000) return `${tick / 1000000}Mn`;
    if (tick >= 1000) return `${tick / 1000}k`;
    return tick;
  };

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload?.length) {
      const shouldLimit = isChart && !isExpand;
      const visiblePayload = shouldLimit ? payload.slice(0, 4) : payload;

      return (
        <div
          style={{
            backgroundColor: "white",
            padding: "7px",
            border: "1px solid #ccc",
            fontSize: "12px",
            maxWidth: "260px",
            pointerEvents: "auto",
            fontWeight: "bold",
          }}
        >
          <div style={{ fontWeight: 600, marginBottom: "6px" }}>
            {payload[0]?.payload?.name}
          </div>

          <div>
            {visiblePayload.map((entry, index) => (
              <div
                key={index}
                style={{ color: entry.color, marginBottom: "4px" }}
              >
                {entry.name}:{" "}
                {entry.value.toLocaleString("en-US", {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 2,
                })}
              </div>
            ))}
          </div>

          {shouldLimit && payload.length > 4 && (
            <div
              style={{
                marginTop: "6px",
                fontStyle: "italic",
                color: "#555",
                fontSize: "11px",
                textAlign: "center",
              }}
            >
              Click the expand icon to see more tooltip data.
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  const truncateXAxisName = (name) =>
    name.length > 10 ? `${name.substring(0, 10)}...` : name;

  const CollapsibleLegend = () => {
    const visibleCount = 4;
    const hasMore = seriesKeys.length > visibleCount;
    const itemsToDisplay =
      isLegendExpanded || !hasMore
        ? seriesKeys
        : seriesKeys.slice(0, visibleCount);

    return (
      <div className="w-full bg-gray-50 rounded-lg p-4">
        {hasMore && (
          <div className="flex items-center justify-end">
            <button
              onClick={() => setIsLegendExpanded((prev) => !prev)}
              className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <span className="text-sm">
                {isLegendExpanded
                  ? "Show Less"
                  : `Show All (${seriesKeys.length})`}
              </span>
              {isLegendExpanded ? (
                <ExpandLess sx={{ fontSize: 16 }} />
              ) : (
                <ExpandMore sx={{ fontSize: 16 }} />
              )}
            </button>
          </div>
        )}

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {itemsToDisplay.map((key, index) => (
            <div
              key={key}
              className="flex items-center space-x-2  rounded hover:bg-white"
            >
              <div
                className="w-3 h-3 rounded"
                style={{ backgroundColor: barColors[index] }}
              />
              <span className="text-sm text-gray-700">{key}</span>
            </div>
          ))}
        </div>

        {hasMore && !isLegendExpanded && (
          <div className="mt-2 text-center text-sm text-gray-500">
            +{seriesKeys.length - visibleCount} more series
          </div>
        )}
      </div>
    );
  };

  if (!transformedData || transformedData.length === 0) {
    return (
      <div
        style={{
          width: "100%",
          height: 240,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          fontSize: 16,
          //  fontWeight: 600,
          // color: "#999999",
        }}
      >
        {errMsg ? errMsg : "No records to display"}
      </div>
    );
  }

  return (
    <div>
      <ResponsiveContainer
        width={dimension ? dimension.w * 120 : "100%"}
        height={dimension ? dimension.h * 70 : 400}
      >
        <BarChart
          data={transformedData}
          margin={{
            top: dimension ? 20 : 35,
            right: dimension ? 10 : 30,
            left: isWidth ? 0 : dimension ? 15 : 5,
            bottom: isWidth ? 70 : dimension ? 40 : 60,
          }}
          barGap={0}
          barCategoryGap={0}
        >
          <XAxis
            dataKey="name"
            tick={{ fontSize: dimension ? 10 : 12, fontWeight: 600 }}
            angle={dimension ? -35 : -45}
            textAnchor="end"
            tickFormatter={truncateXAxisName}
          />
          <YAxis
            tick={{ fontSize: dimension ? 10 : 12, fontWeight: 600 }}
            tickCount={10}
            tickFormatter={formatYAxis}
          />
          <Tooltip content={<CustomTooltip />} />

          {seriesKeys.map((key, index) => (
            <Bar
              key={key}
              dataKey={key}
              fill={barColors[index] || generateColor(index)}
              barSize={15}
            />
          ))}
        </BarChart>
      </ResponsiveContainer>
      {!isChart || isWidth ? <CollapsibleLegend /> : null}
    </div>
  );
};

export default BarChartComponent;
