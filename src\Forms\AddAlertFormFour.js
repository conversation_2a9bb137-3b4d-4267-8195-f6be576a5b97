import { useContext, useState, useEffect, useRef } from "react";
import { Formik, Form, ErrorMessage, Field } from "formik";
import Select from "../components/FormsUI/Select";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import BackButton from "../components/Button/Button";
import Button from "../components/Button/OutlinedButton";
import { useMutation, useQuery } from "react-query";
import { alertServices } from "../services/alert-api";
import { useNavigate } from "react-router-dom";
import ChipInput from "../components/Chip/ChipInput";
import GroupEmailComponent from "../common/groupEmail";
import { RadioGroup, Radio } from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import { DataContext } from "../context/DataContext";
import "react-toastify/dist/ReactToastify.css";
import SuccessDialog from "../popups/SuccessDialog";
import ErrorDialog from "../popups/ErrorDialog";
import BulkUpload from "../common/BulkUpload";
import * as Yup from "yup";
import CustomDropDown from "../components/Dropdown/CustomeDropdown";

const AddAlertFormFour = ({ isEdit, alertId, editDetail, isCopy }) => {
  const { setFormData, formData, handlePrevClick, handlePrevStep } =
    useContext(multiStepFormContext);
  const [groups, setGroups] = useState([]);
  const [bGroups, setBGroups] = useState([]);
  const [groupMembers, setGroupMembers] = useState([]);
  const [groupType, setGroupType] = useState("Individual");
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [errorDialog, setErrorDialog] = useState(false);
  const [newChipState, setNewChipState] = useState(
    editDetail?.contact_point?.groupsEmails?.filter((group) =>
      group.includes("@")
    ) || []
  );
  const [listData, setListData] = useState([]);

  const setFieldValueRef = useRef();

  const navigate = useNavigate();
  const { mutate: createAlertAPI, isLoading: creationLoading } = useMutation(
    alertServices.createAlert
  );
  const { mutate: updateAlertAPI, isLoading: upadteLoading } = useMutation(
    alertServices.updateAlert
  );
  const { setAlertDetails } = useContext(DataContext);
  const { data: groupDetails } = useQuery(
    ["groupList", 1, 1000],
    alertServices.getAll,
    {
      onSuccess: ({ data }) => {
        let groupDetails = data?.data?.map((item) => ({
          label: item.name,
          value: item.name,
          members: item.members,
          subGroups: item.subGroups,
        }));

        let groupList = data?.data?.map((item) => ({
          label: item.name,
          value: item.name,
        }));

        groupList = [
          { label: "Select All", value: "Select All" },
          ...groupDetails,
        ];
        setGroups(groupDetails);
        setListData(groupList);
      },
    }
  );

  useEffect(() => {
    if ((isEdit && editDetail) || isCopy) {
      const groupsEmails = editDetail.contact_point?.groupsEmails || [];
      const individuals = editDetail.contact_point?.individualEmails || [];

      const groupNames = groupsEmails.filter((group) => !group.includes("@"));
      const individualEmails = [
        ...groupsEmails.filter((group) => group.includes("@")),
        ...individuals,
      ];

      setNewChipState(individualEmails);

      if (individualEmails.length > 0 && groupNames.length === 0) {
        setGroupType("Individual");
      } else if (groupNames.length > 0) {
        setGroupType("Group");
      }
    }
  }, [isEdit, editDetail, isCopy]);

  useEffect(() => {
    if (bGroups && bGroups.length > 0) {
      const bGroupValues = bGroups.map((group) => group.value);

      if (setFieldValueRef.current) {
        setFieldValueRef.current("groupName", (prev = []) => {
          const merged = [...prev, ...bGroupValues];
          return [...new Set(merged)]; // remove duplicates
        });
      }
    }
  }, [bGroups, isEdit]);

  const handleChipRemove = (index, formikBag) => {
    const newChips = [...formikBag.values.chips];
    newChips.splice(index, 1);
    formikBag.setFieldValue("chips", newChips);
  };

  const handleChipAdd = (chip, formikBag) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(chip)) {
      formikBag.setFieldError("chips", "Please enter a valid Email ID");
      return;
    }
    formikBag.setFieldValue("chips", [...formikBag.values.chips, chip]);
  };

  const handleSubmit = (values, newData) => {
    let reqData = {};

    const specialKeys = ["Customer", "Supplier", "Destination"];

    Object.keys(formData).forEach((key) => {
      const value = formData[key];
      if (
        key === "groupName" ||
        key === "newChips" ||
        key === "filterType" ||
        key === "timeInterval" ||
        key === "timePeriod" ||
        key === "chips" ||
        key === "conditions" ||
        key === "minimumSubmissionCount" ||
        key === "groupType"
      )
        return;

      if (specialKeys.includes(key)) {
        if (key === "Customer") {
          reqData.customerAndBind = value || [];
        } else if (key === "Supplier") reqData.suppliers = value || [];
        else if (key === "Destination") reqData.destinations = value || [];
      } else {
        if (value !== undefined && value !== null) {
          reqData[key] = value;
        }
      }
    });

    // Remove "Select All" from groupName before sending
    const filteredGroups = (values?.groupName || []).filter(
      (group) => group !== "Select All"
    );

    if (formData?.customerListData) {
      delete reqData.customerListData;
    }
    if (values?.chips?.length > 0) reqData.individuals = values?.chips;
    if (filteredGroups.length > 0 || newChipState)
      reqData.groups = [...filteredGroups, ...(newChipState || [])];

    if (formData?.timePeriod) reqData.timerange = formData?.timePeriod;
    if (formData?.minimumSubmissionCount)
      reqData.minSubmissionCount = formData?.minimumSubmissionCount;

    if (formData?.timeInterval)
      reqData.evaluationTimeframe = formData?.timeInterval;
    if (formData?.timeInterval) reqData.runEvery = formData?.timeInterval;

    if (Array.isArray(formData.conditions)) {
      const mappedConditions = formData.conditions.map((condition) => ({
        field: condition.type1,
        condition: condition.type2,
        value: condition.type3,
        operator: condition.type4,
      }));
      reqData.filters = mappedConditions;
    }

    if (isEdit) {
      updateAlertAPI(
        {
          alertId,
          reqData,
        },
        {
          onSuccess: () => {
            setSuccessDialog(true);
            setMessage("Alert updated successfully");
          },
          onError: (error) => {
            setMessage(error?.response?.data?.message);
            setErrorDialog(true);
          },
        }
      );
    } else {
      createAlertAPI(
        {
          reqData,
        },
        {
          onSuccess: () => {
            setSuccessDialog(true);
            setMessage("Alert created successfully");
          },
          onError: (error) => {
            setMessage(error?.response?.data?.message);
            setErrorDialog(true);
          },
        }
      );
    }
  };

  const initialValues = {
    groupType: formData?.groupType || groupType,
    groupName:
      formData?.groupName?.map((g) => ({ label: g, value: g })) ||
      editDetail?.contact_point?.groupsEmails
        ?.filter((g) => !g.includes("@"))
        ?.map((g) => ({ label: g, value: g })) ||
      [],
    chips: formData?.chips || editDetail?.contact_point?.individualEmails || [],
    newChips:
      formData?.newChips ||
      editDetail?.contact_point?.groupsEmails?.filter((g) => g.includes("@")) ||
      [],
  };

  const validationSchema = Yup.object().shape({
    chips: Yup.array().when("groupType", {
      is: "Individual",
      then: (schema) =>
        schema
          .min(1, "Please enter at least one Email ID")
          .required("Please enter at least one Email ID")
          .of(
            Yup.string()
              .email("Please enter a valid Email ID")
              .required("Please enter a valid Email ID")
          )
          .test(
            "no-duplicates",
            "Duplicate Email IDs are not allowed",
            (value) => {
              if (!Array.isArray(value)) return true;
              const unique = new Set(value.map((v) => v?.toLowerCase().trim()));
              return unique.size === value.length;
            }
          ),
      otherwise: (schema) => schema.notRequired(),
    }),
    groupName: Yup.array().when("groupType", {
      is: "Group",
      then: (schema) =>
        schema
          .min(1, "Please select at least one group")
          .required("Please select at least one group"),
      otherwise: (schema) => schema.notRequired(),
    }),
  });
  return (
    <div>
      <Formik
        enableReinitialize
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          const keys = Object.keys(values);
          setAlertDetails((prevDetails) => {
            const newDetails = [...prevDetails];
            newDetails[3] = keys; // Store keys for step 4
            return newDetails;
          });
          // handleNextClickStep();
          let newData = Object.assign(formData, values);
          handleSubmit(values, newData);

          setFormData(newData);
        }}
      >
        {({ setFieldValue, resetForm, errors, touched, values }) => {
          setFieldValueRef.current = setFieldValue;

          return (
            <Form
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  // Allow Enter key to submit only if the focused element is the submit button
                  if (e.target.type !== "submit") {
                    e.preventDefault();
                  }
                }
              }}
            >
              <div className="flex flex-col   ">
                <div className="flex flex-row mx-28 justify-between">
                  <div className="flex flex-col">
                    <InputLabel
                      label={"Send Email"}
                      isMandatory={true}
                      labelClassName={"mb-3"}
                    />
                    <div className="px-1">
                      <RadioGroup
                        name="groupType"
                        row
                        value={groupType}
                        onChange={(e) => {
                          setGroupType(e.target.value);
                          if (e.target.value === "Individual") {
                            setFieldValue("groupName", []);
                            setFieldValue("newChips", []);
                            setNewChipState([]);
                          } else if (e.target.value === "Group") {
                            setFieldValue("chips", []);
                          }
                        }}
                      >
                        <FormControlLabel
                          value="Individual"
                          //  disabled={edit}
                          control={
                            <Radio
                              sx={{
                                "& .MuiSvgIcon-root": {
                                  fontSize: "16px",
                                },
                                "&.Mui-checked .MuiSvgIcon-root": {
                                  color: "black",
                                },
                              }}
                            />
                          }
                          label={
                            <span className="text-headingColor text-sm">
                              Individuals
                            </span>
                          }
                        />
                        <FormControlLabel
                          value="Group"
                          // disabled={edit}
                          control={
                            <Radio
                              sx={{
                                "& .MuiSvgIcon-root": {
                                  fontSize: "16px",
                                },
                                "&.Mui-checked .MuiSvgIcon-root": {
                                  color: "black",
                                },
                              }}
                            />
                          }
                          label={
                            <span className="text-headingColor text-sm">
                              Groups
                            </span>
                          }
                        />
                      </RadioGroup>
                    </div>
                  </div>
                  {groupType === "Group" ? (
                    <div className="w-1/2 md:w-[550px]">
                      <InputLabel
                        label={"Select one option"}
                        isMandatory={true}
                        labelClassName={"mb-3"}
                      />
                      <BulkUpload
                        setGroups={setBGroups}
                        setListData={setListData}
                      />{" "}
                      <div className="text-center mt-2 mb-2">or</div>
                      <CustomDropDown
                        btnWidth="w-full"
                        data={listData}
                        btnName={"Select Groups"}
                        onSelectionChange={(selectedOptions) => {
                          setFieldValue("groupName", selectedOptions);
                        }}
                        defaultSelectedData={
                          editDetail?.contact_point?.groupsEmails
                        }
                      />
                      <p className="text-errorColor text-xs my-1">
                        <ErrorMessage name="groupName" />
                      </p>
                    </div>
                  ) : (
                    <div className="w-[550px] ">
                      <div className="flex mb-2">
                        <InputLabel label={"Individuals"} isMandatory={true} />
                        <InputLabel
                          label={"(Press enter to add Email ID)"}
                          labelClassName={"text-xs ml-1 mt-0.5"}
                        />
                      </div>

                      <Field
                        id="chips"
                        name="chips"
                        render={(fieldProps) => (
                          <ChipInput
                            chips={
                              fieldProps?.field?.value.length > 0
                                ? fieldProps?.field?.value
                                : []
                            }
                            onChipAdd={(chip) =>
                              handleChipAdd(chip, fieldProps.form)
                            }
                            onChipRemove={(index) =>
                              handleChipRemove(index, fieldProps.form)
                            }
                            isEdit={true}
                            placeholder={"Enter Email ID"}
                          />
                        )}
                      />
                      <p className="text-errorColor text-xs my-1">
                        <ErrorMessage name="chips" />
                      </p>
                    </div>
                  )}{" "}
                </div>
                {(groupType === "Group" && values?.groupName?.length > 0) ||
                (newChipState &&
                  newChipState?.length > 0 &&
                  groupType !== "Individual") ? (
                  <GroupEmailComponent
                    groups={groups}
                    selectedName={values?.groupName}
                    setFieldValue={setFieldValue}
                    setGroupMembers={setGroupMembers}
                    groupMembers={groupMembers}
                    newChipState={newChipState}
                    setNewChipState={setNewChipState}
                  />
                ) : null}
              </div>
              <div className="flex-grow flex justify-end items-center mx-20 mt-20 mb-20 gap-4">
                <BackButton
                  label={"Back"}
                  buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded"
                  onClick={() => {
                    handlePrevClick();
                    handlePrevStep();
                  }}
                ></BackButton>
                <Button
                  // block="true"
                  type="submit"
                  label="Save"
                  // value="submit"
                  buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded "
                  //loading={creationLoading || upadteLoading}
                  disabled={creationLoading || upadteLoading}
                  // onClick={handleNextClickStep}
                ></Button>
              </div>
            </Form>
          );
        }}
      </Formik>
      <SuccessDialog
        show={suceessDialog}
        onHide={() => {
          setSuccessDialog(false);
          navigate("/app/alerts");
        }}
        message={message}
      />
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
    </div>
  );
};
export default AddAlertFormFour;
