import { useState, useEffect, useMemo, useContext } from "react";
import { userService } from "../services/user.service";
import Table from "../components/table/ReportTable";
import Pagination from "../components/Pagination/Pagination";
import CustomSwitch from "../components/Toggle-switch/switch";
import { Alert, EditingIcon, SearchIcon, TrashIcon } from "../icons";
import { AuthContext } from "../context/AuthContext";
import InfoModal from "../components/modals/InfoModal";
import SuccessDialog from "../popups/SuccessDialog";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import CreateorEditModal from "../UserManagement/CreateorEditModal";
import { DataContext } from "../context/DataContext";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { useNavigate } from "react-router-dom";
import Button from "../components/Button/Button";
import AddButton from "../components/Button/OutlinedButton";
import Title from "../Title";
import EditButton from "../components/Button/EditButton";
import DeleteDialog from "../popups/DeleteDialog";
import Avatar from "react-avatar";
import bgImage from "../assets/img/Records.png";
import theme from "../tailwind-theme";
import { CssTooltip } from "../components/StyledComponent";

function Users() {
  const navigate = useNavigate();
  const [users, setUsers] = useState(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(0);
  const [showCreateorEditConfirmation, setShowCreateorEditConfirmation] =
    useState(0);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [userId, setUserId] = useState("");
  const [successDialog, setSuccessDialog] = useState(false);
  const { roles } = useContext(AuthContext);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [reload, setReload] = useState(false);
  const [searchStr, setSearchStr] = useState("");
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [deleteLoading, setIsDeleteLoading] = useState(false);
  const [isEditUser, setIsEditUser] = useState(false);
  const { resultPerPage } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(10);
  const [refetchList, setRefetch] = useState(false);
  const [imgErr, setImgErr] = useState("");
  const [editClicked, setEditClicked] = useState(false);

  const [editUser, setEditUser] = useState({
    name: "",
    email: "",
    role: "",
    password: "",
    confirmPassword: "",
    image: "",
    status: "",
  });

  const permissions = useMemo(() => {
    return roles?.resources?.filter((res) => res.name === "User Management")[0]
      .permissions;
  }, [roles]);

  const handleKeyUp = (event) => {
    // if(event.target.value.length >= 2) {
    setSearchStr(event.target.value);
    setReload(true);
    setCurrentPage(1);
    // }
  };

  const handleAdd = () => {
    if (permissions.create === 0) {
      handleSetMessage("Add permission not allowed");
      handleOpenInfoModal();
    } else {
      setShowCreateorEditConfirmation(1);
      setIsEditUser(false);
    }
  };
  const handleOpenInfoModal = () => {
    setShowAlertConfirmation(true);
  };
  const handleCloseInfoModal = () => {
    setShowAlertConfirmation(false);
  };
  const handleSetMessage = (message) => {
    setMessage(message);
  };
  const handleEdit = (user) => {
    if (permissions?.update === 0) {
      handleSetMessage("Update permission not allowed");
      handleOpenInfoModal();
    } else {
      setShowCreateorEditConfirmation(1);
      setEditUser(user);
      setIsEditUser(true);
      setEditClicked(true);
      // console.log("editButton");
    }
  };
  const handleDelete = (id) => {
    if (permissions.delete === 0) {
      handleSetMessage("Delete permission not allowed");
      handleOpenInfoModal();
    } else {
      setShowDeleteConfirmation(1);
      handleSetMessage("User is successfully deleted");
      setUserId(id);
      setIsEditUser(false);
    }
  };

  // useEffect(() => {
  //   const refreshData = (page = currentPage) => {
  //     userService.getAll("", page).then((data) => {
  //       setUsers(data.data.data);
  //       setTotalResults(data.data.totalCount);
  //     });
  //   };
  //   if (fetchRef.current !== currentPage) {
  //     refreshData();
  //     fetchRef.current = currentPage;
  //   }
  // }, [currentPage]);

  useEffect(() => {
    if (reload.searchStr !== "") {
      setIsLoading(true);
      // setCurrentPage(1);
      setIsLoading(true);
      userService
        .getAll(searchStr, currentPage, limitPerPage)
        .then(({ data }) => {
          let userList = data.data.map((usr) => ({
            ...usr,
            lastLoggedin: usr?.lastLoggedin ? usr?.lastLoggedin : "-",
          }));
          setIsLoading(false);

          setUsers(userList);
          //setCurrentPage(1);
          setTotalResults(data.totalCount);
        })
        .catch((error) => {
          setIsLoading(false);

          toast.error(
            error?.response?.data?.message
              ? error.response.data.message
              : error.message
          );

          // console.error("Error fetching data:", error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [reload, searchStr, currentPage, limitPerPage, refetchList]);

  function deleteUser(id) {
    const filteredUsers = users.map((x) => {
      if (x.id === id) {
        x.isDeleting = true;
      }
      return x;
    });
    setUsers(filteredUsers);
    setIsDeleteLoading(true);
    userService
      .delete(id)
      .then(() => {
        setRefetch((oldState) => !oldState);
        setCurrentPage(1);

        setShowDeleteConfirmation(0);
        setSuccessDialog(true);
      })
      .catch((error) => {
        toast(
          error.response.data.message ? error.response.data.message : error
        );
        setRefetch((oldState) => !oldState);
        setShowDeleteConfirmation(0);
        setIsDeleteLoading(false);
      })
      .finally(() => {
        setIsDeleteLoading(false);
      });
  }

  function handlePageChange(page) {
    setCurrentPage(page);
  }

  // const formatDateTime = (dateTimeString) => {
  //   try {
  //     const date = new Date(dateTimeString);

  //     const options = {
  //       day: "2-digit",
  //       month: "short",
  //       year: "numeric",
  //       hour: "2-digit",
  //       minute: "2-digit",
  //       second: "2-digit",
  //     };

  //     const formattedTime = date
  //       .toLocaleTimeString("en-GB", options)
  //       .replace(",", "");

  //     const formattedDateTime = formattedTime;

  //     return formattedDateTime;
  //   } catch (error) {
  //     return "-";
  //   }
  // };

  const handleSwitchChange = (status, id) => {
    //console.log("e,id", status, id);
    userService
      .updateSwitch(id, status)
      .then((res) => {
        //console.log(res);
        if (res?.data?.id) {
          setUsers((state) => {
            const newState = state.map((s) => {
              if (s.id === res?.data?.id) {
                //console.log("s.id", s.id);
                s.isUserActive = res.data.isUserActive;
              }
              return s;
            });
            return newState;
          });
        }
      })
      .catch((error) => {
        toast.error(
          error.response.data.message ? error.response.data.message : error
        );

        // setStatus(error.response.data.message);
      });
    //  setSwitchChecked(true);
  };

  const handleSuccessDialog = () => {
    setSuccessDialog(true);
  };

  const columns = useMemo(
    () => [
      {
        header: "User name",
        accessorKey: "name",
        size: 240,
        Cell: ({ row }) => (
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <div
              style={{
                width: "35px",
                height: "45px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginRight: "3px",
              }}
            >
              {row?.original?.profileImage ? (
                <img
                  src={
                    row?.original?.profileImage
                      ? row?.original?.profileImage
                      : ""
                  }
                  style={{
                    borderRadius: "24px",
                    width: "24px",
                    height: "24px",
                  }}
                  alt=""
                />
              ) : (
                <Avatar
                  size="25"
                  round={true}
                  color={theme.backgroundColor.bgSecondary}
                  name={row?.original?.name}
                />
              )}
            </div>
            <div
              style={{
                width: "90%",
                // float: "left",
                display: "flex",
                flexDirection: "column",
              }}
            >
              {row?.original?.name ? row?.original?.name : "-"}{" "}
              <CssTooltip title={row?.original?.email} placement="top" arrow>
                <p style={{ color: "#928F8F", margin: 0 }}>
                  {row?.original?.email
                    ? row.original.email.length > 27
                      ? `${row.original.email.slice(0, 27)}...`
                      : row.original.email
                    : "-"}
                </p>
              </CssTooltip>
            </div>
          </div>
        ),
      },
      {
        header: "Status",
        accessorKey: "isUserActive",
        size: 80,
        enableColumnFilter: false,
        enableSorting: false,
        Cell: ({ row }) => (
          <div
            style={{
              paddingRight: "2rem",
            }}
          >
            <CustomSwitch
              diameter={15}
              width={36}
              checked={row?.original?.isUserActive}
              onChange={(e) => handleSwitchChange(e, row?.original?.id)}
            />{" "}
          </div>
        ),
      },
      {
        header: "Role",
        accessorKey: "role.name",
        size: 140,
      },
      {
        header: "User Type",
        accessorKey: "isLdapUser",
        size: 140,

        Cell: ({ row }) => (
          <>{row.original.isLdapUser ? "LDAP User" : "Non-LDAP User"}</>
        ),
      },
      {
        header: "Last Logged In",
        accessorKey: "lastLoggedin",
        size: 140,
      },

      {
        header: "",
        accessorKey: "action",
        enableSorting: false,
        enableColumnFilter: false,
        size: 180,
        Cell: ({ row }) => (
          <>
            <div className="flex gap-8 mr-4">
              <EditButton
                label={"Edit"}
                icon={EditingIcon}
                onClick={() => {
                  handleEdit(row?.original);
                }}
              />

              <EditButton
                label={"Delete"}
                icon={TrashIcon}
                onClick={() => {
                  handleDelete(row?.original?.id);
                }}
              />
            </div>
          </>
        ),
      },
    ],
    []
  );

  const handleLimitChange = (e) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
  };

  const onHideCreateorEditModal = () => {
    setShowCreateorEditConfirmation(0);
    setImgErr("");
  };

  return (
    <>
      <div className="bg-bgPrimary my-5 ">
        <div className="mt-10">
          <Title title={"User List"} />
        </div>
        <div className="border border-listBorder bg-white pb-5 mt-10">
          {!isLoading && totalResults === 0 && searchStr === "" ? (
            <>
              <div className=" mt-5 mx-3">
                {/* mx-3 */}
                <div className="border border-outerBorder mb-5">
                  <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                    {"Oops ! no records to display."}
                  </div>
                  <div className="flex justify-center text-tabColor text-base font-semibold mt-5 ">{`You have not added any users yet.`}</div>
                  <div className="flex justify-center my-12">
                    <img
                      src={bgImage}
                      style={{
                        height: "10%",
                        width: "10%",
                        objectFit: "cover",
                      }}
                      alt="bg"
                    />
                  </div>
                  <div className="flex justify-center mb-5">
                    <AddButton
                      label={"+Add New User"}
                      buttonClassName="w-full md:w-[144px] text-xs  rounded "
                      onClick={() => handleAdd()}
                    ></AddButton>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              <div>
                {/* //className="mx-3" */}
                {/* <label className="text-sm font-medium mb-2 mt-3">Card Name</label> */}
                <div class="w-full flex mt-3 p-4">
                  <div className=" w-1/2 ">
                    <input
                      type="text"
                      style={{
                        border: `1px solid ${theme.borderColor.outerBorder}`,
                        paddingLeft: "2rem",
                      }}
                      className="w-full px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                      placeholder="Search user"
                      value={searchStr}
                      onChange={handleKeyUp}
                    />

                    <div
                      class="top-0 right-0  mr-4"
                      style={{ marginLeft: "0.25rem", marginTop: "-1.7rem" }}
                    >
                      <SearchIcon className="w-5 h-5" />
                    </div>
                  </div>
                  <div className="flex-grow flex justify-end items-center gap-3">
                    <Button
                      label={"User Analysis"}
                      buttonClassName="w-full md:w-[144px]  border-errorBorder text-xs rounded text-errorColor"
                      onClick={() => {
                        navigate("/app/usermanagement/useranalysis", {
                          state: { user: users },
                        });
                      }}
                    ></Button>
                    <AddButton
                      label={"+Add New User"}
                      buttonClassName="w-full md:w-[144px] text-xs  rounded "
                      onClick={() => handleAdd()}
                    ></AddButton>
                  </div>
                </div>
                <div className="mt-3 mx-4">
                  <Table
                    columns={columns}
                    data={Array.isArray(users) ? users : []}
                    isLoading={isLoading}
                    // autoResetPage={false}
                  />
                </div>
                {totalResults > 0 ? (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      // justifyContent: "flex-start",
                      marginTop: "20px",
                      padding: "16px",
                    }}
                  >
                    <div className="flex ">
                      <div>
                        <ResultPerPageComponent
                          countPerPage={resultPerPage}
                          limit={limitPerPage}
                          handleLimitChange={handleLimitChange}
                        />
                      </div>

                      <div
                        style={{
                          display: "flex",
                          fontSize: "14px",
                          padding: "10px 0px 0px 10px",
                          color: theme.textColor.titleColor,
                        }}
                      >
                        {(currentPage - 1) * limitPerPage + 1} -{" "}
                        {Math.min(limitPerPage * currentPage, totalResults)} of{" "}
                        {totalResults} rows
                      </div>
                    </div>
                    <div>
                      {/* <div>{((currentPage-1)*limitPerPage)+1} to {(limitPerPage*currentPage)} of {totalCount}rows</div> */}
                      <Pagination
                        className="pagination-bar"
                        currentPage={currentPage}
                        totalCount={totalResults}
                        pageSize={limitPerPage}
                        onPageChange={(page) => {
                          handlePageChange(page);
                        }}
                      />{" "}
                    </div>
                  </div>
                ) : null}
                {/* <div
              style={{
                display: "flex",
                justifyContent: "center",
                marginTop: "20px",
              }}
            >
              <Pagination
                className="pagination-bar"
                currentPage={currentPage}
                totalCount={totalResults}
                pageSize={pageSize}
                onPageChange={(page) => {
                  handlePageChange(page);
                }}
              />
            </div> */}
              </div>
            </>
          )}
        </div>
        <DeleteDialog
          show={showDeleteConfirmation !== 0 ? true : false}
          onHide={() => setShowDeleteConfirmation(0)}
          onConfirm={() => deleteUser(userId)}
          title={"Are you sure you want to delete this user?"}
          isLoading={deleteLoading}
        />
        <SuccessDialog
          show={successDialog}
          onHide={() => setSuccessDialog(false)}
          message={message} //"User is successfully deleted."
        />{" "}
        <CreateorEditModal
          imgErr={imgErr}
          setImgErr={setImgErr}
          show={showCreateorEditConfirmation !== 0 ? true : false}
          onHide={onHideCreateorEditModal}
          onConfirm={() => {
            setShowCreateorEditConfirmation(0);
            setRefetch((oldState) => !oldState);
          }}
          handleSuccessDialog={handleSuccessDialog}
          handleSetMessage={handleSetMessage}
          user={isEditUser ? editUser : null}
          editClicked={editClicked}
          setEditClicked={setEditClicked}
        />
        <InfoModal
          icon={<Alert className="w-10 h-10 " />}
          show={showAlertConfirmation}
          onHide={handleCloseInfoModal}
          message={message}
        />
        <ToastContainer position="top-center" autoClose={3000} />
      </div>
    </>
  );
}

export default Users;
