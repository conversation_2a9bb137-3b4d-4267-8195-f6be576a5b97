import React, { useState, useEffect, useMemo, useContext, useRef } from "react";
import Table from "../components/table/ReportTable";
import { reportService } from "../services/staticreport.service";
import {
  FileIcon,
  BarChartIcon,
  LineChartIcon,
  MultiAxisChartIcon,
  PieChartIcon,
  TableChartIcon,
} from "../icons";
import { AuthContext } from "../context/AuthContext";
import { useNavigate, useLocation } from "react-router-dom";
import { Tabs, Tab } from "@mui/material";
import Title from "../Title";
import theme from "../tailwind-theme";
import { Form, Formik } from "formik";
import Select from "../components/FormsUI/Select";
import moment from "moment-timezone";
import ExportPopup from "../popups/exportpopup";
import { TIME_ZONE } from "../utils/constants";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import { defaultTimeZone } from "../common/constants";
import bgImage from "../assets/img/Records.png";

function ReportsTab() {
  const location = useLocation();
  const formikRef = useRef(null);
  const tabData = location?.state?.tab || "";
  const subTypeData1 = location?.state?.subType || "";
  const [report, setReport] = useState([]);
  const [subtype, setSubType] = useState(subTypeData1 ? subTypeData1 : "");
  const [activeTab, setActiveTab] = useState(tabData ? tabData : 0);
  const [subReport, setSubReport] = useState([]);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [downloadGraph, setDownloadGraph] = useState("");
  const [loading, setLoading] = useState(false);
  const { roles } = useContext(AuthContext);
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  let filteredSubReportData;

  const columns = useMemo(() => {
    const baseColumns = [
      {
        header: "Report Name",
        accessorKey: "value",
        size: 150,
        muiTableHeadCellProps: {
          sx: {
            "& .MuiInputBase-root": {
              maxWidth: "250px",
            },
          },
        },
        Cell: ({ row }) => (
          <div className="flex gap-3 cursor-pointer">
            {row.original.visualizationType === "Bar Graph" ? (
              <BarChartIcon className="w-3.5 h-3.5 justify-center items-center flex" />
            ) : row.original.visualizationType === "Line Graph" ? (
              <LineChartIcon className="w-3.5 h-3.5 justify-center items-center flex" />
            ) : row.original.visualizationType === "Pie Chart" ? (
              <PieChartIcon className="w-3.5 h-3.5 justify-center items-center flex" />
            ) : row.original.visualizationType === "MultiAxis Graph" ? (
              <MultiAxisChartIcon className="w-3.5 h-3.5 justify-center items-center flex " />
            ) : row.original.visualizationType === "Table Report" ? (
              <TableChartIcon className="w-4 h-4 justify-center items-center flex " />
            ) : (
              <FileIcon className="w-4 h-4" />
            )}

            <div style={{ paddingRight: "8rem" }}>{row.original.value}</div>
          </div>
        ),
      },
    ];
    if (subtype === "Dynamic Reports") {
      baseColumns.push({
        header: "Visualization Type",
        accessorKey: "visualizationType",
        size: 150,
        Cell: ({ row }) => <span>{row.original.visualizationType || "-"}</span>,
      });
      baseColumns.push({
        header: "Created By",
        accessorKey: "createdBy",
        size: 150,
        Cell: ({ row }) => <span>{row.original.createdBy || "-"}</span>,
      });
    }
    return baseColumns;
  }, [subtype]);

  if (user.isSuperAdmin) {
    filteredSubReportData = subReport.map((report) => ({
      value: report.name,
      viewBy: report.viewBy,
      id: report.id || "",
      visualizationType: report.visualizationType,
      creater: report.creater || "",
      createdBy:
        report.creater === "Other User" ? user.name : report.creater || "-",
    }));
  } else {
    filteredSubReportData = subReport
      .filter((report) => {
        if (subtype === "Dynamic Reports") {
          return true;
        }

        const staticRole = roles.staticReports.find(
          (role) => role.name === report.name
        );
        const dynamicRole = roles.dynamicReports.find(
          (role) => role.name === report.name
        );

        const hasViewPermission =
          staticRole?.permissions?.view === 1 ||
          dynamicRole?.permissions?.view === 1;

        return hasViewPermission;
      })
      .map((report) => ({
        value: report.name,
        viewBy: report.viewBy,
        id: report.id || "",
        creater: report.creater || "",
        createdBy:
          report.creater === "Other User" ? user.name : report.creater || "-",
        visualizationType: report.visualizationType,
      }));
  }

  const handleTabClick = (index) => {
    setActiveTab(index);
    setSubType(report[index]);
  };

  const handleRowClick = (row) => {
    if (subtype === "Dynamic Reports") {
      navigate(
        `/app/reports/dynamic-reports?${formikRef.current.values.timeZone}`,
        {
          state: { ...row.original, activeTab, subtype },
        }
      );
    } else {
      navigate(
        `/app/reports/static-reports?${formikRef.current.values.timeZone}`,
        {
          state: { ...row.original, activeTab, subtype },
        }
      );
    }
  };

  // *********** API Calling
  useEffect(() => {
    setLoading(true);
    reportService
      .getReportDetails("REPORT_TYPE", "")
      .then((res) => {
        setReport(res.data);
        setSubType(res.data[tabData ? tabData : 0]);
      })
      .catch((err) => {
        //console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    if (subtype !== "") {
      setLoading(true);
      reportService
        .getReportDetails("REPORT_SUB_TYPE", subtype)
        .then((res) => {
          setSubReport(res?.data);
        })
        .catch((err) => {
          //console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [subtype]);

  const exportReport = (type) => {
    if (type !== "") {
      let extension =
        type === "PDF" ? ".pdf" : type === "EXCEL" ? ".xlsx" : ".csv";
      //console.log(downloadGraph, type);
      reportService.downloadReport(downloadGraph, 1, type).then((blob) => {
        const url = URL.createObjectURL(blob.data);
        const link = document.createElement("a");
        link.href = url;
        link.download = Date.now() + extension;
        link.click();
      });
    }
  };

  const timezones = moment.tz.names();
  const timezoneList = timezones
    .filter(
      (zone) =>
        zone !== "Asia/Calcutta" &&
        zone !== "Asia/Katmandu" &&
        zone !== "Asia/Macao" &&
        zone !== "Asia/Thimbu" &&
        zone !== "Africa/Asmera" &&
        zone !== "Etc/UCT"
    )
    .map((zone) => {
      const gmtOffset = moment.tz(zone).format("Z");
      return {
        label: `GMT${gmtOffset} - ${zone
          .replace("_", " ")
          .replace("/", " - ")}`,
        value: zone,
      };
    })
    .filter((item) => !/GMT[+-]0{2}:0{2} - GMT0/.test(item.label));

  //  console.log("filteredSubReportData", filteredSubReportData);

  return (
    <>
      <div className="bg-bgPrimary my-5 ">
        <div className="mt-10">
          <Title title={"Static Reports"} />
        </div>
        {loading ? (
          <div className="w-full h-screen flex justify-center items-center text-center p-6">
            {" "}
            Loading ...{" "}
          </div>
        ) : report.length === 0 && !loading ? (
          <>
            <div className="border border-outerBorder my-5 bg-white">
              <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                {"No reports have been assigned yet!"}
              </div>
              {/* <div className="flex justify-center text-tabColor text-base font-semibold mt-5 ">{`Reports will appear here once assigned.`}</div> */}
              <div className="flex justify-center my-12">
                <img
                  src={bgImage}
                  style={{
                    height: "10%",
                    width: "10%",
                    objectFit: "cover",
                  }}
                  alt="bg"
                />
              </div>
            </div>
          </>
        ) : (
          <div className="mx-3">
            <Formik
              innerRef={formikRef}
              initialValues={{ timeZone: TIME_ZONE }}
            >
              {({ setFieldValue }) => (
                <Form>
                  <div className="border border-listBorder bg-white p-3 mt-10">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center ml-auto mr-3 mt-3">
                        <InputLabel
                          label={"Time Zone"}
                          labelClassName="mr-3 font-medium"
                        />
                        <Select
                          name="timeZone"
                          className="w-[230px]"
                          options={
                            activeTab === 5
                              ? defaultTimeZone
                              : timezoneList || []
                          }
                          placeholder={"Select time zone"}
                          isSearchable={true}
                          onChange={(e) => setFieldValue("timeZone", e.value)} // Set selected value in Formik
                          menuListzIndex={9999}
                        />
                      </div>
                    </div>
                    <Tabs
                      value={activeTab}
                      onChange={(event, newValue) => {
                        handleTabClick(newValue);
                        setFieldValue("timeZone", TIME_ZONE); // Update Formik field value
                      }}
                      sx={{
                        "& .MuiTabs-indicator": {
                          backgroundColor: theme.backgroundColor.bgSecondary,
                        },
                      }}
                    >
                      {report.map((rep, index) => (
                        <Tab
                          key={index}
                          label={rep}
                          sx={{
                            textTransform: "none",
                            paddingBottom: 0,
                            paddingLeft: "10px",
                            paddingRight: "10px",
                            margin: "0 8px",
                            minWidth: "auto",
                          }}
                          className={`${
                            activeTab === index
                              ? "active text-errorColor"
                              : "text-tabColor"
                          }`}
                        />
                      ))}
                    </Tabs>

                    <div className="mt-5 mx-3">
                      <Table
                        columns={columns}
                        data={filteredSubReportData}
                        isLoading={loading}
                        onRowClick={handleRowClick}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        )}
      </div>
      <ExportPopup
        show={showExportConfirmation}
        onHide={() => setShowExportConfirmation(false)}
        onConfirm={(type) => {
          exportReport(type);
          setShowExportConfirmation(false);
        }}
        title={"Export Report"}
        identity={"Reports"}
      />
    </>
  );
}

export default ReportsTab;
