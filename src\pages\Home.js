import Card from "react-bootstrap/Card";
import { AuthContext } from "../context/AuthContext";
import { useContext, useEffect } from "react";
import ConfirmationModal from "../components/modals/ConfirmationModal";
import MyDashboard from "./DashboardManagement/MyDashboard";
import { useNavigate } from "react-router-dom";
const Home = () => {
  let { user, reset, logout, roles } = useContext(AuthContext);
  const navigate = useNavigate();

  const id = user.defaultDashboard;
  useEffect(() => {
    if (!reset) {
      const defaultDashboard = roles?.resources.some(
        (resource) =>
          resource.name === "Default Dashboard" &&
          resource.permissions.view === 1
      );

      if (!defaultDashboard) {
        navigate("/app/dashboard/details");
      }
    }
  }, [reset, roles, navigate]);
  return (
    <>
      {id ? (
        <MyDashboard dashboardId={id} isNoDownload={true} />
      ) : (
        <h4 className="flex justify-center items-center text-center h-screen">
          No Dashboard to display
        </h4>
      )}
      <ConfirmationModal
        show={reset ? true : false}
        onHide={() => {
          reset = false;
        }}
        cancelHide="true"
        align="center"
        confirmButonName="Okay"
        onConfirm={() => {
          logout();
        }}
        body={
          "We have sent an email to reset your password in your registered mailid. Kindly change your password."
        }
        title={"Reset Your Password"}
      />
    </>
  );
};

export default Home;
