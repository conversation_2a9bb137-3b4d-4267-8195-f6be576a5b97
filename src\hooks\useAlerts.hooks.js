import { useQuery } from "react-query";
import { alertServices } from "../services/alert-api";

const useAlerts = ({
  page = 1,
  limit = 10,
  search = "",
  sorts = [],
  filters = {},
}) => {
  return useQuery(
    ["alerts", { page, limit, search, sorts, filters }],
    () => alertServices.getAlertsList({ page, limit, search, sorts, filters }),
    {
      select: ({ data }) => {
        const { rows, count } = data;

        return {
          totalCount: count,
          pageNo: page,
          pageSize: limit,
          data: rows.map((row) => {
            const {
              id,
              name,
              created_at,
              updated_by,
              category,
              alert_type,
              created_by,
              updated_at,
            } = row;

            return {
              id,
              name,
              alertType: alert_type,
              groupName: "Group6",
              createdBy: created_by,
              createdAt: created_at,
              updatedAt: updated_at,
              updatedBy: updated_by,
            };
          }),
        };
      },
      keepPreviousData: true,
    }
  );
};

const useAlertsHistory = ({
  page = 1,
  limit = 10,
  search = "",
  startDate,
  endDate,
  status = "",
  shouldFetch = true, // Add this flag to control the initial load
  refreshKey = null,
}) => {
  return useQuery(
    [
      "alertsHistory",
      {
        page,
        limit,
        search,
        startDate,
        endDate,
        status,
        refreshKey,
      },
    ],
    () =>
      alertServices.getAlertsHistory({
        page,
        limit,
        search,
        startDate,
        endDate,
        status,
      }),

    {
      enabled: shouldFetch, // Use the `enabled` flag to control the query execution
      select: ({ data }) => {
        const { rows, count } = data;

        return {
          totalCount: count,
          pageNo: page,
          pageSize: limit,
          data: rows.map((row) => {
            const {
              id,
              name,
              created_at,
              category,
              alert_type,
              created_by,
              timestamp,
              status,
              interval_start,
              interval_end,
            } = row;

            return {
              id,
              name,
              alertType: alert_type,
              groupName: "Group6",
              createdBy: created_by,
              createdAt: created_at,
              category: category,
              timestamp: timestamp,
              status,
              intervalStart: interval_start,
              intervalEnd: interval_end,
            };
          }),
        };
      },
      // keepPreviousData: true,
    }
  );
};

export { useAlerts, useAlertsHistory };
