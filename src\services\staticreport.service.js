import axios from "axios";
import getAPIMap from "../routes/ApiUrls";
import { removeSelectAllFromPayload } from "../utils/constants";

async function getReport(options) {
  const cleanedPayload = removeSelectAllFromPayload(options.payload);
  return axios.post(getAPIMap("reports"), cleanedPayload);
}

async function downloadReport(
  name,
  download,
  type,
  startDate,
  endDate,
  searchStr,
  timeZone,
  totalCount,
  filters,
  duration,
  maxCount,
  fileName
) {
  const axiosConfig = {};

  if (totalCount < maxCount) {
    axiosConfig.responseType = "blob";
  }

  const requestBody = {
    reportName: name,
    download: 1,
    type,
    startDate,
    endDate,
    search: searchStr,
    timezone: timeZone,
    fileName: fileName,
  };

  if (["Daily", "Weekly", "Monthly"].includes(duration)) {
    requestBody.defaultViewBy =
      duration === "Daily" ? "day" : duration === "Weekly" ? "week" : "month";
  }

  if (filters && filters.length !== 0) {
    requestBody.filters = filters;
  }
  return axios.post(getAPIMap("reports"), requestBody, axiosConfig);
}

async function getReportDetails(type, typename) {
  let url = getAPIMap("classifiedReports") + `?type=${type}`;
  if (typename !== "") {
    url += `&subtype=${typename}`;
  }
  return axios.get(url);
}
async function getReportFilter({ queryKey }) {
  const [_, reportName, filterFlag] = queryKey;

  let url = getAPIMap("reportsFilter") + `?reportName=${reportName}`;

  if (filterFlag !== undefined) {
    url += `&filterFlag=${filterFlag}`;
  }

  return axios.get(url);
}

async function getCustomers() {
  let url = getAPIMap("customerData");
  return axios.get(url);
}
async function getLCRData() {
  let url = getAPIMap("lcrData");
  return axios.get(url);
}
async function getCDRStatus() {
  let url = getAPIMap("cdrStatusData");
  return axios.get(url);
}
async function getSuppliers() {
  let url = getAPIMap("supplierData");
  return axios.get(url);
}
async function getSourcePrime() {
  let url = getAPIMap("sourcePrimeFilter");
  return axios.get(url);
}
async function getDestinationPrime() {
  let url = getAPIMap("destinationPrimeFilter");
  return axios.get(url);
}
async function getDerivedField(options) {
  let url = getAPIMap("derivedFieldData");
  if (options.queryKey[1] && options.queryKey[1] !== "") {
    url = url + "?allDerivedFields=" + options.queryKey[1].allDerivedFields;
  }
  return axios.get(url);
}
async function getNonAggregatedField(options) {
  let url = getAPIMap("nonAggregatedField");

  return axios.get(url);
}
export async function sendMail(options) {
  let url = getAPIMap("reports");
  let response = axios.post(url, options.payload);
  return response;
}
export async function getSelectedColumn(options) {
  let url = getAPIMap("selectedColumn");
  if (options.queryKey[1] && options.queryKey[1] !== "") {
    url = url + "?reportType=" + options.queryKey[1];
  }
  if (options.queryKey[2] && options.queryKey[2] !== "") {
    url = url + "&reportName=" + options.queryKey[2];
  }
  if (options.queryKey[3] && options.queryKey[3] !== "") {
    url = url + "&reportId=" + options.queryKey[3];
  }
  let response = axios.get(url);
  return response;
}
export async function reportSave(options) {
  let url = getAPIMap("selectedColumn");
  let response = axios.post(url, options.payload);
  return response;
}
export const reportService = {
  getReport,
  getReportDetails,
  getCustomers,
  getSuppliers,
  downloadReport,
  getDerivedField,
  getNonAggregatedField,
  getLCRData,
  getCDRStatus,
  getReportFilter,
  getSourcePrime,
  getDestinationPrime,
};
