import React, { useState, useEffect, useRef } from "react";
import "./ChipInput.css";
import theme from "../../tailwind-theme";

const ChipInput = ({
  chips,
  onChipAdd,
  onChipRemove,
  isEdit,
  placeholder,
  searchQuery,
  isNew = false,
}) => {
  const [inputValue, setInputValue] = useState("");
  const inputRef = useRef(null);

  const handleInputChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleInputKeyDown = (event) => {
    if (event.key === "Enter" && inputValue.trim() !== "") {
      onChipAdd(inputValue.trim());
      setInputValue("");
      event.preventDefault();
    }
  };

  const highlightText = (text) => {
    if (!searchQuery) return text;
    const regex = new RegExp(`(${searchQuery})`, "gi");
    const parts = text.split(regex);
    return parts.map((part, index) =>
      part.toLowerCase() === searchQuery.toLowerCase() ? (
        <span key={index} style={{ backgroundColor: "yellow" }}>
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  useEffect(() => {
    const handleBackspace = (event) => {
      if (
        event.key === "Backspace" &&
        inputValue === "" &&
        chips.length > 0 &&
        document.activeElement === inputRef.current
      ) {
        event.preventDefault();
        onChipRemove(chips.length - 1);
      }
    };

    document.addEventListener("keydown", handleBackspace);

    return () => {
      document.removeEventListener("keydown", handleBackspace);
    };
  }, [inputValue, chips, onChipRemove]);

  return (
    <div className="input-container">
      <div className={`chips-input ${isEdit ? "" : "non-editable"}`}>
        {chips &&
          chips?.map((chip, index) => (
            <span
              key={index}
              style={{
                backgroundColor: !isEdit
                  ? "#eaeaeb"
                  : isNew
                  ? "#d3f1cb"
                  : "#F8C7C7",
                fontSize: "14px",
              }}
              className="chip"
            >
              {highlightText(chip)}
              {isEdit && (
                <span className="close" onClick={() => onChipRemove(index)}>
                  &times;
                </span>
              )}
            </span>
          ))}
        {isEdit && (
          <input
            id="chips"
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleInputKeyDown}
            placeholder={placeholder || "Add a value..."}
          />
        )}
      </div>
    </div>
  );
};

export default ChipInput;
