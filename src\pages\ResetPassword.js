import React from "react";
import { Link } from "react-router-dom";
import Logo from "../assets/img/Airtel 1.svg";
import ResetPasswordForm from "../components/Forms/ResetPasswordForm";

// import LoginForm from '../components/Forms/LoginForm'

function ResetPassword() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900 font-sans">
      <Link
        to="/"
        className="pl-8 pr-8 text-xl font-bold text-gray-800 dark:text-gray-200 bg-white  dark:bg-white-900"
      >
        <img
          width={"150px"}
          aria-hidden="true"
          src={Logo}
          alt="Airtel SMS Hub Reporting"
        />
      </Link>
      <div className="flex flex-1 h-full items-center lg:mt-0">
        <div className="flex-1 h-full max-w-xl mx-auto overflow-hidden bg-white rounded-lg shadow-xl dark:bg-gray-800">
          {/* <div className="flex flex-col overflow-y-auto md:flex-row"> */}
          {/* <div className="h-32 md:h-auto md:w-1/2">
              <img
                aria-hidden="true"
                className="object-cover w-full h-full "
                src={ImageLight}
                alt="Office"
              />
            </div> */}
          <main className="flex items-center justify-center p-6 sm:p-12 md:flex-row">
            <div className="w-full">
              <h1
                className="mb-4 text-xl font-semibold text-gray-700 dark:text-gray-200"
                style={{ textAlign: "center" }}
              >
                Reset Password
                <hr />
              </h1>
              <br />

              <ResetPasswordForm />
            </div>
          </main>
          {/* </div> */}
        </div>
      </div>
    </div>
  );
}

export default ResetPassword;
