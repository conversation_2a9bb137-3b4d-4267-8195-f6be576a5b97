import { useState, useEffect } from "react";

import { reportService } from "../services/staticreport.service";
import GraphImg from "../assets/img/File.png";
import {
  TableContainer,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
  Pagination,
} from "@windmill/react-ui";
import download from "../assets/img/Group 238310.png";
import ExportPopup from "../popups/exportpopup";

const Reports = () => {
  console.log("am i here in create dashboard...");
  return (
    <div style={{ height: "80%" }}>
      <h1 style={{ fontSize: "28px" }}>Static Reports</h1>
      <TabView />
    </div>
  );
};

function TabView() {
  const [activeTab, setActiveTab] = useState(0);
  const [report, setreport] = useState([]);
  const [subtype, setsubtype] = useState("");
  const [subreport, setsubreport] = useState([]);
  const [showOverlay, setShowOverlay] = useState(false);
  const [selectedGraph, setselectedGraph] = useState({});
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [downloadGraph, setDownloadGraph] = useState("");
  const [totalResults, setTotalResults] = useState(0);
  const [resultsPerPage, setresultsPerPage] = useState(10);
  const [page, setPage] = useState(1);
  const [cols, setcols] = useState([]);
  const [data, setdata] = useState([]);
  const handleTabClick = (index) => {
    setActiveTab(index);
    setsubtype(report[index]);
  };

  useEffect(() => {
    if (subtype !== "") {
      reportService
        .getReportDetails("REPORT_SUB_TYPE", subtype)
        .then((res) => {
          console.log("113count", res.data);
          setsubreport(res.data);
          //  setTotalResults(res?.data.totalCount);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, [subtype]);
  useEffect(() => {
    setcols([]);
    setdata([]);
    console.log(selectedGraph);
    if (selectedGraph !== "") {
      reportService
        .getReport(selectedGraph)
        .then((res) => {
          setdata(res.data.data);
          setTotalResults(res?.data.totalCount);
          const colsArray = [];
          colsArray.push({ name: "No.", field: "" });
          Object.keys(res.data.data[0]).map((col) => {
            colsArray.push({ name: col, field: col });
          });
          setcols(colsArray);
          console.log(cols);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, [selectedGraph]);
  useEffect(() => {
    reportService
      .getReportDetails("REPORT_TYPE", "")
      .then((res) => {
        console.log("113count", res.data.totalCount);
        setreport(res.data);
        setsubtype(res.data[0]);
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);
  const exportReport = (type) => {
    if (type !== "") {
      let extension =
        type === "PDF" ? ".pdf" : type === "EXCEL" ? ".xlsx" : ".csv";
      console.log(downloadGraph, type);
      reportService.downloadReport(downloadGraph, 1, type).then((blob) => {
        const url = URL.createObjectURL(blob.data);
        const link = document.createElement("a");
        link.href = url;
        link.download = Date.now() + extension;
        link.click();
      });
    }
  };
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };
  console.log("113repots", totalResults);
  return (
    <div style={{ background: "white" }}>
      <div className="tab-nav">
        {console.log(report)}
        {!showOverlay &&
          report &&
          report.length > 0 &&
          report.map((rep, index) => (
            <button
              style={{ fontSize: "14px" }}
              className={activeTab === index ? "active" : ""}
              onClick={() => handleTabClick(index)}
            >
              {rep}
            </button>
          ))}
      </div>
      <div className="tab-content">
        {!showOverlay && (
          <>
            <div>
              <TableContainer>
                <Table className="table table-auto">
                  <TableHeader>
                    <TableRow>
                      <TableCell style={{ width: "80%", fontSize: "15px" }}>
                        Report Name
                      </TableCell>
                      <TableCell style={{ width: "20%" }}></TableCell>
                    </TableRow>
                  </TableHeader>

                  <TableBody>
                    {console.log("Subreport", subreport)}
                    {subreport.map((graph, index) => (
                      <TableRow>
                        {console.log("Graph", graph)}
                        <TableCell
                          style={{ cursor: "pointer" }}
                          onClick={() => {
                            setShowOverlay(true);

                            console.log(cols);
                            setselectedGraph(graph);
                          }}
                        >
                          <img
                            src={GraphImg}
                            style={{ float: "left" }}
                            alt="graph"
                          />{" "}
                          &nbsp;{" "}
                          <span style={{ fontSize: "14px" }}>{graph.name}</span>
                        </TableCell>
                        <TableCell style={{ cursor: "pointer" }}>
                          {" "}
                          <img
                            alt="download"
                            src={download}
                            style={{ float: "left" }}
                            onClick={() => {
                              setShowExportConfirmation(true);
                              setDownloadGraph(graph);
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          </>
        )}

        {showOverlay && (
          <div>
            <div>
              <h1 style={{ width: "98%", float: "left", fontSize: "20px" }}>
                {selectedGraph}
              </h1>
              <span
                style={{ width: "2%", float: "left", cursor: "pointer" }}
                onClick={() => setShowOverlay(false)}
              >
                <b>x</b>
              </span>
            </div>
            <TableContainer>
              <Table className="table table-auto report-table">
                <TableHeader>
                  <TableRow>
                    {cols &&
                      cols.map((col) =>
                        col.name === "No." ? (
                          <TableCell style={{ width: "1%", fontSize: "12px" }}>
                            {col.name}
                          </TableCell>
                        ) : (
                          <TableCell style={{ width: "10%", fontSize: "12px" }}>
                            {col.name}
                          </TableCell>
                        )
                      )}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data &&
                    data.map((d, ind) => (
                      <TableRow key={ind}>
                        {cols &&
                          cols.map((col, cinde) => {
                            return col.name === "No." ? (
                              <TableCell style={{ width: "1%" }} key={cinde}>
                                {ind + 1}
                              </TableCell>
                            ) : (
                              <TableCell key={cinde}>{d[col.field]}</TableCell>
                            );
                          })}
                      </TableRow>
                    ))}
                  {!data && (
                    <TableRow>
                      <TableCell colSpan="4" className="text-center">
                        <div className="spinner-border spinner-border-lg align-center"></div>
                      </TableCell>
                    </TableRow>
                  )}
                  {data && !data.length && (
                    <TableRow>
                      <TableCell colSpan="4" className="text-center">
                        <div className="p-2">No Rows To Display</div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        )}
      </div>
      <div className=" flex justify-center">
        {" "}
        <Pagination
          totalResults={totalResults}
          resultsPerPage={resultsPerPage}
          onChange={handlePageChange}
          label="Page navigation"
        />
      </div>

      <ExportPopup
        show={showExportConfirmation}
        onHide={() => setShowExportConfirmation(false)}
        graphName={selectedGraph}
        onConfirm={(type) => {
          console.log("am i here...");
          exportReport(
            type === "PDF" ? ".pdf" : type === "EXCEL" ? ".xlsx" : ".csv"
          );
          setShowExportConfirmation(false);
        }}
        title={"Export Report"}
        identity={"Reports"}
      />
    </div>
  );
}

export default Reports;
