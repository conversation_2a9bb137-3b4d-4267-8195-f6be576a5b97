import React, { useState, useContext } from "react";
import { useLocation } from "react-router";
import BackButton from "../../components/Button/Button";
import { useNavigate } from "react-router-dom";
import PanelPreviewData from "./PanelPreviewData";
import AddCard from "../../popups/AddCard";
import CardPreviewData from "./CardPreviewData";
import RemoveCard from "../../popups/RemoveCard";
import Button from "../../components/Button/OutlinedButton";
import CancelButton from "../../components/Button/Button";
import { createDashboard } from "../../services/dashboard-api";
import { useMutation } from "react-query";
import SuccessDialog from "../../popups/SuccessDialog";
import ResponsiveLayout from "./ResponsiveLayout";
import AvailableItemsAccordion from "../../components/Accordion/AvailableItemsAccordion";
import ErrorDialog from "../../popups/ErrorDialog";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import { AuthContext } from "../../context/AuthContext";
import InfoModal from "../../components/modals/InfoModal";
import Title from "../../Title";
import BreadcrumbNavigation from "../../components/BreadCrumps/BreadCrump";
import { EditingIcon } from "../../icons";

function DashboardCreation() {
  const [panelDroppedData, setPanelDroppedData] = useState([]);
  const [cardDroppedData, setCardDroppedData] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [removeDialog, setRemoveDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState(false);
  const [message1, setMessage1] = useState(false);
  const [isCardAdded, setIsCardAdded] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  // const dashboardName = location?.state?.name;
  const [dashboardName, setDashboardName] = useState(location?.state?.name);
  const [editDashboardName, setEditDashboardName] = useState(false);
  const [dashboardNameErr, setDashboardNameErr] = useState("");

  const [totalCount, setTotalCount] = useState({
    panel: 0,
    card: 0,
  });

  const { roles } = useContext(AuthContext);
  const { configApiData } = useContext(AuthContext);
  //console.log("configApiData", configApiData);
  let dimension = { w: panelDroppedData.length > 0 ? 3 : 6, h: 3 };

  const permissions = roles.resources.filter(
    (res) => res.name === "Card Management"
  )[0].permissions;

  const panelPermissions = roles.resources.filter(
    (res) => res.name === "Panel Management"
  )[0].permissions;

  const { setCurrentStep, setStepCount, setFormData } =
    useContext(multiStepFormContext);

  const handleCardAdded = () => {
    setIsCardAdded(true);
  };

  const handleCardAddedFalse = () => {
    setIsCardAdded(false);
  };

  const { mutate: createDashboardAPI, isLoading: creationLoading } =
    useMutation(createDashboard);

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    //console.log("dropData", e.dataTransfer);
    // if (e.dataTransfer.dropEffect === "none") {
    //   e.preventDefault();
    //   return;
    // }
    try {
      const droppedData = JSON.parse(e.dataTransfer.getData("data"));
      //console.log("droppedData123", droppedData);
      const addPanel = (panelData) => {
        const startDate = droppedData?.data?.reqData?.startDate;
        const endDate = droppedData?.data?.reqData?.endDate;
        const count = droppedData?.data?.previewResponse?.data?.count;
        // console.log("startDate,endDate", startDate, endDate);
        const maxAllowedPanels = roles?.isSuperAdminRole
          ? configApiData?.MAX_PANELS_PER_DASHBOARD
          : roles?.panelCount;

        const isPanelAlreadyDropped = panelDroppedData.some(
          (panel) => panel.panelData.id === panelData.id
        );

        if (!isPanelAlreadyDropped && panelData) {
          if (panelDroppedData.length < maxAllowedPanels) {
            setPanelDroppedData((prevData) => [
              ...prevData,
              { panelData, dimension, startDate, endDate, count },
            ]);
          } else {
            setShowAlertConfirmation(true);
            setMessage(
              `Maximum ${maxAllowedPanels} panels allowed in a dashboard`
            );
          }
        } else {
          setShowAlertConfirmation(true);
          setMessage("Already the panel is added");
        }
      };

      const addCard = (cardData) => {
        const maxAllowedCards = roles?.isSuperAdminRole
          ? configApiData?.MAX_CARDS_PER_DASHBOARD
          : 4;
        const isCardAlreadyDropped = cardDroppedData.some(
          (card) => card.id === cardData.id
        );
        if (!isCardAlreadyDropped) {
          if (cardDroppedData.length < maxAllowedCards) {
            setCardDroppedData((prevData) => [...prevData, cardData]);
          } else {
            setShowAlertConfirmation(true);
            setMessage(
              `Maximum ${maxAllowedCards} cards allowed in a dashboard`
            );
          }
        } else {
          setShowAlertConfirmation(true);
          setMessage("Already the card is added");
        }
      };

      if (droppedData.type === "panel") {
        const { panel, previewResponse } = droppedData?.data;
        const panelData = {
          ...panel,

          data:
            panel.visualizationType === "MultiAxis Graph"
              ? previewResponse?.data
              : previewResponse?.data.data,
        };

        addPanel(panelData);
      } else if (droppedData.type === "card") {
        const cardData = droppedData?.data;
        addCard(cardData);
      }
    } catch (error) {
      e.preventDefault();
    }
  };

  function openCard() {
    setOpenDialog(true);
  }

  function closeCard() {
    setOpenDialog(false);
  }

  const removeCard = (index) => {
    setCardDroppedData((prevData) => prevData.filter((_, i) => i !== index));
  };
  const removePanel = (index) => {
    setPanelDroppedData((prevData) => {
      const newData = prevData.filter((_, i) => i !== index);
      return newData.map((ele, i) => {
        let dimension = { w: i > 0 ? 3 : 6, h: 3 };
        return { ...ele, dimension };
      });
    });
  };
  //console.log("dash-panelDroppedData", panelDroppedData);
  const createAPICall = () => {
    const cardIds = cardDroppedData.map((card) => card.id);
    const panelIds = panelDroppedData.map((panel) => panel.panelData.id);

    let reqData = {
      name: dashboardName,
      cards: cardIds.map((id, index) => ({ id, order: index + 1 })),
      panels: panelIds.map((id, index) => ({ id, order: index + 1 })),
    };

    createDashboardAPI(
      {
        reqData,
      },
      {
        onSuccess: (resp) => {
          setSuccessDialog(true);
          setMessage("Dashboard created successfully");
        },
        onError: ({ response }) => {
          //console.log("save response", response);
          setErrorDialog(true);
          setMessage(response?.data?.message);
        },
      }
    );
  };
  const getGridData = (index, dimension) => {
    let dim = { x: 0, y: 0, w: dimension.w, h: dimension.h };

    switch (index) {
      case 0:
        return dim;
      default:
        dim.x = index % 2 === 0 ? 6 : 0;
        dim.y = index % 2 === 0 ? 6 : 3;
        return dim;
    }
  };

  const handleDashboardNameError = (e) => {
    setDashboardName(e.target.value);
    if (
      !/^(?=.*[a-zA-Z])[a-zA-Z0-9_\s!@#$%^&*()\-+=~`'<>.,/?;:{}[\]|\\]*$/.test(
        e.target.value
      )
    ) {
      setDashboardNameErr("Enter atleast one alphabet");
    } else if (e.target.value?.length < 2) {
      setDashboardNameErr("Min length allowed is 2 characters");
    } else if (e.target.value?.length > 256) {
      setDashboardNameErr("Max length allowed is 256");
    } else {
      setDashboardNameErr("");
    }
  };

  return (
    <>
      <div
        className="flex mt-5 h-[95%] w-full"
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(e)}
      >
        <div>
          <div className="cursor-pointer ">
            <BreadcrumbNavigation
              linkTwo={"Dashboard"}
              onlinkTwoClick={() => navigate("/app/dashboard/details")}
              title={dashboardName}
            />
            <div className="mt-6 ">
              {!editDashboardName ? (
                <div className=" flex">
                  <p>{dashboardName}</p>{" "}
                  <EditingIcon
                    className="w-4 h-4 ml-2 mt-1 text-gray-500 cursor-pointer"
                    onClick={() => setEditDashboardName(true)}
                  />
                </div>
              ) : (
                <div>
                  <input
                    type="text"
                    value={dashboardName}
                    onChange={(e) => handleDashboardNameError(e)}
                  />
                  {dashboardNameErr ? (
                    <p className="text-errorColor text-xs my-2">
                      {dashboardNameErr}
                    </p>
                  ) : null}
                </div>
              )}
            </div>
          </div>
          <div>
            {cardDroppedData.length < 1 && panelDroppedData.length < 1 ? (
              <div className="text-center mt-32">
                Please create or drag and drop the cards or panels
              </div>
            ) : null}
          </div>
          <div className="text-sm font-normal mt-2 -ml-3 w-[68vw]  min-h-[46%] relative">
            <ResponsiveLayout
              cardDroppedData={cardDroppedData}
              removeCard={removeCard}
              panelDroppedData={panelDroppedData}
              removePanel={removePanel}
              getGridData={getGridData}
              isCreate={true}
            />
          </div>

          <div className="flex gap-3 mb-3 justify-end w-full">
            <CancelButton
              label={"Cancel"}
              buttonClassName="w-[100px] h-9 text-xs mb-3"
              onClick={() => {
                navigate("/app/dashboard/details");
              }}
            />
            <Button
              type="submit"
              label={"Save"}
              buttonClassName="w-[100px] h-9 text-xs  mr-3 "
              onClick={() => createAPICall()}
              loading={creationLoading}
              disabled={
                (cardDroppedData.length === 0 &&
                  panelDroppedData.length === 0) ||
                dashboardNameErr
              }
            />
          </div>
        </div>

        <div className="bg-white p-2 rounded mr-20">
          <div className="border border-panelBorder p-2.5 rounded ">
            <div className="flex flex-col gap-2">
              <BackButton
                label={"+ Add New Card"}
                buttonClassName="w-full md:w-[200px] text-xs mb-2 border-errorBorder text-errorColor"
                onClick={() => {
                  if (!roles.isSuperAdminRole) {
                    if (permissions.create === 1) {
                      openCard();
                    } else {
                      setShowAlertConfirmation(true);
                      setMessage("Create permission not allowed");
                    }
                  } else {
                    openCard();
                  }
                }}
              />
              <BackButton
                label={"+ Add New Panel"}
                buttonClassName="w-full md:w-[200px] text-xs mb-2 border-errorBorder text-errorColor"
                onClick={() => {
                  if (!roles.isSuperAdminRole) {
                    if (panelPermissions.create === 1) {
                      setCurrentStep(0);
                      setStepCount(0);
                      setFormData([]);
                      navigate("/app/panelmanagement/add", {
                        state: { Dashboard: true, name: dashboardName },
                      });
                    } else {
                      setShowAlertConfirmation(true);
                      setMessage("Create permission not allowed");
                    }
                  } else {
                    setCurrentStep(0);
                    setStepCount(0);
                    setFormData([]);
                    navigate("/app/panelmanagement/add", {
                      state: { Dashboard: true, name: dashboardName },
                    });
                  }
                }}
              />
              <AvailableItemsAccordion
                title="Available Cards"
                count={totalCount?.card}
              >
                <CardPreviewData
                  isCardAdded={isCardAdded}
                  setTotalCount={setTotalCount}
                  handleCardAddedFalse={handleCardAddedFalse}
                />
              </AvailableItemsAccordion>
              <AvailableItemsAccordion
                title="Available Panels"
                count={totalCount?.panel}
              >
                <PanelPreviewData setTotalCount={setTotalCount} />
              </AvailableItemsAccordion>
            </div>
          </div>
        </div>
      </div>

      <AddCard
        openCard={openDialog}
        closeCard={closeCard}
        isAdd={true}
        onCardAdded={handleCardAdded}
      />
      <RemoveCard
        open={removeDialog}
        onCancelClick={() => setRemoveDialog(false)}
        message={message}
        message1={message1}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => {
          setSuccessDialog(false);
          navigate("/app/dashboard/details");
        }}
        message={message}
      />
      <ErrorDialog
        show={errorDialog}
        onHide={() => {
          setErrorDialog(false);
          navigate("/app/dashboard/details");
        }}
        message={message}
      />
      <InfoModal
        show={showAlertConfirmation}
        onHide={() => {
          setShowAlertConfirmation(false);
        }}
        message={message}
      />
    </>
  );
}

export default DashboardCreation;
