// TabManager.js
// This component handles the logic for detecting and managing multiple tabs.

import React, { useState, useEffect, useRef, useCallback } from "react";

const TabManager = ({ children }) => {
  // State to track if the current tab is the master tab
  const [isMaster, setIsMaster] = useState(false);
  // State to track if the component is currently checking for master status
  const [checkingMaster, setCheckingMaster] = useState(true);
  // Ref to store the unique ID for the current tab
  const tabIdRef = useRef(
    `tab_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  );
  // Ref to store the BroadcastChannel instance
  const channelRef = useRef(null);
  // Refs for timeout IDs to manage cleanup
  const queryMasterTimeoutRef = useRef(null);
  const masterClaimTimeoutRef = useRef(null);
  const masterLivenessIntervalRef = useRef(null); // Added for liveness check

  const localStorageKey = "app_master_tab_id_v3"; // Unique key for localStorage

  // Function to designate the current tab as master
  const becomeMaster = useCallback(() => {
    localStorage.setItem(localStorageKey, tabIdRef.current);
    setIsMaster(true);
    setCheckingMaster(false);
    if (channelRef.current) {
      channelRef.current.postMessage({
        type: "MASTER_ANNOUNCEMENT",
        payload: { masterId: tabIdRef.current },
      });
    }
  }, []);

  // Function to designate the current tab as secondary
  const becomeSecondary = useCallback((masterId) => {
    setIsMaster(false);
    setCheckingMaster(false);
    // Optional: ensure localStorage reflects the known master, even if this tab isn't it.
    if (masterId) {
      localStorage.setItem(localStorageKey, masterId);
    }
  }, []);

  useEffect(() => {
    const currentTabId = tabIdRef.current;

    // Initialize BroadcastChannel
    try {
      channelRef.current = new BroadcastChannel(
        "app_tab_coordination_channel_v3"
      );
    } catch (error) {
      // Fallback for browsers without BroadcastChannel:
      // If a master ID is in localStorage, assume this is a secondary tab.
      // This is less robust as it can't handle crashed master tabs well.
      const storedMasterIdSimple = localStorage.getItem(localStorageKey);
      if (storedMasterIdSimple && storedMasterIdSimple !== currentTabId) {
        becomeSecondary(storedMasterIdSimple);
      } else {
        // No master or this tab was the master (e.g. after refresh)
        becomeMaster();
      }
      return; // Exit effect if BroadcastChannel is not supported
    }

    // Handler for messages received on the BroadcastChannel
    const handleMessage = (event) => {
      if (!event.data || !event.data.type) return;
      const { type, payload } = event.data;

      // Clear any pending timeouts if another tab makes a definitive statement
      if (type === "MASTER_ANNOUNCEMENT" || type === "MASTER_RESPONSE") {
        if (payload.masterId !== currentTabId) {
          // Message is from another tab
          clearTimeout(queryMasterTimeoutRef.current);
          clearTimeout(masterClaimTimeoutRef.current);
        }
      }

      switch (type) {
        case "MASTER_ANNOUNCEMENT":
          // Another tab announced itself as master
          if (payload.masterId !== currentTabId) {
            becomeSecondary(payload.masterId);
          }
          break;
        case "QUERY_MASTER":
          // Another tab is asking who the master is
          // Check 'isMaster' state which is more current than localStorage within this tab's lifecycle
          if (isMaster) {
            channelRef.current.postMessage({
              type: "MASTER_RESPONSE",
              payload: { masterId: currentTabId },
            });
          }
          break;
        case "MASTER_RESPONSE":
          // Received a response from an existing master
          if (payload.masterId !== currentTabId) {
            becomeSecondary(payload.masterId);
          }
          break;
        case "MASTER_CLOSING":
          // The master tab is closing
          if (localStorage.getItem(localStorageKey) === payload.masterId) {
            localStorage.removeItem(localStorageKey);
            setCheckingMaster(true); // Re-enter checking phase

            // Attempt to become master after a short, randomized delay to reduce race conditions
            masterClaimTimeoutRef.current = setTimeout(() => {
              const existingMaster = localStorage.getItem(localStorageKey);
              if (!existingMaster) {
                becomeMaster();
              } else {
                // Another tab became master faster
                becomeSecondary(existingMaster);
              }
            }, Math.random() * 250 + 50); // Random delay between 50ms and 300ms
          }
          break;
        default:
          break;
      }
    };

    channelRef.current.onmessage = handleMessage;

    // Initial master check logic
    const storedMasterId = localStorage.getItem(localStorageKey);

    if (!storedMasterId) {
      // No master in localStorage, try to become master immediately
      becomeMaster();
    } else if (storedMasterId === currentTabId) {
      // This tab was previously the master (e.g., after a refresh)
      becomeMaster(); // Re-assert mastership and announce
    } else {
      // Master ID found in localStorage. Query its liveness.
      channelRef.current.postMessage({ type: "QUERY_MASTER" });
      setCheckingMaster(true); // Assume secondary until confirmed

      queryMasterTimeoutRef.current = setTimeout(() => {
        // If no response from stored master after timeout, try to become master
        const currentStoredMaster = localStorage.getItem(localStorageKey);
        // Only claim if the stored master is still the one we queried or if no master exists
        if (!currentStoredMaster || currentStoredMaster === storedMasterId) {
          becomeMaster();
        } else {
          // Another tab became master or responded while we were waiting
          becomeSecondary(currentStoredMaster);
        }
      }, 750); // Timeout for master query response (e.g., 0.75 seconds)
    }

    // Cleanup function for when the component unmounts or tab closes
    const handleBeforeUnload = () => {
      // Check 'isMaster' state directly
      if (isMaster) {
        // Important: Remove from localStorage *before* posting message to avoid race condition
        // where another tab might read the stale localStorage entry.
        localStorage.removeItem(localStorageKey);
        if (channelRef.current) {
          channelRef.current.postMessage({
            type: "MASTER_CLOSING",
            payload: { masterId: currentTabId },
          });
        }
      }
      // Clean up timeouts and close channel
      clearTimeout(queryMasterTimeoutRef.current);
      clearTimeout(masterClaimTimeoutRef.current);
      if (channelRef.current) {
        channelRef.current.close();
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      // Additional cleanup if component unmounts for other reasons (e.g., SPA navigation)
      handleBeforeUnload(); // Call the same cleanup logic
    };
  }, [isMaster, becomeMaster, becomeSecondary]); // isMaster is included to re-run if master status changes programmatically

  // Effect for secondary tabs to periodically check master liveness
  useEffect(() => {
    if (!isMaster && !checkingMaster) {
      // This tab is confirmed secondary. Start checking master liveness.
      masterLivenessIntervalRef.current = setInterval(() => {
        const currentMasterIdInStorage = localStorage.getItem(localStorageKey);
        if (!currentMasterIdInStorage) {
          // Master key is gone from localStorage.
          // This could be due to master closing gracefully, crashing, or another tab cleaning up.
          // Reload to re-initiate master election process for this tab.
          window.location.reload();
        }
        // If master key exists, this tab assumes the master is still active or
        // another tab has taken over. Existing BroadcastChannel messages and
        // initial query logic handle transitions.
      }, 2000); // Check every 2 seconds

      return () => {
        if (masterLivenessIntervalRef.current) {
          clearInterval(masterLivenessIntervalRef.current);
          masterLivenessIntervalRef.current = null;
        }
      };
    } else {
      // Not a confirmed secondary tab (either master, or still checking), ensure interval is cleared.
      if (masterLivenessIntervalRef.current) {
        clearInterval(masterLivenessIntervalRef.current);
        masterLivenessIntervalRef.current = null;
      }
    }
  }, [isMaster, checkingMaster, localStorageKey, tabIdRef]);

  // Display a loading message while checking tab status
  if (checkingMaster) {
    return null;
  }

  if (!isMaster) {
    return (
      <div className="max-w-[650px] mx-auto my-8 px-8 py-6 bg-[#fef2f4] text-[#7a0026] border border-[#fbd2db] rounded-xl text-center shadow-lg font-sans leading-relaxed">
        <h1 className="text-2xl font-semibold text-[#e40046] mb-4">
          ⚠️ Multiple Tabs Detected
        </h1>
        <p className="text-lg mb-2">
          This application is designed to work best in a single browser tab.
        </p>
        <p className="text-base text-[#7a0026]">
          You currently have more than one instance open. Please close this tab
          or other instances to ensure optimal performance and prevent potential
          data conflicts.
        </p>
      </div>
    );
  }

  return <>{children}</>;
};

export default TabManager;
