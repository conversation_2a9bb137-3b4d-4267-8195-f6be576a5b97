import LoadingButton from "@mui/lab/LoadingButton";
import CircularProgress from "@mui/material/CircularProgress";
import React from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import { DownloadArrowIcon } from "../../icons";
const ConfirmNNextButton = ({ buttonClassName, download, ...rest }) => {
  const { t } = useTranslation();
  return (
    <LoadingButton
      {...rest}
      style={{ textTransform: "none" }}
      className={twMerge(
        `bg-bgSecondary text-white text-medium font-normal h-10 rounded-10px disabled:bg-opacity-60 disabled:text-white ${buttonClassName}`
      )}
      variant="contained"
      // loadingIndicator={<CircularProgress className="text-white" />}
    >
      <span>{t(`${rest.label}`)}</span>
      {download === true ? <DownloadArrowIcon className="ml-2" /> : null}
    </LoadingButton>
  );
};

export default ConfirmNNextButton;
