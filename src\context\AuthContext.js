import React, {
  useState,
  useMemo,
  useEffect,
  useCallback,
  useRef,
  // useContext,
} from "react";
import moment from "moment";
import axios from "axios";
import { config } from "../assets/config/config";
import ThemedSuspense from "../components/ThemedSuspense";
import IdleWarningModal from "../components/IdleWarningModal";
import { TIME_ZONE } from "../utils/constants";

const apiUrl = config.api.url;

// create context
export const AuthContext = React.createContext();

export const AuthProvider = ({ children }) => {
  const [isLoaded, setLoaded] = useState(false);
  const [user, setUser] = useState(null);
  const [roles, setRoles] = useState(null);
  const [accessToken, setAccessToken] = useState(null);
  const [fetchRole, setFetchRole] = useState(false);
  const [reset, setReset] = useState(false);
  const [configApiData, setConfigApiData] = useState();
  const [storeLoginTime, setStoreLoginTime] = useState();
  const [timezone, setTimezone] = useState(
    Intl.DateTimeFormat().resolvedOptions().timeZone
  );

  // Idle timeout states
  const [showIdleWarning, setShowIdleWarning] = useState(false);
  const [idleWarningCountdown, setIdleWarningCountdown] = useState(() => {
    // Initialize with dynamic value if available, otherwise use default (convert to seconds for display)
    return Math.floor(
      (configApiData?.IDLE_WARNING_DURATION || 60 * 1000) / 1000
    );
  });
  const [isLoggingOut, setIsLoggingOut] = useState(false); // Prevent duplicate logout calls
  // Refs for timeout management
  const timeoutRef = useRef();
  const warningTimeoutRef = useRef();
  const countdownIntervalRef = useRef();
  const userRef = useRef(user);
  const showIdleWarningRef = useRef(showIdleWarning);
  const isLoggingOutRef = useRef(false);

  // Update refs when state changes
  useEffect(() => {
    userRef.current = user;
  }, [user]);

  useEffect(() => {
    showIdleWarningRef.current = showIdleWarning;
  }, [showIdleWarning]);

  useEffect(() => {
    isLoggingOutRef.current = isLoggingOut;
  }, [isLoggingOut]);

  const fetchAfterLogin = (token, roleId) => {
    const config = { headers: { Authorization: `Bearer ${token}` } };

    try {
      axios.get(`${apiUrl}/v1/users/config`, config).then(({ data }) => {
        // console.log("result", data);

        setConfigApiData(data);
      });
      return axios
        .get(`${apiUrl}/v1/roles/${roleId}`, config)
        .then(({ data }) => {
          setRoles(data);
          setFetchRole(false);
          setLoaded(true);
        });
    } catch (error) {
      console.error(error);
      setLoaded(true);
    }
  };

  useEffect(() => {
    if (fetchRole === true && user?.roleId && accessToken) {
      fetchAfterLogin(accessToken.token, user.roleId);
    }
  }, [fetchRole, user, accessToken]);

  const refreshTokens = useCallback(() => {
    return axios
      .post(`${apiUrl}/v1/auth/refresh-tokens`, {})
      .then(({ data }) => {
        setAccessToken(data.token);
        setUser(data.user);
        fetchAfterLogin(data.token.token, data.user.roleId);
        return data;
      })
      .catch((error) => {
        setLoaded(true);
        setUser(null);
        setAccessToken(null);
        setLoaded(true);
        return error;
      });
  }, []);

  const startSilentRefresh = useCallback(() => {
    if (accessToken) {
      const tokenExpires = moment(accessToken.expires);
      const tokenMaxAge = tokenExpires.diff(moment().add(1, "minutes"));
      setTimeout(() => {
        refreshTokens();
      }, tokenMaxAge);
    }
  }, [accessToken, refreshTokens]);

  const syncLogout = (event) => {
    if (event.key === "logout") {
      setAccessToken(null);
      setUser(null);
    }
  };
  useEffect(() => {
    const interceptorId = axios.interceptors.request.use(
      (config) => {
        config.withCredentials = true;
        config.credentials = "include";
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.request.eject(interceptorId);
    };
  }, [accessToken]);

  useEffect(() => {
    refreshTokens();
  }, [refreshTokens]);

  useEffect(() => {
    startSilentRefresh();
  }, [accessToken, startSilentRefresh]);

  useEffect(() => {
    window.addEventListener("storage", syncLogout);
    return function cleanup() {
      window.removeEventListener("storage", syncLogout);
    };
  }, []);

  const handleLoginTime = useCallback(() => {
    const storedLoginTime = window.localStorage.getItem("loginTime");
    if (storedLoginTime) {
      const loginTime = new Date(parseInt(storedLoginTime));
      setStoreLoginTime(formatDateTime(loginTime));
    } else {
      setStoreLoginTime("---");
    }
  }, []);

  const formatDateTime = (date) => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  useEffect(() => {
    handleLoginTime(); // Call handleLoginTime when component mounts or refreshes
  }, [handleLoginTime]);

  // Idle timeout functions
  const handleIdleLogout = useCallback(() => {
    // Prevent duplicate logout calls
    if (isLoggingOutRef.current) {
      return;
    }

    setIsLoggingOut(true);
    setShowIdleWarning(false);

    // Clear all timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
    }

    setAccessToken(null);
    setUser(null);

    axios
      .post(`${apiUrl}/v1/auth/logout`, {})
      .then(() => {
        window.localStorage.setItem("logout", moment());
      })
      .catch((err) => {
        console.error("Logout API call failed:", err);
      })
      .finally(() => {
        setIsLoggingOut(false);
      });
  }, []);

  const resetIdleTimeout = useCallback(() => {
    // Clear any existing timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
    }

    // Get timeout values directly from configApiData or use defaults (in milliseconds)
    const timeoutDuration =
      configApiData?.IDLE_TIMEOUT_DURATION_MS || 10 * 60 * 1000; // 10 minutes default
    const warningDuration =
      configApiData?.IDLE_WARNING_DURATION_MS || 1 * 60 * 1000; // 1 minute default

    // Reset states
    setShowIdleWarning(false);
    setIdleWarningCountdown(Math.floor(warningDuration / 1000)); // Convert to seconds for display

    // Only set timeouts if user is logged in
    if (!userRef.current) return;

    // Set warning timeout (timeoutDuration - warningDuration seconds)
    const warningTime = timeoutDuration - warningDuration;
    warningTimeoutRef.current = setTimeout(() => {
      setShowIdleWarning(true);

      // Start countdown (convert milliseconds to seconds for display)
      let countdown = Math.floor(warningDuration / 1000);
      setIdleWarningCountdown(countdown);

      countdownIntervalRef.current = setInterval(() => {
        countdown -= 1;
        setIdleWarningCountdown(countdown);

        if (countdown <= 0) {
          clearInterval(countdownIntervalRef.current);
        }
      }, 1000);
    }, warningTime);

    // Set logout timeout
    timeoutRef.current = setTimeout(() => {
      handleIdleLogout();
    }, timeoutDuration);
  }, [handleIdleLogout, configApiData]); // Added configApiData as dependency

  const stayLoggedIn = useCallback(() => {
    resetIdleTimeout();
  }, [resetIdleTimeout]);

  // Setup idle timeout event listeners
  useEffect(() => {
    if (!user) return;

    const events = [
      "mousedown",
      "mousemove",
      "keypress",
      "keydown",
      "scroll",
      "touchstart",
      "click",
    ];

    const handleActivity = () => {
      // Only reset timeout if warning is not showing
      if (!showIdleWarningRef.current) {
        resetIdleTimeout();
      }
    };

    events.forEach((event) => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      // Cleanup
      events.forEach((event) => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [user, resetIdleTimeout]); // Removed showIdleWarning dependency

  // Initialize timeout when user logs in
  useEffect(() => {
    if (user) {
      resetIdleTimeout();
    }
  }, [user, resetIdleTimeout]);

  // Cleanup timeouts when component unmounts or user logs out
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
      }
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, []);

  const value = useMemo(() => {
    const register = (username, email, password) => {
      return axios
        .post(`${apiUrl}/v1/auth/register`, {
          name: username,
          email: email,
          password: password,
        })
        .then((response) => {
          setAccessToken(response.data.token);
          setUser(response.data.user);
          startSilentRefresh();
        });
    };

    const login = (email, password, callback = false) => {
      return axios
        .post(`${apiUrl}/v1/auth/login`, {
          email: email,
          password: password,
          timezone: TIME_ZONE,
        })
        .then((response) => {
          const loginTime = new Date();
          window.localStorage.setItem("loginTime", loginTime.getTime());
          handleLoginTime();
          if (response.data.resetPassword === true) {
            setReset(true);
            setAccessToken(response.data.token);
            setUser(response.data.user);
            // startSilentRefresh()
            let config = {};
            config.headers = {};
            config.headers.Authorization = `Bearer ${response.data?.token?.token}`;
            return fetchAfterLogin(
              response.data.token.token,
              response.data.user.roleId
            );
          } else if (!response?.data?.otpSent) {
            setAccessToken(response.data.token);
            setUser(response.data.user);
            // startSilentRefresh()
            let config = {};
            config.headers = {};
            config.headers.Authorization = `Bearer ${response.data.token.token}`;

            return fetchAfterLogin(
              response.data.token.token,
              response.data.user.roleId
            );
          } else {
            callback && callback(response);
          }
        });
    };

    const logout = () => {
      // Prevent duplicate logout calls
      if (isLoggingOutRef.current) {
        return Promise.resolve();
      }

      setIsLoggingOut(true);
      setShowIdleWarning(false);

      // Clear all idle timeouts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
      }
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }

      setAccessToken(null);
      setUser(null);

      return axios
        .post(`${apiUrl}/v1/auth/logout`, {})
        .then((response) => {
          window.localStorage.setItem("logout", moment());
        })
        .catch((err) => {
          console.error("Logout API call failed:", err);
        })
        .finally(() => {
          setIsLoggingOut(false);
        });
    };

    const otpVerificationCall = (email, OTP) => {
      return axios
        .post(`${apiUrl}/v1/auth/verify-otp`, {
          email: email,
          otp: OTP,
          timezone: timezone,
        })
        .then(({ data }) => {
          if (data.resetPassword === true) {
            setReset(true);
            setAccessToken(data.token);
            setUser(data.user);
            // startSilentRefresh()
            let config = {};
            config.headers = {};
            config.headers.Authorization = `Bearer ${data.token.token}`;
            return fetchAfterLogin(data.token.token, data.user.roleId);
          } else {
            setAccessToken(data.token);
            setUser(data.user);
            // startSilentRefresh()
            let config = {};
            config.headers = {};
            config.headers.Authorization = `Bearer ${data.token.token}`;

            return fetchAfterLogin(data.token.token, data.user.roleId);
          }
        });
    };

    const forgotPassword = (email) => {
      return axios.post(`${apiUrl}/v1/auth/forgot-password`, {
        email: email,
      });
    };

    const resetPassword = (password, resetToken) => {
      return axios.post(
        `${apiUrl}/v1/auth/reset-password?token=${resetToken}`,
        {
          password: password,
        }
      );
    };

    const verifyEmail = (emailVerificationToken) => {
      return axios.post(
        `${apiUrl}/v1/auth/verify-email?token=${emailVerificationToken}`,
        {}
      );
    };

    return {
      user,
      setUser,
      register,
      setRoles,
      roles,
      login,
      logout,
      forgotPassword,
      resetPassword,
      verifyEmail,
      otpVerificationCall,
      reset,
      configApiData,
      setConfigApiData,
      storeLoginTime,
      setStoreLoginTime,
      timezone,
      setTimezone,
      setReset,
      // Idle timeout exports
      showIdleWarning,
      idleWarningCountdown,
      stayLoggedIn,
    };
  }, [
    user,
    roles,
    startSilentRefresh,
    reset,
    configApiData,
    storeLoginTime,
    timezone,
    showIdleWarning,
    idleWarningCountdown,
    stayLoggedIn,
    handleLoginTime,
  ]);

  if (!isLoaded) {
    return <ThemedSuspense />;
  }

  // Modal logout handler that uses the same logout function
  const handleModalLogout = () => {
    value.logout();
  };

  return (
    <>
      <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
      {/* Idle Warning Modal - only show when user is logged in */}
      {user && (
        <IdleWarningModal
          isOpen={showIdleWarning}
          countdown={idleWarningCountdown}
          onStayLoggedIn={stayLoggedIn}
          onLogout={handleModalLogout}
        />
      )}
    </>
  );
};
