export function handleDateFormat(inputDate) {
  const dateObject = new Date(inputDate);
  const formattedDate = dateObject
    .toLocaleDateString("en-GB")
    .replace(/\//g, "-");
  const formattedTime = dateObject?.toLocaleTimeString("en-GB");

  const formattedDateTime = `${formattedDate} ${formattedTime}`;

  return formattedDateTime;
}

export function convertToUTC(dateString) {
  const localDate = new Date(dateString);
  if (isNaN(localDate.getTime())) {
    return "Invalid date";
  }
  const year = localDate.getUTCFullYear();
  const month = String(localDate.getUTCMonth() + 1).padStart(2, "0");
  const day = String(localDate.getUTCDate()).padStart(2, "0");
  const hours = String(localDate.getUTCHours()).padStart(2, "0");
  const minutes = String(localDate.getUTCMinutes()).padStart(2, "0");
  const seconds = String(localDate.getUTCSeconds()).padStart(2, "0");

  const utcDateString = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

  return utcDateString;
}

// export const formatDateTime = (dateTimeString) => {
//   const date = new Date(dateTimeString);

//   const options = {
//     day: "2-digit",
//     month: "short",
//     year: "numeric",
//     hour: "2-digit",
//     minute: "2-digit",
//     second: "2-digit",
//   };

//   const formattedDate = date
//     .toLocaleDateString("en-GB", options)
//     .replace(/\//g, " "); // Replace slashes with spaces

//   const formattedTime = date
//     .toLocaleTimeString("en-GB", options)
//     .replace(",", ""); // Remove comma

//   const formattedDateTime = formattedTime;

//   return formattedDateTime;
// };

export function formatDateTime(inputDate) {
  const originalDate = new Date(inputDate);

  const day = ("0" + originalDate.getDate()).slice(-2);
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const monthIndex = originalDate.getMonth();
  const month = monthNames[monthIndex];
  const year = originalDate.getFullYear();
  const hours = ("0" + originalDate.getHours()).slice(-2);
  const minutes = ("0" + originalDate.getMinutes()).slice(-2);
  const seconds = ("0" + originalDate.getSeconds()).slice(-2);

  return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
}
export const encryptPassword = async (password, pemPublicKey) => {
  try {
    // Clean the PEM key to get base64 encoded DER
    const pemHeader = "-----BEGIN PUBLIC KEY-----";
    const pemFooter = "-----END PUBLIC KEY-----";
    const pemContents = pemPublicKey
      .replace(pemHeader, "")
      .replace(pemFooter, "")
      .replace(/\r?\n|\r/g, ""); // Remove line breaks

    // Decode base64 to binary
    const binaryDer = atob(pemContents);
    const binaryDerBuffer = new Uint8Array(binaryDer.length);
    for (let i = 0; i < binaryDer.length; i++) {
      binaryDerBuffer[i] = binaryDer.charCodeAt(i);
    }

    // Import the public key
    const cryptoKey = await window.crypto.subtle.importKey(
      "spki",
      binaryDerBuffer.buffer,
      {
        name: "RSA-OAEP",
        hash: "SHA-256",
      },
      false,
      ["encrypt"]
    );

    // Encode the password
    const encoder = new TextEncoder();
    const encodedPassword = encoder.encode(password);

    // Encrypt
    const encryptedBuffer = await window.crypto.subtle.encrypt(
      {
        name: "RSA-OAEP",
      },
      cryptoKey,
      encodedPassword
    );

    // Convert encrypted ArrayBuffer to base64 string
    const encryptedBytes = new Uint8Array(encryptedBuffer);
    const encryptedBase64 = btoa(String.fromCharCode(...encryptedBytes));

    return encryptedBase64;
  } catch (error) {
    console.error("Encryption failed:", error);
    return null;
  }
};
