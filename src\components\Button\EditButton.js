import React from "react";
import Button from "@mui/material/Button";

const EditButton = ({ onClick, buttonClassName, icon: Icon, ...rest }) => {
  const buttonClasses = `px-3 h-7 w-10 rounded-md ${buttonClassName} ${
    rest.disabled ? "opacity-50 cursor-not-allowed" : ""
  }`;

  const labelClasses = `text-titleColor ml-2 ${
    rest.disabled ? "text-dialogColor" : ""
  }`;

  return (
    <Button
      {...rest}
      style={{ textTransform: "none" }}
      className={buttonClasses}
      onClick={onClick}
      disabled={rest.disabled}
    >
      <div
        className={`flex border border-tabColor h-9 px-3 rounded-md items-center`}
      >
        {Icon && <Icon className="w-3.5 h-3.5" />}
        <span>
          <span className={labelClasses}>{`${rest.label}`}</span>
        </span>
      </div>
    </Button>
  );
};

export default EditButton;
