import React from "react";
import {
  <PERSON>mpo<PERSON><PERSON><PERSON>,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";

const colors = ["#8BD1AE", "#F28F8F", "#EDDF82", "#A1C3FA"];

const PreviewMultiAxis = ({ data }) => {
  function convertDatetimeFormat(datetime) {
    const date = new Date(datetime);
    const formattedDate = date.toLocaleString("en-US", {
      month: "short",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
    return formattedDate.replace(",", "");
  }

  // Convert sampledata to convertedData
  const convertedData = {
    ...data,
    data: data?.data?.map((item) => ({
      ...item,
      Datetime: convertDatetimeFormat(item.Datetime),
    })),
    "Y-Axis": data["Y-Axis"]?.flatMap((axis) =>
      axis?.name?.split(" and ")?.map((name) => ({ name, type: axis.type }))
    ),
  };
  // Calculate min and max values for each key

  const yAxisWidths = convertedData["Y-Axis"]?.map((axis) => {
    const key = axis.name;
    const maxLength = Math.max(
      ...convertedData.data?.map((item) => String(item[key]).length)
    );
    // Adjust the multiplier and other factors as needed to fit your layout
    return maxLength * 6; // Adjust the multiplier as needed
  });
  // console.log("preview-multi", convertedData, "yAxisWidths", yAxisWidths);
  return (
    <ResponsiveContainer width={300} height={160}>
      <ComposedChart
        data={convertedData.data}
        margin={{
          top: 15,
          right: 25,
          left: 0,
        }}
      >
        <CartesianGrid stroke="#f5f5f5" />
        <XAxis
          dataKey="Datetime"
          tick={{ fontSize: 8 }}
          tickCount={3}
          textAnchor="end"
          interval={3}
        />
        <Tooltip contentStyle={{ fontSize: 8, padding: "1px" }} />

        {convertedData["Y-Axis"]?.map((axis, index) => {
          if (axis.type === "Line Graph") {
            return (
              <Line
                type="natural"
                dot={{ strokeWidth: 1.5, r: 0 }}
                strokeWidth={2.5}
                key={index}
                dataKey={axis.name}
                stroke={colors[index]}
                yAxisId={index}
              />
            );
          } else if (axis.type === "Bar Graph") {
            return (
              <Bar
                barSize={20}
                key={axis.name}
                dataKey={axis.name}
                fill={colors[index]}
                yAxisId={index}
                barGap={0}
                barCategoryGap={0}
              />
            );
          }
        })}

        {convertedData["Y-Axis"]?.map((axis, index) => {
          return (
            <YAxis
              tick={{ fontSize: 8 }}
              tickCount={5}
              key={axis.name}
              yAxisId={index}
              orientation={index === 0 ? "left" : "right"}
              stroke={colors[index]}
              width={yAxisWidths[index]}
            />
          );
        })}
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export default PreviewMultiAxis;
