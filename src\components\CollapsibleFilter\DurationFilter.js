import { Formik, Form } from "formik";
import React, { useContext, useEffect, useState } from "react";
import Select from "../FormsUI/Select";
import dayjs from "dayjs";
import weekOfYear from "dayjs/plugin/weekOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
import { DataContext } from "../../context/DataContext";

dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);

function DurationFilter({ durationOption }) {
  const [weekOptions, setWeekOptions] = useState([]);
  const { setDurationTime, durationTime } = useContext(DataContext);

  useEffect(() => {
    if (durationOption === "weekly") {
      const currentYear = dayjs().year();

      const firstWeek = dayjs(`${currentYear}-01-01`).isoWeek();
      const lastWeek = dayjs(`${currentYear}-12-31`).isoWeek();

      const startWeek = firstWeek === 52 || firstWeek === 53 ? 1 : firstWeek;
      const totalWeeks = lastWeek === 1 ? 52 : lastWeek;

      const weeks = Array.from({ length: totalWeeks }, (_, i) => {
        const weekNum = i + 1;

        let weekStart = dayjs()
          .year(currentYear)
          .isoWeek(weekNum)
          .startOf("isoWeek");

        if (weekNum === 1 && weekStart.year() < currentYear) {
          weekStart = weekStart.year(currentYear);
        }

        const startDate = weekStart.format("MMM D");
        const endDate = weekStart.endOf("isoWeek").format("MMM D");

        return {
          value: `Week ${weekNum}`,
          label: `Week ${weekNum} (${startDate} - ${endDate})`,
        };
      });

      setWeekOptions(weeks);
    }
  }, [durationOption]);

  const monthOptions = [
    { value: "January", label: "January" },
    { value: "February", label: "February" },
    { value: "March", label: "March" },
    { value: "April", label: "April" },
    { value: "May", label: "May" },
    { value: "June", label: "June" },
    { value: "July", label: "July" },
    { value: "August", label: "August" },
    { value: "September", label: "September" },
    { value: "October", label: "October" },
    { value: "November", label: "November" },
    { value: "December", label: "December" },
  ];

  const getOptions = () => {
    switch (durationOption) {
      case "weekly":
        return weekOptions;
      case "monthly":
        return monthOptions;
      default:
        return [];
    }
  };

  if (durationOption === "none") {
    return null;
  }

  return (
    <div>
      <Formik
        initialValues={{
          durationPeriod: durationTime ? durationTime : "",
        }}
      >
        {({ values, errors, status }) => (
          <Form>
            <div className="w-[200px]">
              <label className="block text-sm font-medium text-gray-500 mb-1">
                {durationOption === "weekly"
                  ? "Select Week"
                  : durationOption === "monthly"
                  ? "Select Month"
                  : "Duration Period"}
              </label>

              <Select
                name="durationPeriod"
                options={getOptions()}
                placeholder={
                  durationOption === "weekly"
                    ? "Select week"
                    : durationOption === "monthly"
                    ? "Select month"
                    : "Select duration period"
                }
                bgColor="#FFFFFF"
                borderRadius="20px"
                menuListzIndex={9999}
                onChange={(e) => {
                  setDurationTime(e.value);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default DurationFilter;
