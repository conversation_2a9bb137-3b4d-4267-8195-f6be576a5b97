<?xml version="1.0" encoding="iso-8859-1"?>

<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
<svg version="1.0" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"  width="800px"
	 height="800px" viewBox="0 0 24 24" xml:space="preserve">
<g id="Guides">
	<g id="_x32_0_px_2_">
	</g>
	<g id="_x32_0px">
	</g>
	<g id="_x34_0px">
	</g>
	<g id="_x34_4_px">
	</g>
	<g id="_x34_8px">
		<g id="_x31_6px">
		</g>
		<g id="square_4px">
			<g id="_x32_8_px">
				<g id="square_4px_2_">
				</g>
				<g id="square_4px_3_">
				</g>
				<g id="square_4px_1_">
				</g>
				<g id="_x32_4_px_2_">
				</g>
				<g id="_x31_2_px">
				</g>
			</g>
		</g>
	</g>
	<g id="Icons">
	</g>
	<g id="_x32_0_px">
	</g>
	<g id="square_6px">
		<g id="_x31_2_PX">
		</g>
	</g>
	<g id="_x33_6_px">
		<g id="_x33_2_px">
			<g id="_x32_8_px_1_">
				<g id="square_6px_1_">
				</g>
				<g id="_x32_0_px_1_">
					<g id="_x31_2_PX_2_">
					</g>
					<g id="_x34_8_px">
						<g id="_x32_4_px">
						</g>
						<g id="_x32_4_px_1_">
						</g>
					</g>
				</g>
			</g>
		</g>
	</g>
	<g id="_x32_0_px_3_">
	</g>
	<g id="_x32_0_px_4_">
	</g>
	<g id="New_Symbol_8">
		<g id="_x32_4_px_3_">
		</g>
	</g>
</g>
<g id="Artboard">
</g>
<g id="Free_Icons">
	<g>
		
			<line style="fill:none;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="0.5" y1="9.5" x2="7.5" y2="9.5"/>
		
			<line style="fill:none;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="3" y1="12.5" x2="7.5" y2="12.5"/>
		
			<line style="fill:none;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="5.5" y1="15.5" x2="7.5" y2="15.5"/>
		
			<rect x="9.5" y="7.5" style="fill:none;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" width="14" height="11"/>
		<polyline style="fill:none;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" points="9.5,7.5 
			16.5,13.5 23.5,7.5 		"/>
	</g>
</g>
</svg>