import React from "react";
import { CloseIcon } from "../../icons";

function CardData({ card, isDrop, onClose, isCreate, isEdit, isApplied }) {
  function formatValue(value) {
    if (value === null || value === undefined || isNaN(value)) {
      return "0";
    }

    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + "Mn";
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + "k";
    } else {
      return value.toString();
    }
  }

  return (
    <div className="relative">
      <div className="rounded-lg shadow p-2.5 bg-white ">
        {(isCreate || isEdit) && (
          <CloseIcon
            className="w-2 h-2 cursor-pointer ml-auto"
            onClick={(event) => {
              event.stopPropagation();
              event.preventDefault();

              onClose();
            }}
          />
        )}
        <div className="flex justify-between items-center">
          <div className="text-base text-black">{formatValue(card?.value)}</div>
          {!isApplied ? (
            <div className="text-end text-[10px] text-tabColor font-medium">
              Today
            </div>
          ) : null}
        </div>
        <div className="my-2 border-b border-panelBorder" />
        <div className="text-sm text-titleColor font-medium">
          {card?.reportField}
        </div>
      </div>
    </div>
  );
}

export default CardData;
