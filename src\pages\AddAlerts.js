import { lazy, useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import FormHeading from "../components/FormsUI/Heading";
import StepperHeader from "../components/Stepper/StepperHeader";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import { getAlertId } from "../services/alert-api";
import { useLocation } from "react-router-dom";
import { useQuery } from "react-query";
import BreadcrumbNavigation from "../components/BreadCrumps/BreadCrump";

const AddAlertFormOne = lazy(() => import("../Forms/AddAlertFormOne"));
const AddAlertFormTwo = lazy(() => import("../Forms/AddAlertFormTwo"));
const AddAlertFormThree = lazy(() => import("../Forms/AddAlertFormThree"));
const AddAlertFormFour = lazy(() => import("../Forms/AddAlertFormFour"));

const AddAlerts = () => {
  const navigate = useNavigate();
  const [editDetails, setEditDetails] = useState({});
  const location = useLocation();

  const alertId = location.state && location.state.id.id;
  const isEdit = location.state && location.state.isEdit;
  const isCopy = location.state && location.state.isCopy;

  const { currentStep, formData, setFormData, stepCount } =
    useContext(multiStepFormContext);
  const { data: editDetail } = useQuery(["alerts", alertId], getAlertId, {
    enabled: isEdit === true || isCopy === true,
    onSuccess: (resp) => {
      //console.log("resp", resp?.data);
      setEditDetails(resp?.data);
    },
  });
  const getCompletionPercentage = () => {
    //console.log("currentSteptv", stepCount);
    switch (stepCount) {
      case 0:
        return "0% completed";
      case 1:
        return "25% completed";
      case 2:
        return "50% completed";
      case 3:
        return "75% completed";
      case 4:
        return "100% completed";
      default:
        return "";
    }
  };

  const displayStepForms = (currentStep) => {
    // eslint-disable-next-line default-case
    switch (currentStep) {
      case 0:
        return (
          <AddAlertFormOne
            editDetail={editDetails}
            isCopy={isCopy}
          ></AddAlertFormOne>
        );
      case 1:
        return <AddAlertFormTwo editDetail={editDetails}></AddAlertFormTwo>;
      case 2:
        return <AddAlertFormThree editDetail={editDetails}></AddAlertFormThree>;
      case 3:
        return (
          <AddAlertFormFour
            alertId={alertId}
            isEdit={isEdit}
            isCopy={isCopy}
            editDetail={editDetails}
          ></AddAlertFormFour>
        );

      default:
        return <AddAlertFormOne editDetail={editDetails}></AddAlertFormOne>;
    }
  };
  return (
    <>
      <div className="bg-bgPrimary my-4">
        <BreadcrumbNavigation
          linkTwo={"List of Alerts"}
          onlinkTwoClick={() => navigate("/app/alerts")}
          title={isEdit ? "Update Alert" : "Add Alert"}
        />
        <div className="border border-listBorder bg-white pb-5">
          <div className="flex flex-row mx-3 my-3">
            <div>
              <InputLabel
                label={"Enter Details"}
                labelClassName="font-semibold"
              />
            </div>
            <div className="flex-grow flex justify-end items-center">
              <span
                className="font-bold cursor-pointer "
                onClick={() => {
                  navigate("/app/alerts");
                }}
              >
                X
              </span>
            </div>
          </div>
          <div className="mt-3 mx-3 mb-3 border-b-2 border-listBorder" />
          <div className="my-10">
            <StepperHeader atStep={currentStep} module={"alerts"} />
          </div>
          <div className="my-14 border bg-bgTeritary ">
            <div className="flex">
              <div className="w-full h-12 flex items-center justify-center font-hebrew text-base font-bold">
                Step {currentStep + 1}{" "}
              </div>
              <div className="flex-grow flex justify-end items-center mx-10 font-hebrew text-base font-bold whitespace-nowrap">
                {getCompletionPercentage()}
              </div>
            </div>
          </div>
          <div className="mt-[45px]">{displayStepForms(currentStep)}</div>
        </div>
      </div>
    </>
  );
};
export default AddAlerts;
