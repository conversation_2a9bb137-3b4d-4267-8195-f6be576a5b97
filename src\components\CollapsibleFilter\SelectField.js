import { Formik, Form } from "formik";
import React from "react";
import Select from "../FormsUI/Select";
import InputLabel from "../FormsUI/InputLabel/InputLabel";

function SelectField() {
  return (
    <div>
      {" "}
      <Formik
        initialValues={{
          graphType: "",
        }}
      >
        {({ values, errors, status }) => (
          <Form>
            <div className="w-[200px] ">
              <InputLabel
                label={"Visualization Type"}
                labelClassName={"text-xs font-bold"}
              />
              <Select
                name="graphType"
                options={[
                  { value: "Pie Chart", label: "Pie Chart" },
                  { value: "Bar Chart", label: "Bar Chart" },
                  { value: "Line Chart", label: "Line Chart" },
                ]}
                placeholder={"Select visualization type"}
                bgColor="#FFFFFF"
                borderRadius="20px"
                menuListzIndex={9999}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default SelectField;
