import Tooltip, { tooltipClasses } from "@mui/material/Tooltip";
import styled from "@emotion/styled";

export const CssTooltip = styled(({ className, bgColor, color, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme, bgColor, color }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: bgColor || "#000000",
    color: color || "white",
    fontSize: 12,
    // border: "2px solid #7070708C",
    padding: "10px 20px",
    boxShadow: "0px 3px 6px #00000029",
    borderRadius: 10,
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.common.white,
    "&::before": {
      backgroundColor: bgColor || "#000000",
      //boxShadow: "3px 3px 3px 3px #00000029",
      //border: "0.5px solid #7070708C",
    },
  },
}));
