import axios from "axios";
import { config } from "../assets/config/config";
import getAPIMap from "../routes/ApiUrls";

const apiUrl = config.api.url;

export const userService = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  resetPassword,
  updateSwitch,
};

async function getAll(searchstr, pagenum = 1, limit = 10) {
  let url = getAPIMap("user") + `?page=${pagenum}&limit=${limit}`;
  if (searchstr !== "" && searchstr !== undefined) {
    url += `&search=${searchstr}`;
  }

  return await axios.get(url);
}

async function getById(id, user) {
  return await axios.get(getAPIMap("user") + `/${id}`, user);
  // return fetchWrapper.get(`${baseUrl}/${id}`);
}

async function create(user) {
  return await axios.post(getAPIMap("user"), user);
}

async function update(id, user) {
  return await axios.put(getAPIMap("user") + `/${id}`, user);
  // return fetchWrapper.put(`${baseUrl}/${id}`, params);
}

async function updateSwitch(id, status) {
  return await axios.put(getAPIMap("user") + `/${id}`, {
    isUserActive: status,
  });
  // return fetchWrapper.put(`${baseUrl}/${id}`, params);
}

// prefixed with underscored because delete is a reserved word in javascript
async function _delete(id) {
  return await axios.delete(getAPIMap("user") + `/${id}`);
}
async function resetPassword(token, password) {
  let config = {};
  config.headers = {};
  config.headers.Authorization = `Bearer ${token}`;
  return await axios.post(
    `${apiUrl}/v1/auth/reset-password`,
    { token, password },
    config
  );
}
