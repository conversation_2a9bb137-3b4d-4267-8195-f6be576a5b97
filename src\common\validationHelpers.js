import * as Yup from "yup";
import { FILTER_PERIOD_REPORT } from "./constants";

// Helper function for creating validation schema for ReportFilter component
export const createValidationSchema = (
  shouldShowFilter,
  user,
  activeView,
  graphValidation,
  reportName
) => {
  return Yup.object().shape({
    destination_mcc_final: Yup.string()
      .matches(/^[\d]+$/, "Destination MCC must be a number")
      .length(3, "Destination MCC must be 3 digits code")
      .test(
        "not-zero",
        "Destination MCC cannot be 000",
        (value) => value !== "000"
      ),
    destination_mnc_final: Yup.string()
      .matches(/^[\d]+$/, "Destination MNC must be a number")
      .max(3, "Destination MNC must be  between 1 to 3 digits"),
    customer_name: Yup.array().when([], {
      is: () => shouldShowFilter("customer_name") && !user?.isSuperAdmin,
      then: (schema) => schema.min(1, "Customer Name is required"),
      otherwise: (schema) => schema,
    }),
    supplier: Yup.array().when([], {
      is: () => shouldShowFilter("supplier") && !user?.isSuperAdmin,
      then: (schema) => schema.min(1, "Supplier is required"),
      otherwise: (schema) => schema,
    }),
    // Chart validations
    xAxis: Yup.string()
      .when("visualizationType", {
        is: (value) => {
          return (
            ["bar", "scatter", "pie", "gauge", "heat"].includes(value) &&
            activeView === "graph" &&
            graphValidation
          );
        },
        then: (schema) => schema.required("X-axis is required"),
        otherwise: (schema) => schema,
      })
      .test(
        "xAxis-yAxis-different",
        "X-axis cannot be the same as Y-axis",
        function (value) {
          const { visualizationType, yAxis } = this.parent;
          if (
            visualizationType === "heat" &&
            activeView === "graph" &&
            graphValidation &&
            yAxis &&
            value
          ) {
            return yAxis !== value;
          }
          return true;
        }
      ),
    fieldBasedValue: Yup.string().when("visualizationType", {
      is: (value) => {
        return value === "line" && activeView === "graph" && graphValidation;
      },
      then: (schema) => schema.required("Field Based Value is required"),
      otherwise: (schema) => schema,
    }),
    derivedFields: Yup.string().when("visualizationType", {
      is: (value) => {
        return value === "heat" && activeView === "graph" && graphValidation;
      },
      then: (schema) => schema.required("Derived Field is required"),
      otherwise: (schema) => schema,
    }),
    mapColor: Yup.string().when("visualizationType", {
      is: (value) => {
        return value === "heat" && activeView === "graph" && graphValidation;
      },
      then: (schema) => schema.required("Heat Map Color is required"),
      otherwise: (schema) => schema,
    }),
    yAxis: Yup.mixed()
      .test("yAxis-type-required", "Y-axis is required", function (value) {
        const { visualizationType } = this.parent;
        if (
          ["line", "bar", "pie", "multiAxis"].includes(visualizationType) &&
          activeView === "graph" &&
          graphValidation
        ) {
          return Array.isArray(value) && value.length > 0;
        }
        if (
          (visualizationType === "gauge" ||
            visualizationType === "scatter" ||
            visualizationType === "heat") &&
          activeView === "graph" &&
          graphValidation
        ) {
          return typeof value === "string" && value.trim() !== "";
        }
        return true;
      })
      .test(
        "yAxis-xAxis-different",
        "Y-axis cannot be the same as X-axis",
        function (value) {
          const { visualizationType, xAxis } = this.parent;
          if (
            visualizationType === "heat" &&
            activeView === "graph" &&
            graphValidation &&
            xAxis &&
            value
          ) {
            return xAxis !== value;
          }
          return true;
        }
      ),
    bubbleSize: Yup.string().when("visualizationType", {
      is: (value) => {
        return (
          ["scatter"].includes(value) &&
          activeView === "graph" &&
          graphValidation
        );
      },
      then: (schema) => schema.required("Bubble Size is required"),
      otherwise: (schema) => schema,
    }),
    colorBy: Yup.string().when("visualizationType", {
      is: (value) => {
        return (
          ["scatter"].includes(value) &&
          activeView === "graph" &&
          graphValidation
        );
      },
      then: (schema) => schema.required("Color By is required"),
      otherwise: (schema) => schema,
    }),

    timePeriod: Yup.string().when([], {
      is: (value) =>
        reportName === FILTER_PERIOD_REPORT.reportName &&
        activeView === "graph" &&
        value !== "line" &&
        value !== "multiAxis",
      then: (schema) => schema.required("Time Period is required"),
      otherwise: (schema) => schema,
    }),

    configExpectedValue: Yup.number()
      .when("visualizationType", {
        is: (value) =>
          value === "gauge" && activeView === "graph" && graphValidation,
        then: (schema) => schema.required("Expected Value is required"),
        otherwise: (schema) => schema,
      })
      .typeError("Expected Value must be a number")
      .min(1, "Expected Value must be greater than or equal to 1"),
  });
};
