import React, { memo, useContext, useEffect, useState } from "react";
import { Typo<PERSON>, Stack } from "@mui/material";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import { styled } from "@mui/system";
import InputBase from "@mui/material/InputBase";
import { AuthContext } from "../../context/AuthContext";

function ResultPerPageComponent(props) {
  const { limit, handleLimitChange, disabled, colors, pageName } = props;
  //console.log(props);
  const { configApiData } = useContext(AuthContext);
  // console.log("ConfigAP", configApiData);
  const [resultPerPage, setResultPerPage] = useState(
    []
    // Array.from({ length: configApiData?.MAX_PAGE_SIZE }, (_, i) => ({
    //   //configApiData?.MAX_PAGE_SIZE
    //   name: (i + 1).toString(),
    //   values: (i + 1).toString(),
    // }))
  );

  useEffect(() => {
    // let newData = Array.from(
    //   { length: configApiData?.MAX_PAGE_SIZE },
    //   (_, i) => ({
    //     //configApiData?.MAX_PAGE_SIZE
    //     name: (i + 1).toString(),
    //     values: (i + 1).toString(),
    //   })
    // );

    //console.log(newData);
    let newData;
    if (!pageName) {
      newData = [
        { name: "10", values: "10" },
        { name: "30", values: "30" },
        { name: "50", values: "50" },
        { name: "100", values: "100" },
      ];
    } else {
      if (pageName === "CDRSearch") {
        newData = [
          { name: "100", values: "100" },
          { name: "500", values: "500" },
          { name: "1000", values: "1000" },
        ];
      } else {
        newData = [
          { name: "100", values: "100" },
          { name: "500", values: "500" },
          { name: "1000", values: "1000" },
          // { name: "5000", values: "5000" },
          // { name: "10000", values: "10000" },
        ];
      }
    }
    setResultPerPage(newData);
  }, [configApiData]);

  const PaginationDropdownInput = styled(InputBase)(({ theme }) => ({
    "& .MuiInputBase-root": { lineHeight: "-1.5625em" },
    "& .MuiInputBase-input": {
      height: "10px",
      width: "10px",
      opacity: 1,
      fontWeight: 500,
      color: `#707070`, // "#292929",
      //  borderRadius: "3px",
      background: "#fff",
      //  border: "1px solid #A9B0BC",
      border: "1px solid #D9D9D9",

      // "&:focus": {
      //   border: "1px solid #A9B0BC",
      // },
    },
    "& .MuiOutlinedInput-notchedOutline": {
      border: "1.8px solid #909bb8",
    },
    "& .MuiOutlinedInput-root": {
      "&:hover fieldset": {
        borderColor: "#909bb8",
      },
      "& fieldset": {
        borderColor: "1.8px solid #909bb8", // Set border color for default state
        "&.Mui-focused": {
          borderColor: "#909bb8", // Set border color for focused state
        },
      },
    },

    "& .MuiSvgIcon-root": {
      // color: "#A1A1A1",
      color: `${colors?.tpBlue}`,
    },

    [theme.breakpoints.down("md")]: {
      "& .MuiInputBase-input": {
        padding: "7px 0px 7px 10px",
        width: "30px",
        fontSize: "14px",
      },
    },
    [theme.breakpoints.up("md")]: {
      "& .MuiInputBase-input": {
        padding: "7px 0px 7px 10px",
        width: "30px",
        fontSize: "14px",
      },
    },
    [theme.breakpoints.up("xl")]: {
      "& .MuiInputBase-input": {
        padding: "7px 0px 7px 10px",
        width: "30px",
        fontSize: "14px",
      },
    },
  }));

  return (
    <Stack
      direction="row"
      spacing="10px"
      justifyContent="space-between"
      alignItems="center"
    >
      <Typography
        style={{
          textAlign: "left",
          opacity: 1,
          color: "#707070",
          fontSize: "14px",
          fontWeight: 500,
          lineHeight: "19px",
          // fontFamily: "Open Sans Hebrew",
        }}
      >
        Rows&nbsp;per&nbsp;page&nbsp;:
      </Typography>
      <FormControl sx={{ Width: "45px", Height: "19px" }}>
        <Select
          value={limit}
          onChange={handleLimitChange}
          disabled={disabled}
          displayEmpty
          inputProps={{ "aria-label": "Without label" }}
          input={<PaginationDropdownInput />}
          MenuProps={{
            disableScrollLock: true,
            PaperProps: {
              sx: {
                fontSize: "14px",
                color: `#707070`, // "#000000",
                lineHeight: "18px",
                fontWeight: 400,
                // marginTop: "-8px",
                width: "70px", // 70px,
                minWidth: "40px !important",
                boxShadow: "0px 3px 6px #00000029",
                border: "1px solid #E5E5E5",
                borderRadius: "6px",
                opacity: 1,
              },
            },
          }}
        >
          {resultPerPage?.map((e, i) => {
            return (
              <MenuItem value={e.values} key={i}>
                {e.name}
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>
    </Stack>
  );
}

export default memo(ResultPerPageComponent);
