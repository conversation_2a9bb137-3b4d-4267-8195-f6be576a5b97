{"name": "smshub", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/icons-material": "^5.15.7", "@mui/lab": "^5.0.0-alpha.166", "@mui/material": "^5.15.19", "@mui/x-date-pickers": "^7.28.3", "@nivo/pie": "^0.88.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@types/d3": "^7.4.3", "@windmill/react-ui": "^0.6.0", "antd": "^5.11.3", "axios": "^1.4.0", "bootstrap": "^5.3.2", "chart.js": "^4.4.1", "chartjs-plugin-datalabels": "^2.2.0", "country-state-city": "^3.2.1", "d3": "^7.8.5", "dayjs": "^1.11.10", "formik": "^2.2.9", "i18next": "^22.5.0", "i18next-browser-languagedetector": "^7.0.1", "joi": "^17.11.0", "libphonenumber-js": "^1.10.54", "material-react-table": "^3.2.1", "memoize": "^10.0.0", "moment": "^2.29.4", "moment-timezone": "^0.5.46", "otp-input-react": "^0.3.0", "react": "^18.2.0", "react-avatar": "^5.0.3", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.9.1", "react-chartjs-2": "^5.2.0", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-grid-layout": "^1.4.4", "react-i18next": "^12.3.1", "react-icons": "^5.0.1", "react-query": "^3.39.3", "react-router-dom": "^6.19.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-simple-toasts": "^5.10.0", "react-switch": "^7.0.0", "react-table": "^7.8.0", "react-timer-hook": "^3.0.7", "react-to-pdf": "^2.0.0", "react-toastify": "^10.0.4", "react-window": "^1.8.10", "recharts": "^2.11.0", "styled-components": "^6.1.8", "tailwind-merge": "^2.2.1", "web-vitals": "^2.1.4", "yup": "^1.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/user-event": "^14.5.2", "eslint-config-prettier": "^8.8.0", "prettier": "2.8.8", "tailwindcss": "^3.3.2"}}