import { useContext, useState } from "react";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import { Formik, Form } from "formik";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import BackButton from "../components/Button/Button";
import Button from "../components/Button/OutlinedButton";
import { useQuery } from "react-query";
import {
  customerList,
  destinationName,
  supplierList,
} from "../services/dropdown-api";
import { DataContext } from "../context/DataContext";
import CustomDropDown from "../components/Dropdown/CustomeDropdown";
import * as Yup from "yup";
import { getNodeList } from "../services/alert-api";

const AddAlertFormTwo = ({ editDetail }) => {
  const {
    handleNextClick,
    setFormData,
    formData,
    handleNextClickStep,
    handlePrevClick,
    handlePrevStep,
  } = useContext(multiStepFormContext);

  const [customerListData, setCustomerListData] = useState([]);
  const [supplierListData, setSupplierListData] = useState([]);
  const [nodeData, setNodeData] = useState();

  const { setAlertDetails, destinationCountryList, setDestinationCountryList } =
    useContext(DataContext);

  // Fetch Supplier List
  useQuery(["/supplierData"], supplierList, {
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      // Create a Set to store unique names
      const seen = new Set();

      const uniqueData = data.filter((item) => {
        if (seen.has(item.name)) return false;
        seen.add(item.name);
        return true;
      });

      const supplierOptions = [
        {
          value: "Select All",
          label: "Select All",
        },
        ...uniqueData.map((element) => ({
          value: element.name,
          label: element.name,
        })),
      ];

      setSupplierListData(supplierOptions);
    },
  });

  // Fetch Customer List
  useQuery(["/customerData"], customerList, {
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      const seen = new Set();

      // const uniqueCustomers = data.filter((item) => {
      //   if (seen.has(item.name)) return false;
      //   seen.add(item.name);
      //   return true;
      // });

      const cust = [
        { value: "Select All", label: "Select All" },
        ...data.map((element) => ({
          value: element.name,
          label: element.label,
        })),
      ];

      setCustomerListData(cust);
      setFormData((prev) => ({ ...prev, customerListData: cust }));
    },
  });

  //node-list
  useQuery(["/node-list"], getNodeList, {
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      const cust = [
        {
          value: "Select All",
          label: "Select All",
        },
        ...data.map((element) => ({
          value: element.value,
          label: element.label,
        })),
      ];
      setNodeData(cust);
    },
  });

  useQuery(["/destinationCountryData"], destinationName, {
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      const uniqueCountries = [...new Set(data)];

      const destination = [
        { value: "Select All", label: "Select All" },
        ...uniqueCountries.map((element) => ({
          value: element,
          label: element,
        })),
      ];

      setDestinationCountryList(destination);
    },
  });

  const initialValues = {};

  const { customers, suppliers, destinations } = editDetail?.filters || {};

  const dropdownFields = formData?.filterType?.split(/[- ]/) || [];

  dropdownFields.forEach((field) => {
    if (field === "Customer") {
      initialValues["Customer"] = formData?.Customer || customers || [];
    }
    if (field === "Supplier") {
      initialValues["Supplier"] = formData?.Supplier || suppliers || [];
    }
    if (field === "Destination") {
      initialValues["Destination"] =
        formData?.Destination || destinations || [];
    }
  });

  // Include "Node" only if alertType is "Error"
  if (formData?.alertType === "Error") {
    initialValues["node"] = formData?.node || editDetail?.filters?.node || [];
  }

  const validationSchema = Yup.object().shape(
    dropdownFields.reduce(
      (schema, field) => {
        if (field === "Customer") {
          schema["Customer"] = Yup.array()
            .min(1, "Customer is required")
            .required("Customer is required");
        }
        if (field === "Supplier") {
          schema["Supplier"] = Yup.array()
            .min(1, "Supplier is required")
            .required("Supplier is required");
        }
        if (field === "Destination") {
          schema["Destination"] = Yup.array()
            .min(1, "Destination is required")
            .required("Destination is required");
        }
        return schema;
      },
      formData?.alertType === "Error"
        ? {
            node: Yup.array()
              .min(1, "Node is required")
              .required("Node is required"),
          }
        : {}
    )
  );

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        enableReinitialize={true}
        onSubmit={(values) => {
          const keys = Object.keys(values);
          setAlertDetails((prevDetails) => {
            const newDetails = [...prevDetails];
            newDetails[1] = keys.map((key) => key);
            return newDetails;
          });
          handleNextClick();
          handleNextClickStep();
          const newData = { ...formData, ...values };
          setFormData(newData);
        }}
      >
        {({ values, errors, touched, setFieldValue }) => (
          <Form>
            <div className="flex flex-col items-center justify-center mb-4 mx-10 md:mx-0">
              {dropdownFields.map((field, index) => (
                <div className="w-full md:w-[500px] mt-10" key={index}>
                  <InputLabel label={field} isMandatory={true} />
                  <CustomDropDown
                    btnWidth="w-full lg:w-[500px]"
                    data={
                      field === "Customer"
                        ? customerListData
                        : field === "Supplier"
                        ? supplierListData
                        : destinationCountryList
                    }
                    btnName={`Select ${field} name`}
                    onSelectionChange={(selectedDetails) => {
                      const filteredSelection = selectedDetails.filter(
                        (item) => item !== "Select All"
                      );
                      setFieldValue(field, filteredSelection);
                    }}
                    defaultSelectedData={
                      field === "Customer"
                        ? formData?.Customer || customers || []
                        : field === "Supplier"
                        ? formData?.Supplier || suppliers || []
                        : field === "Destination"
                        ? formData?.Destination || destinations || []
                        : []
                    }
                    isUser={true}
                    isDuplicate={true}
                    maximumLength="350"
                  />
                  {errors[field] &&
                    touched[field] && ( // Use field instead of "field"
                      <div className="text-errorColor text-xs">
                        {errors[field]}
                      </div>
                    )}
                </div>
              ))}
              {formData?.alertType === "Error" ? (
                <div className="w-full md:w-[500px] mt-10">
                  <InputLabel label={"Node"} isMandatory={true} />
                  <CustomDropDown
                    btnWidth="w-full lg:w-[500px]"
                    data={nodeData}
                    btnName={`Select Node`}
                    onSelectionChange={(selectedDetails) => {
                      const filteredSelection = selectedDetails.filter(
                        (item) => item !== "Select All"
                      );
                      setFieldValue("node", filteredSelection);
                    }}
                    defaultSelectedData={
                      formData["node"] || initialValues["node"]
                    }
                    isMultiSelect={true}
                    isUser={true}
                  />
                  {errors.node && touched.node && (
                    <div className="text-errorColor text-xs">{errors.node}</div>
                  )}
                </div>
              ) : null}
            </div>
            <div className="flex-grow flex justify-end items-center mx-20 mt-20 mb-20 gap-4">
              <BackButton
                label="Back"
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded"
                onClick={() => {
                  handlePrevClick();
                  handlePrevStep();
                }}
              />
              <Button
                block="true"
                type="submit"
                label="Next"
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded"
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default AddAlertFormTwo;
