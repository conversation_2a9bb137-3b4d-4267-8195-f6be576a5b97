import React, { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import GraphImg from "../../assets/img/image (2).png";
import { reportService } from "../../services/staticreport.service";
import {
  TableContainer,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
} from "@windmill/react-ui";

const data = [
  { name: "A", value: 10 },
  { name: "B", value: 20 },
  { name: "C", value: 30 },
  { name: "D", value: 40 },
  { name: "E", value: 50 },
];

const spec = {
  mark: "bar",
  encoding: {
    x: { field: "a", type: "ordinal" },
    y: { field: "b", type: "quantitative" },
  },
};
// <BarChart data={data}/>
let subGraphs = [
  {
    name: "Customer wise destination wise traffic report",
    displayName: "Customer wise destination wise Traffic Report",
    isActive: true,
    cols: [
      { name: "No.", field: "" },
      { name: "Date", field: "timestamp" },
      { name: "Customer", field: "customer_name" },
      { name: "Customer Bind", field: "customer_bind" },
      { name: "Destination", field: "destination" },
      { name: "Protocol", field: "protocol" },
      { name: "Total Submission", field: "total_submitted" },
      { name: "Submission Success", field: "total_success" },
      { name: "Submission Error", field: "submission_error" },
      { name: "Delivery Success", field: "" },
      { name: "Delivery Failure", field: "delivery_failure" },
    ],
  },
  {
    name: "Customer wise destination wise error report",
    displayName: "Customer wise destination wise Error Report",
    isActive: true,
    cols: [
      { name: "No.", field: "" },
      { name: "Date", field: "timestamp" },
      { name: "Customer", field: "customer_name" },
      { name: "Customer Bind", field: "customer_bind" },
      { name: "Destination", field: "destination" },
      { name: "Traffic Type", field: "traffic_type" },
      { name: "Status", field: "status" },
      { name: "Count", field: "number_of_records" },
    ],
  },
  {
    name: "",
    displayName: "",
    isActive: false,
  },
  {
    name: "",
    displayName: "",
    isActive: false,
  },
  {
    name: "",
    displayName: "",
    isActive: false,
  },
  {
    name: "",
    displayName: "",
    isActive: false,
  },
  {
    name: "",
    displayName: "",
    isActive: false,
  },
  {
    name: "",
    displayName: "",
    isActive: false,
  },
];

function HubTrafic() {
  const [showOverlay, setShowOverlay] = useState(false);
  const [selectedGraph, setselectedGraph] = useState({});
  const [cols, setcols] = useState([]);
  const [data, setdata] = useState([]);
  useEffect(() => {
    //console.log(selectedGraph);
    if (selectedGraph !== "") {
      reportService
        .getReport(selectedGraph.name)
        .then((res) => {
          setdata(res.data);
          //console.log(res.data[0])
          const colsArray = [];
          colsArray.push({ name: "No.", field: "" });
          Object.keys(res.data[0]).map((col) => {
            colsArray.push({ name: col, field: col });
          });
          setcols(colsArray);
          //console.log(cols);
        })
        .catch((err) => {
          //console.log(err)
        });
    }
  }, [selectedGraph]);

  subGraphs = subGraphs.filter((graph) => graph.displayName !== "");
  return (
    <>
      {!showOverlay && (
        <>
          <div>
            {subGraphs.map((graph, index) => (
              <div>
                <Card
                  style={{
                    width: "23%",
                    float: "left",
                    margin: "1%",
                    height: "250px",
                  }}
                  onClick={() => {
                    setShowOverlay(true);
                    setcols(
                      subGraphs.filter((agraph) => agraph.name == graph.name)[0]
                        .cols
                    );
                    //console.log(cols)
                    setselectedGraph(graph);
                  }}
                >
                  <Card.Img
                    variant="top"
                    src={GraphImg}
                    style={{ height: "150px" }}
                  />
                  <Card.Body style={{ background: "#707070" }}>
                    <Card.Title style={{ color: "white" }}>
                      {graph.displayName}
                    </Card.Title>
                    {/* <Card.Text>
              Some quick example text to build on the card title and make up the
              bulk of the card's content.
            </Card.Text> */}
                  </Card.Body>
                </Card>
              </div>
            ))}
          </div>
        </>
      )}

      {showOverlay && (
        <div>
          <div>
            <h1 style={{ width: "98%", float: "left" }}>
              {selectedGraph.displayName}
            </h1>
            <span
              style={{ width: "2%", float: "left", cursor: "pointer" }}
              onClick={() => setShowOverlay(false)}
            >
              <b>x</b>
            </span>
          </div>
          <TableContainer>
            <Table className="table table-auto">
              <TableHeader>
                <TableRow>
                  {cols &&
                    cols.map((col) => (
                      <TableCell style={{ width: "10%" }}>{col.name}</TableCell>
                    ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {data &&
                  data.map((d, ind) => (
                    <TableRow key={ind}>
                      {cols &&
                        cols.map((col, cinde) => {
                          return col.name === "No." ? (
                            <TableCell style={{ width: "10%" }} key={cinde}>
                              {ind + 1}
                            </TableCell>
                          ) : (
                            <TableCell style={{ width: "10%" }} key={cinde}>
                              {d[col.field]}
                            </TableCell>
                          );
                        })}
                    </TableRow>
                  ))}
                {!data && (
                  <TableRow>
                    <TableCell colSpan="4" className="text-center">
                      <div className="spinner-border spinner-border-lg align-center"></div>
                    </TableCell>
                  </TableRow>
                )}
                {data && !data.length && (
                  <TableRow>
                    <TableCell colSpan="4" className="text-center">
                      <div className="p-2">No Users To Display</div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </div>
      )}
    </>
  );
}

export default HubTrafic;
