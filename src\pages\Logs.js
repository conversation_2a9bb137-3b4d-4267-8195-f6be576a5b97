import React, { useMemo, useState, useEffect, useContext } from "react";
import Table from "../components/table/ReportTable";
import Pagination from "../components/Pagination/Pagination";
import { LogService } from "../services/logsService.service";
import Button from "../components/Button/OutlinedButton";
import ExportPopup from "../popups/exportpopup";
import SuccessDialog from "../popups/SuccessDialog";
import { SearchhIcon } from "../icons";
import Calendar from "../components/DatePicker/AuditCalendar";
import dayjs from "dayjs";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { DataContext } from "../context/DataContext";
import Title from "../Title";
import Select from "../components/FormsUI/Select";
import { useQuery } from "react-query";
import { Formik, Form } from "formik";
import theme from "../tailwind-theme";
import { formatDate } from "../utils/fileDateFormator";
import { CssTooltip } from "../components/StyledComponent";

function Logs() {
  const [logTableData, setLogTableData] = useState([]);
  const [respData, setRespData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRoleOption, setSelectedRoleOption] = useState();
  const [selectedEventOption, setSelectedEventOption] = useState();
  const [eventsList, setEventsList] = useState([]);
  const [rolesList, setRolesList] = useState([]);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [download, setDownload] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("PDF");
  const { resultPerPage } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(100);
  const [searchInput, setSearchInput] = useState("");
  const [selectedFilter, setSelectedFilter] = useState({
    startDate: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
    endDate: dayjs().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
  });
  const [selectedRange, setSelectedRange] = useState("This Month");

  useQuery(["EventList"], () => LogService.getEventDetails(), {
    onSuccess: (data) => {
      const options = data.data.map((option) => ({
        label: option,
        // value: option.toLowerCase().replace(/\s+/g, "_"),
        value: option,
      }));
      const defaultOption = { label: "All Events", value: "" };
      const convertedOptions = [defaultOption, ...options];
      setEventsList(convertedOptions);
    },
  });

  useQuery(["RoleList"], () => LogService.getRoleDetails(), {
    onSuccess: (data) => {
      const options = data.data.map((option) => ({
        label: option,
        value: option.toLowerCase().replace(/\s+/g, "_"),
      }));
      const defaultOption = { label: "All Roles", value: "" };
      const convertedOptions = [defaultOption, ...options];
      setRolesList(convertedOptions);
    },
  });

  const { isLoading } = useQuery(
    [
      "LogDetails",
      currentPage,
      limitPerPage,
      searchInput,
      selectedEventOption,
      selectedRoleOption,
      selectedFilter.startDate,
      selectedFilter.endDate,
      download,
      downloadType,
    ],
    LogService.getLogDetails,
    {
      onSuccess: (data) => {
        if (download === true) {
          let extension =
            downloadType === "PDF"
              ? ".pdf"
              : downloadType === "EXCEL"
              ? ".xlsx"
              : ".csv";
          const url = URL.createObjectURL(data.data);
          const link = document.createElement("a");
          link.href = url;
          const startDate = selectedFilter.startDate.split(" ")[0];
          const endDate = selectedFilter.endDate.split(" ")[0];
          const currentTime = formatDate();
          const filename = `Logs_${startDate} - ${endDate} ${currentTime} ${extension}`;
          link.download = filename;
          link.click();
          setDownload(false);
          setDownloadType("");
        } else {
          let newData = data?.data?.data?.map((x) => {
            return {
              action: x.action,
              createdAt: x.createdAt,
              event: x.event,
              id: x.id,
              roleName: x.roleName,
              //updatedAt: formatDateTime(x.updatedAt),
              updatedAt: x.updatedAt,
              userId: x.userId,
              username: x.username,
            };
          });
          setLogTableData(newData);
          setRespData(data);
        }
      },
    }
  );

  function handlePageChange(page) {
    setCurrentPage(page);
  }

  const columns = useMemo(
    () => [
      {
        header: "Timestamp",
        accessorKey: "updatedAt",
        size: 120,
      },
      {
        header: "Username",
        accessorKey: "username",
        size: 100,
        Cell: ({ row }) => (
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <div>
              <CssTooltip title={row?.original?.username} placement="top" arrow>
                <p>
                  {row?.original?.username
                    ? row?.original?.username.length > 20
                      ? `${row?.original?.username.slice(0, 20)}...`
                      : row?.original?.username
                    : "-"}
                </p>
              </CssTooltip>
            </div>
          </div>
        ),
      },
      {
        header: "Role",
        accessorKey: "roleName",
        size: 120,
      },
      {
        header: "Event",
        accessorKey: "event",
        size: 100,
      },
      {
        header: "Activity",
        accessorKey: "action",
        size: 140,
      },
    ],
    []
  );

  const handleLimitChange = (e) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
  };
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedFilter]);

  return (
    <>
      <div className="bg-bgPrimary my-5 ">
        <div className="mt-10">
          <Title title={"Logs"} />
        </div>
        <div className="border border-listBorder bg-white p-3 mt-10">
          <div className="mx-3">
            <Formik initialValues={{ eventType: "", roleType: "" }}>
              <Form>
                <div className="flex gap-4 mt-7">
                  <div className="">
                    <input
                      type="text"
                      style={{
                        border: `1px solid ${theme.borderColor.outerBorder}`,
                        paddingLeft: "2.5rem",
                      }}
                      className="min-w-[250px] px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                      placeholder="Search Logs"
                      value={searchInput}
                      onChange={(e) => {
                        setSearchInput(e.target.value);
                        setCurrentPage(1);
                      }}
                    />
                    <div
                      className=""
                      style={{ marginLeft: "0.8rem", marginTop: "-1.84rem" }}
                    >
                      <SearchhIcon className="w-4 h-4" />
                    </div>
                  </div>
                  <div className="w-full md:w-[200px] ">
                    <Select
                      name="eventType"
                      options={eventsList}
                      placeholder={"Select Events"}
                      onChange={(selectedOption) => {
                        const data = selectedOption.value;
                        //console.log("data123", selectedOption);
                        setSelectedEventOption(data);
                        setCurrentPage(1);
                      }}
                      isSearchable={true}
                      menuListzIndex={9999}
                    />
                  </div>
                  <div className="w-full md:w-[200px] ">
                    <Select
                      name="roleType"
                      options={rolesList}
                      placeholder={"Select Roles"}
                      onChange={(selectedOption) => {
                        const data = selectedOption.value;
                        //console.log("data123", data);
                        setSelectedRoleOption(data);
                        setCurrentPage(1);
                      }}
                      isSearchable={true}
                      menuListzIndex={9999}
                    />
                  </div>
                  <div className="">
                    <Calendar
                      setSelectedFilter={setSelectedFilter}
                      setSelectedRange={setSelectedRange}
                      selectedRange={selectedRange}
                    />
                  </div>
                  <div>
                    <Button
                      label={"Download"}
                      buttonClassName="text-xs w-36 text-white h-10  rounded-md"
                      onClick={() => {
                        setShowExportConfirmation(true);
                      }}
                    />
                  </div>
                </div>
              </Form>
            </Formik>
            <div className="mt-5">
              <Table
                columns={columns}
                data={Array.isArray(logTableData) ? logTableData : []}
                isLoading={isLoading}
              />

              {respData?.data && respData?.data?.totalCount !== undefined && (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    // justifyContent: "flex-start",
                    marginTop: "20px",
                  }}
                >
                  <div className="flex ">
                    <div>
                      {" "}
                      <ResultPerPageComponent
                        countPerPage={resultPerPage}
                        limit={limitPerPage}
                        handleLimitChange={handleLimitChange}
                        pageName={"reports"}
                      />
                    </div>

                    <div
                      style={{
                        display: "flex",
                        fontSize: "14px",
                        padding: "10px 0px 0px 10px",
                        color: theme.textColor.titleColor,
                      }}
                    >
                      {(currentPage - 1) * limitPerPage + 1} -{" "}
                      {Math.min(
                        limitPerPage * currentPage,
                        respData?.data?.totalCount
                      )}{" "}
                      of {respData?.data?.totalCount} rows
                    </div>
                  </div>
                  <div>
                    <Pagination
                      className="pagination-bar"
                      currentPage={currentPage}
                      totalCount={respData?.data?.totalCount}
                      pageSize={limitPerPage}
                      onPageChange={(page) => {
                        handlePageChange(page);
                      }}
                    />{" "}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <ExportPopup
        show={showExportConfirmation}
        onHide={() => setShowExportConfirmation(false)}
        onConfirm={(type) => {
          setDownload(true);
          setShowExportConfirmation(false);
          setDownloadType(type);
        }}
        title={"Export"}
        identity={"Logs"}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => setSuccessDialog(false)}
        message={"Audit log successfully downloaded."}
      />{" "}
    </>
  );
}

export default Logs;
