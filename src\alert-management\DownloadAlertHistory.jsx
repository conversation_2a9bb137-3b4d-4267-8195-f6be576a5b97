import React, { useState } from "react";
import Dialog from "@mui/material/Dialog";
import Box from "@mui/material/Box";
import { CloseIcon } from "../icons";
import { FormControlLabel, Radio, RadioGroup } from "@mui/material";
import CommonButton from "../components/Button/CommonButton";

const radioGroupOptions = [
  { label: "CSV", value: "csv" },
  { label: "XLSX", value: "xlsx" },
  { label: "PDF", value: "pdf" },
];
const DEFAULT_TYPE = "csv";

export default function DownloadAlertHistory(props) {
  const { handleClose, title, handleAction } = props;

  const [downloadType, setDownloadType] = useState(DEFAULT_TYPE);
  const handleOk = () => handleAction(downloadType);

  return (
    <Box width={"100%"}>
      <Dialog
        open={true}
        fullWidth
        sx={{
          "& .MuiDialog-container": {
            "& .MuiPaper-root": {
              width: "100%",
              maxWidth: "450px",
              margin: 0,
            },
          },
        }}
      >
        <div className="mx-4  mb-5">
          <div className=" mt-4 text-black text-base font-medium flex items-center justify-between">
            {title}
            <CloseIcon
              onClick={handleClose}
              className=" w-2.5 h-2.5 cursor-pointer"
            />
          </div>
          <div>
            <h2>{"Select Type"}</h2>
            <RadioGroup
              row
              value={downloadType}
              onChange={(e) => {
                setDownloadType(e.target.value);
              }}
            >
              {radioGroupOptions.map(({ label, value }) => (
                <GroupOptionsComponent
                  label={label}
                  value={value}
                  key={label}
                />
              ))}
            </RadioGroup>
          </div>

          <footer>
            <CommonButton onClick={handleOk}>Ok</CommonButton>
          </footer>
        </div>
      </Dialog>
    </Box>
  );
}

const GroupOptionsComponent = ({ label, value }) => {
  return (
    <FormControlLabel
      value={value}
      control={
        <Radio
          sx={{
            "& .MuiSvgIcon-root": {
              fontSize: "16px",
            },
            "&.Mui-checked .MuiSvgIcon-root": {
              color: "black",
            },
          }}
        />
      }
      label={<span className="text-headingColor text-sm">{label}</span>}
    />
  );
};
