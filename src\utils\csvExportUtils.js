/**
 * CSV Export Utility Functions
 * This module provides utilities for exporting data to CSV format
 */

/**
 * Converts array of objects to CSV content string
 * @param {Array} data - Array of objects to convert to CSV
 * @param {Object} options - Optional configuration
 * @param {Array<string>} options.customHeaders - Custom header labels to use instead of object keys
 * @param {string} options.title - Optional title to add at the top of the CSV
 * @returns {string} CSV content string
 */
export const convertToCSV = (data, options = {}) => {
  if (!data || data.length === 0) {
    throw new Error("No data available for CSV conversion");
  }

  const { customHeaders, title } = options;

  // Extract headers from the first object
  const headers = Object.keys(data[0]);

  // Use custom headers if provided, otherwise use object keys
  const displayHeaders =
    customHeaders && customHeaders.length === headers.length
      ? customHeaders
      : headers;

  const csvHeaders = displayHeaders.join(",");

  // Convert each row to CSV format
  const csvRows = data.map((row) =>
    headers
      .map((header) => {
        const value = row[header];

        // Handle null or undefined values
        if (value === null || value === undefined) {
          return "";
        }

        // Convert to string and handle special characters
        const stringValue = String(value);

        // If value contains comma, quote, or newline, wrap in quotes and escape existing quotes
        if (
          stringValue.includes(",") ||
          stringValue.includes('"') ||
          stringValue.includes("\n")
        ) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }

        return stringValue;
      })
      .join(",")
  );

  // Build CSV content
  const csvContent = [csvHeaders, ...csvRows];

  // Add title at the top if provided
  if (title) {
    const titleRow = `"${title.replace(/"/g, '""')}"`;
    const emptyRow = ""; // Empty row for spacing
    csvContent.unshift(emptyRow, titleRow);
  }

  return csvContent.join("\n");
};

/**
 * Creates and downloads a CSV file
 * @param {string} csvContent - The CSV content string
 * @param {string} filename - The filename for the download
 */
export const downloadCSV = (csvContent, filename) => {
  try {
    // Create blob with CSV content
    const blob = new Blob([csvContent], {
      type: "text/csv;charset=utf-8;",
    });

    // Create download link
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);

    link.href = url;
    link.download = filename.endsWith(".csv") ? filename : `${filename}.csv`;

    // Trigger download
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    throw new Error(`Failed to download CSV: ${error.message}`);
  }
};

/**
 * Exports data to CSV with error handling and loading states
 * @param {Object} params - Export parameters
 * @param {Array} params.data - Data to export
 * @param {string} params.filename - Filename for the export
 * @param {Object} params.options - Optional CSV formatting options
 * @param {Array<string>} params.options.customHeaders - Custom header labels
 * @param {string} params.options.title - Optional title for the CSV
 * @param {Function} params.onStart - Callback when export starts
 * @param {Function} params.onSuccess - Callback when export succeeds
 * @param {Function} params.onError - Callback when export fails
 */
export const exportDataToCSV = ({
  data,
  filename,
  options = {},
  onStart,
  onSuccess,
  onError,
}) => {
  // Start loading state
  if (onStart) onStart();

  try {
    // Validate data
    if (!data || data.length === 0) {
      throw new Error("No data available for CSV export");
    }

    // Convert to CSV with options
    const csvContent = convertToCSV(data, options);

    // Download file
    downloadCSV(csvContent, filename);

    // Success callback
    if (onSuccess) onSuccess();
  } catch (error) {
    // Error callback
    if (onError) onError(error.message);
  }
};
