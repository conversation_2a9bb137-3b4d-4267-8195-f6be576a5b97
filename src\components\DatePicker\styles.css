.react-datepicker__header {
  background-color: white;
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
  margin-top: 0;
  color: #707070;
  font-weight: bold;
  font-size: 12px;
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  color: #000;
  display: inline-block;
  width: 1.5rem;
  line-height: 1.7rem;
  text-align: center;
  margin: 0.2rem;
  font-size: 12px;
}
.react-datepicker__day--in-range {
  border-radius: 0.1rem;
  background-color: rgba(210, 46, 41, 0.5);
  color: black;
  border-radius: 0.3rem;
}
/* // #eb1d160d; */
.react-datepicker__day--in-selecting-range:not(
    .react-datepicker__day--in-range,
    .react-datepicker__month-text--in-range,
    .react-datepicker__quarter-text--in-range,
    .react-datepicker__year-text--in-range
  ),
.react-datepicker__month-text--in-selecting-range:not(
    .react-datepicker__day--in-range,
    .react-datepicker__month-text--in-range,
    .react-datepicker__quarter-text--in-range,
    .react-datepicker__year-text--in-range
  ),
.react-datepicker__quarter-text--in-selecting-range:not(
    .react-datepicker__day--in-range,
    .react-datepicker__month-text--in-range,
    .react-datepicker__quarter-text--in-range,
    .react-datepicker__year-text--in-range
  ),
.react-datepicker__year-text--in-selecting-range:not(
    .react-datepicker__day--in-range,
    .react-datepicker__month-text--in-range,
    .react-datepicker__quarter-text--in-range,
    .react-datepicker__year-text--in-range
  ) {
  background-color: rgba(210, 46, 41, 0.5);
}
.react-datepicker__day:hover {
  background-color: rgba(210, 46, 41, 0.5);
  color: black;
}
.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--keyboard-selected,
.react-datepicker__quarter-text--keyboard-selected,
.react-datepicker__year-text--keyboard-selected,
.react-datepicker__day--selected {
  border-radius: 0.3rem;
  background-color: rgba(210, 46, 41, 0.5);
  color: rgb(0, 0, 0);
}

.react-datepicker {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  border-bottom: none;
}
.react-datepicker__day-name {
  color: #d98886;
  font-size: 12px;
}
.react-datepicker__day--disabled {
  color: #ccc;
}

.react-datepicker__day--in-selecting-range,
.react-datepicker__day--selecting-range-start,
.react-datepicker__day--selecting-range-end {
  background-color: rgba(210, 46, 41, 0.5);
}
