import React, { useState, useRef, useEffect, memo } from "react";
import ExpandMore from "@mui/icons-material/ExpandMore";
import ExpandLess from "@mui/icons-material/ExpandLess";
import SearchIcon from "@mui/icons-material/Search";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import { useTranslation } from "react-i18next";
import { CircularProgress } from "@mui/material";
import { VariableSizeList as List, areEqual } from "react-window";
import { CloseIcon } from "../../icons";
import theme from "../../tailwind-theme";
import { CssTooltip } from "../StyledComponent";

const Dropdown = ({
  btnName,
  btnWidth,
  btnHeight,
  width,
  data,
  isLoading,
  reset,
  onSelectionChange,
  isMulti = true,
  disabled = false,
  defaultSelectedData,
  commonValues,
  isSearch = true,
  optionDataList,
  onBlur,
  isReports,
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [inputValue, setInputValue] = useState("");
  const buttonRef = useRef(null);

  const Styles = {
    btnPrimary: `bg-transparent text-gray-400 px-2 w-full border border-outerBorder flex min-h-[40px] items-center rounded-[5px] ${
      btnWidth ? btnWidth : "min-w-[400px]"
    } ${btnHeight ? btnHeight : "h-full"}`, //"h-[40px]" flex-wrap
  };
  const itemHeight = 30;
  const maxListHeight = 150;

  const handleOpen = (e) => {
    e.preventDefault();
    setOpen(!open);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (commonValues) {
      const valuesArray = Array.isArray(commonValues)
        ? commonValues
        : commonValues.split(",");
      setSelectedOptions(valuesArray);
      setInputValue(valuesArray.join(", "));
    } else {
      setSelectedOptions([]);
      setInputValue("");
    }
  }, [commonValues]);

  const handleSelectAll = () => {
    setSelectedOptions((prevSelectedOptions) => {
      if (prevSelectedOptions.includes("Select All")) {
        return [];
      } else {
        return data.map((element) => element.value);
      }
    });
  };

  const handleCheckboxChange = (option, filterType) => {
    if (option.value === "Select All") {
      handleSelectAll();
    } else {
      setSelectedOptions((prevSelectedOptions) => {
        let updatedOptions;
        if (!isMulti) {
          updatedOptions = [option.value];
        } else {
          if (optionDataList) {
            if (prevSelectedOptions.length === data.length - 1) {
              updatedOptions = [
                ...prevSelectedOptions,
                option.value,
                "Select All",
              ];
            } else {
              updatedOptions = prevSelectedOptions.includes(option.value)
                ? prevSelectedOptions.filter(
                    (selectedOption) => selectedOption !== option.value
                  )
                : prevSelectedOptions.length >= optionDataList
                ? [...prevSelectedOptions.slice(1), option.value]
                : [...prevSelectedOptions, option.value];
            }
          } else {
            updatedOptions = prevSelectedOptions.includes(option.value)
              ? prevSelectedOptions.filter(
                  (selectedOption) => selectedOption !== option.value
                )
              : [...prevSelectedOptions, option.value];
          }
        }
        const allOtherOptionsSelected = data
          .map((element) => element.value)
          .filter((value) => value !== "Select All")
          .every((value) => updatedOptions.includes(value));

        if (allOtherOptionsSelected) {
          updatedOptions.push("Select All");
        } else {
          updatedOptions = updatedOptions.filter(
            (selectedOption) => selectedOption !== "Select All"
          );
        }

        return updatedOptions;
      });
    }
  };

  //console.log("selectedOption", selectedOptions);

  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
  };

  useEffect(() => {
    setSelectedOptions([]);
    setSearchText("");
  }, [reset]);

  useEffect(() => {
    onSelectionChange(selectedOptions);
    setInputValue(selectedOptions.join(", "));
  }, [selectedOptions]);

  const filteredOptions =
    data?.length > 0
      ? data.filter((option) =>
          String(option.label).toLowerCase().includes(searchText.toLowerCase())
        )
      : [];

  useEffect(() => {
    if (defaultSelectedData) {
      setSelectedOptions(defaultSelectedData);
    }
  }, [defaultSelectedData]);

  const removeSelectedOption = (e, optionValue) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedOptions((prevSelectedOptions) => {
      const updatedOptions = prevSelectedOptions.filter(
        (selectedOption) => selectedOption !== optionValue
      );
      if (updatedOptions.includes("Select All")) {
        return updatedOptions.filter(
          (selectedOption) => selectedOption !== "Select All"
        );
      }

      return updatedOptions;
    });
  };

  let combinedOptions = [
    ...(selectedOptions || [])
      .map((selectedOption) =>
        data?.find((option) => option.value === selectedOption)
      )
      .filter(Boolean),
    ...filteredOptions.filter((option) => {
      return (
        option &&
        option?.value &&
        option?.label &&
        !(selectedOptions || []).includes(option.value)
      );
    }),
  ];
  const selectAllIndex = combinedOptions.findIndex(
    (option) => option.value === "Select All"
  );

  if (selectAllIndex !== -1) {
    const selectAllOption = combinedOptions.splice(selectAllIndex, 1)[0];
    combinedOptions = [selectAllOption, ...combinedOptions];
  }

  const Row = memo(({ data, index, style }) => {
    const currentOption = combinedOptions?.[index];
    return (
      <div key={index} style={style} className="mb-4">
        <label className="flex justify-between items-center gap-2 text-xs">
          <div className="text-xs">{currentOption?.label}</div>
          <div className="flex-grow flex justify-end mr-3">
            <input
              type="checkbox"
              checked={selectedOptions.includes(currentOption?.value)}
              onChange={() => handleCheckboxChange(currentOption)}
              className="min-w-[20px] w-4 h-4 "
              style={{
                accentColor: `${theme?.backgroundColor.bgCheckboxSelection}`,
              }}
            />
          </div>
        </label>
      </div>
    );
  }, areEqual);

  return (
    <ClickAwayListener onClickAway={handleClose}>
      <div
        className={` ${btnWidth ? "" : "min-w-[150px]"}  relative text-medium `}
      >
        <button
          ref={buttonRef}
          onClick={handleOpen}
          onBlur={onBlur}
          className={
            open
              ? `flex  ${Styles.btnPrimary} border-outerBorder`
              : Styles.btnPrimary
          }
          disabled={disabled}
        >
          <div className="flex w-11/12 flex-wrap  ">
            <div className="text-xs mt-1 flex flex-wrap">
              {selectedOptions.length > 0 ? (
                <>
                  {(isReports
                    ? selectedOptions.slice(0, 1)
                    : selectedOptions
                  ).map((selectedValue, index) => {
                    const option = combinedOptions.find(
                      (option) => option.value === selectedValue
                    );
                    if (option && option.label !== "Select All") {
                      return (
                        <span
                          key={index}
                          className="flex text-black items-center m-1 rounded h-6 px-2 bg-bgTeritary"
                        >
                          <span>{option.label}</span>
                          <CloseIcon
                            onClick={(e) =>
                              removeSelectedOption(e, option.value)
                            }
                            className="h-1.5 ml-1"
                          />
                        </span>
                      );
                    }
                    return null;
                  })}

                  {isReports && selectedOptions.length > 1 && (
                    <CssTooltip
                      title={
                        <div>
                          {selectedOptions
                            .slice(1)
                            .map((selectedValue, index) => {
                              const option = combinedOptions.find(
                                (option) => option.value === selectedValue
                              );
                              return option && option.label !== "Select All" ? (
                                <span key={index}>
                                  {index > 0 ? ", " : ""}
                                  {option.label}
                                </span>
                              ) : null;
                            })}
                        </div>
                      }
                      placement="top"
                      arrow
                    >
                      <span className="flex text-black whitespace-nowrap cursor-pointer mt-3 text-sm">
                        ...
                      </span>
                    </CssTooltip>
                  )}
                </>
              ) : btnName ? (
                btnName
              ) : (
                ""
              )}
            </div>
          </div>
          <div>
            {open ? (
              <ExpandLess className="ml-2" />
            ) : (
              <ExpandMore className="ml-2" />
            )}
          </div>
        </button>
        {open ? (
          <div className="relative bottom-0 left-0 z-50 ">
            <div
              className={`bg-white shadow-md p-4 rounded absolute   ${
                btnWidth ? "min-w-full" : "min-w-[150px]"
              }`}
            >
              {isSearch && (
                <div className="mb-4 ">
                  <div
                    className={`flex items-center border border-gray-300 px-2 py-1  w-full `}
                  >
                    <SearchIcon className="text-gray-400 mr-4 ml-1" />
                    <input
                      type="text"
                      placeholder="Search"
                      value={searchText}
                      onChange={handleSearchChange}
                      className="border-none outline-none w-full text-xs"
                    />
                  </div>
                </div>
              )}
              {isLoading ? (
                <div className="flex justify-center">
                  <CircularProgress />
                </div>
              ) : (
                <div className="w-full max-h-64 ">
                  <List
                    width={width}
                    height={Math.min(
                      combinedOptions.length * itemHeight,
                      maxListHeight
                    )}
                    itemCount={combinedOptions?.length}
                    itemData={combinedOptions}
                    itemSize={(index) =>
                      combinedOptions[index]?.label?.length > 150 ? 75 : 30
                    }
                  >
                    {Row}
                  </List>
                </div>
              )}
            </div>
          </div>
        ) : null}
      </div>
    </ClickAwayListener>
  );
};

export default Dropdown;
