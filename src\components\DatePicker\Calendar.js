import React, { useState, useEffect } from "react";
import { CalendarIcon } from "../../icons";
import dayjs from "dayjs";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./styles.css";
import { DownArrowIcon, UpArrowIcon } from "../../icons";

const timeRanges = [
  "Last Hour",
  "Today",
  "Yesterday",
  "Last 6 Hours",
  "Last Seven Days",
  "Last 12 Hours",
  "Last Week",
  "Last 24 Hours",
  "Last 30 Days",
  "This Month",
  "Last Month",
  "Calendar",
];

function Calendar({
  isEdit,
  setEditUpdated,
  setSelectedFilter,
  selectedRange,
  setSelectedRange,
  handleSelectedRange,
  // selectedFilter,
  formData,
  panel,
  reportTimeRange,
  setIntervalTypesState,
  editDetail,
  selectedFilter,
  isTimeRange,
  setCurrentPage,
}) {
  const [openDialog, setOpenDialog] = useState(false);
  const [isCalendarSelected, setIsCalendarSelected] = useState(false);
  const [dateRange, setDateRange] = useState([null, null]);
  const [startDate, endDate] = dateRange;
  const isApplyDisabled = startDate === null || endDate === null;
  const [startTime, setStartTime] = useState({
    hours: "00",
    minutes: "00",
    seconds: "00",
  });

  const [endTime, setEndTime] = useState({
    hours: "00",
    minutes: "00",
    seconds: "00",
  });
  const convertAndSetDateRange = () => {
    const dateRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
    //  const dateRegex = /^\d{2}-[A-Za-z]{3}-\d{4} \d{2}:\d{2}:\d{2}$/;

    if (
      (formData?.startDate !== undefined &&
        formData?.endDate !== undefined &&
        formData?.startDate !== "" &&
        formData?.endDate !== "") ||
      (editDetail?.timePeriod &&
        dateRegex.test(editDetail?.timePeriod?.split(" to ")[0]?.toString()) &&
        dateRegex.test(editDetail?.timePeriod?.split(" to ")[1]?.toString()))
    ) {
      //  setOpenDialog(true);
      // Parse the start and end dates from the selectedFilter object
      const startDate = new Date(
        formData.startDate ||
          editDetail?.timePeriod?.split(" to ")[0]?.toString()
      );
      const endDate = new Date(
        formData.endDate || editDetail?.timePeriod?.split(" to ")[1]?.toString()
      );

      const datesArray = [startDate, endDate];
      setDateRange(datesArray);
      handleSetInterval(startDate, endDate);

      // Extract time from start date
      const startHours = startDate.getHours().toString().padStart(2, "0");
      const startMinutes = startDate.getMinutes().toString().padStart(2, "0");
      const startSeconds = startDate.getSeconds().toString().padStart(2, "0");

      // Extract time from end date
      const endHours = endDate.getHours().toString().padStart(2, "0");
      const endMinutes = endDate.getMinutes().toString().padStart(2, "0");
      const endSeconds = endDate.getSeconds().toString().padStart(2, "0");

      // Set the start time and end time states
      setStartTime({
        hours: startHours,
        minutes: startMinutes,
        seconds: startSeconds,
      });

      setEndTime({
        hours: endHours,
        minutes: endMinutes,
        seconds: endSeconds,
      });
    }
    // setSelectedFilter({
    //   startDate: formData?.period?.startDate,
    //   endDate: formData?.period?.endDate,
    // });
    if (
      editDetail?.timePeriod &&
      dateRegex.test(editDetail?.timePeriod?.split(" to ")[0]?.toString()) &&
      dateRegex.test(editDetail?.timePeriod?.split(" to ")[1]?.toString()) &&
      selectedRange === "Calendar"
    ) {
      setSelectedRange("Calendar");
    } else {
      setSelectedRange(selectedRange);
    }
  };

  useEffect(() => {
    if (panel) {
      convertAndSetDateRange();
    }
  }, [formData, editDetail]);

  const handleTimeChange = (field, time, setTime) => (e) => {
    if (panel) {
      setEditUpdated(true);
    }

    const { value } = e.target;
    let numericValue = value.replace(/\D/g, "");
    if (field === "hours") {
      if (parseInt(numericValue) > 23) {
        numericValue = "23";
      }
    } else if (field === "minutes") {
      if (parseInt(numericValue) > 59) {
        numericValue = "59";
      }
    }

    setTime((prevTime) => ({
      ...prevTime,
      [field]: numericValue,
    }));
  };

  const handleIncrement = (field, time, setTime, limit) => () => {
    if (panel) {
      setEditUpdated(true);
    }

    setTime((prevTime) => {
      const newValue = (parseInt(prevTime[field], 10) + 1) % (limit + 1);
      const newTime = {
        ...prevTime,
        [field]: newValue < 10 ? `0${newValue}` : `${newValue}`,
      };
      return newTime;
    });
  };

  const handleDecrement = (field, time, setTime, limit) => () => {
    if (panel) {
      setEditUpdated(true);
    }

    setTime((prevTime) => {
      const newValue = Math.max(
        (parseInt(prevTime[field], 10) - 1 + (limit + 1)) % (limit + 1),
        0
      );
      const newTime = {
        ...prevTime,
        [field]: newValue < 10 ? `0${newValue}` : `${newValue}`,
      };
      return newTime;
    });
  };
  const handleDateChange = (dates) => {
    if (panel) {
      setEditUpdated(true);
    }

    setDateRange(dates);
    setIsCalendarSelected(selectedRange === "Calendar");
  };
  function handleReset() {
    setStartTime({ hours: "00", minutes: "00", seconds: "00" });
    setEndTime({ hours: "00", minutes: "00", seconds: "00" });
    setDateRange([null, null]);
    setIsCalendarSelected(false);
  }
  const handleItemClick = (range) => {
    setSelectedRange(range);
    if (range === "Calendar") {
      setIsCalendarSelected(true);
    }
  };

  const handleApply = () => {
    if (panel) {
      isEdit = false;
      setEditUpdated(true);
    }
    setOpenDialog(false);

    if (selectedRange) {
      let formattedStart, formattedEnd;
      const currentDateTime = dayjs();

      if (
        selectedRange === "Last Hour" ||
        selectedRange === "Last 6 Hours" ||
        selectedRange === "Last 12 Hours" ||
        selectedRange === "Last 24 Hours"
      ) {
        const hours = {
          "Last Hour": 1,
          "Last 6 Hours": 6,
          "Last 12 Hours": 12,
          "Last 24 Hours": 24,
        };

        const lastXHours = currentDateTime.subtract(
          hours[selectedRange],
          "hour"
        );
        if (selectedRange === "Last Hour") {
          formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
        } else {
          formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
        }

        handleReset();
      } else if (selectedRange === "Today") {
        handleReset();
        formattedStart = currentDateTime
          .startOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
      } else if (selectedRange === "Yesterday") {
        handleReset();
        const yesterday = currentDateTime.subtract(1, "day");
        formattedStart = yesterday.startOf("day").format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
      } else if (selectedRange === "Last Seven Days") {
        handleReset();
        formattedStart = currentDateTime
          .subtract(6, "days")
          .startOf("day")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:59:59");
      } else if (selectedRange === "Last Week") {
        handleReset();
        formattedStart = currentDateTime
          .subtract(1, "week")
          .startOf("week")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime
          .subtract(1, "week")
          .endOf("week")
          .format("YYYY-MM-DD HH:59:59");
      } else if (selectedRange === "Last 30 Days") {
        handleReset();
        formattedStart = currentDateTime
          .subtract(29, "days")
          .startOf("day")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime
          .endOf("day")
          .format("YYYY-MM-DD HH:59:00");
      } else if (selectedRange === "Last Month") {
        handleReset();
        formattedStart = currentDateTime
          .subtract(1, "month")
          .startOf("month")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime
          .subtract(1, "month")
          .endOf("month")
          .format("YYYY-MM-DD HH:59:59");
      } else if (selectedRange === "This Month") {
        handleReset();
        formattedStart = currentDateTime
          .startOf("month")
          .format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = currentDateTime
          .endOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
      } else if (selectedRange === "Calendar") {
        if (dateRange[0] !== null && dateRange[1] !== null) {
          const formattedStartTime = `${startTime.hours}:${startTime.minutes}:${startTime.seconds}`;
          const formattedEndTime = `${endTime.hours}:${endTime.minutes}:${endTime.seconds}`;

          formattedStart =
            dayjs(dateRange[0]).format("YYYY-MM-DD") + " " + formattedStartTime;
          formattedEnd =
            dayjs(dateRange[1]).format("YYYY-MM-DD") + " " + formattedEndTime;

          if (panel) {
            handleSetInterval(formattedStart, formattedEnd);
          }
        }
      }
      if (panel) {
        handleSelectedRange();
      }
      setSelectedFilter({
        startDate: formattedStart || formData?.period?.startDate,
        endDate: formattedEnd || formData?.period?.endDate,
      });
      setSelectedRange(selectedRange);
      setCurrentPage && setCurrentPage(1); // Reset to the first page when applying a new date range
    }
  };
  const handleSetInterval = (formattedStart, formattedEnd) => {
    let intervals = [];

    const startDate = dayjs(formattedStart);
    const endDate = dayjs(formattedEnd);
    // Calculate the difference in weeks and months
    const minsDifference = endDate.diff(startDate, "minute");
    const weeksDifference = endDate.diff(startDate, "week");
    const monthsDifference = endDate.diff(startDate, "month");
    const hoursDifference = endDate.diff(startDate, "hour");
    const daysDifference = endDate.diff(startDate, "day");

    if (minsDifference <= 60) {
      intervals = [{ label: "Minute", value: "minute" }];
    } else if (
      (hoursDifference > 1 && hoursDifference < 24) ||
      (minsDifference > 60 && minsDifference <= 1440)
    ) {
      intervals = [{ label: "Hour", value: "hour" }];
    } else if (hoursDifference >= 24 && daysDifference < 7) {
      intervals = [{ label: "Day", value: "day" }];
    } else if (daysDifference >= 7 && daysDifference <= 14) {
      intervals = [
        { label: "Week", value: "week" },
        { label: "Day", value: "day" },
      ];
    } else if (weeksDifference >= 2 && monthsDifference < 1) {
      intervals = [{ label: "Week", value: "week" }];
    } else if (monthsDifference >= 1) {
      intervals = [{ label: "Month", value: "month" }];
    }
    if (panel) {
      setIntervalTypesState(intervals);
    }
  };

  useEffect(() => {
    if (selectedRange === "Calendar") {
      setDateRange([
        new Date(selectedFilter.startDate),
        new Date(selectedFilter.endDate),
      ]);

      setStartTime({
        hours: dayjs(selectedFilter.startDate).format("HH"),
        minutes: dayjs(selectedFilter.startDate).format("mm"),
        seconds: dayjs(selectedFilter.startDate).format("ss"),
      });
      setEndTime({
        hours: dayjs(selectedFilter.endDate).format("HH"),
        minutes: dayjs(selectedFilter.endDate).format("mm"),
        seconds: dayjs(selectedFilter.endDate).format("ss"),
      });
    }
  }, []);

  return (
    <div className="relative">
      <div className="flex gap-4 items-center">
        <div
          className="px-3 w-full min-w-[200px] h-10 border border-calendarBoreder rounded-md text-titleColor flex cursor-pointer"
          onClick={() => {
            setOpenDialog(!openDialog);
            // setSelectedRange(null);
          }}
        >
          <CalendarIcon className="w-5 h-5 mt-2 " />
          <div className="text-titleColor text-sm mt-2  ml-2 mr-5 whitespace-nowrap">
            {selectedRange}
            {selectedRange === "Calendar" && isTimeRange
              ? ` (${selectedFilter.startDate} to ${selectedFilter.endDate}) `
              : ""}
          </div>
        </div>
      </div>

      {openDialog && (
        <div
          className={`absolute top-12 ${
            panel ? "left-1 z-10 mb-10" : "right-0"
          } px-1 py-1 border border-outerBorder rounded-sm bg-white min-w-[200px] min-h-[218px] flex flex-row z-10`}
        >
          <div className="px-3 py-2 border border-outerBorder rounded-sm bg-white">
            <div className="mt-1 text-titleColor text-xs font-['Open Sans Hebrew'] whitespace-nowrap font-bold">
              {"Quick Access"}
            </div>
            <p className="mt-3 border-b border-[#D9D9D9]"></p>
            <div className="grid grid-cols-2 gap-1.5 mt-3">
              {timeRanges.map((range, index) => (
                <button
                  key={index}
                  disabled={
                    reportTimeRange ? !reportTimeRange.includes(range) : false
                  }
                  onClick={() => handleItemClick(range)}
                  className={` text-left cursor-pointer disabled:text-opacity-40 text-tabColor text-[10px] font-['Open Sans Hebrew'] font-normal leading-5 ${
                    range === "Calendar" ? "col-span-2" : ""
                  } ${selectedRange === range ? "bg-[#FDF5F5]" : ""}`}
                >
                  {range}
                </button>
              ))}
            </div>

            <button
              className={`mt-2 w-[167px] h-5 bg-bgSecondary text-xs text-white rounded-[3px] mb-2 ${
                selectedRange === "Calendar" && isApplyDisabled
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
              onClick={handleApply}
              disabled={selectedRange === "Calendar" && isApplyDisabled}
            >
              Apply Period
            </button>
          </div>

          {selectedRange === "Calendar" && (
            <div className=" ml-2 min-w-[455px]">
              <DatePicker
                isOutsideRange={() => false}
                startDate={startDate}
                endDate={endDate}
                maxDate={new Date()}
                monthsShown={2}
                selectsRange
                inline
                style={{ backgroundColor: "white", display: "inline-flex" }}
                onChange={handleDateChange}
              />
              <div className="relative border-b border-r border-l border-outerBorder -mt-1.5 mr-0.5 ">
                <div className="flex gap-14 mb-1">
                  <div className="flex items-center">
                    <div className="ml-3 text-titleColor text-xs font-['Open Sans Hebrew'] whitespace-nowrap font-bold items-center">
                      Start time
                    </div>
                    {["hours", "minutes"].map((field) => (
                      <div
                        key={`start-${field}`}
                        className="flex items-center ml-5"
                      >
                        <div className="px-1 h-7 border border-listBorder rounded-md text-[#707070] flex">
                          <input
                            name={field}
                            value={startTime[field]}
                            onChange={handleTimeChange(
                              field,
                              startTime,
                              setStartTime
                            )}
                            maxLength="2"
                            className="focus:outline-none w-5"
                          />
                        </div>
                        <div className="ml-1 flex flex-col gap-1">
                          <div
                            className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                            onClick={handleIncrement(
                              field,
                              startTime,
                              setStartTime,
                              field === "hours" ? 23 : 59
                            )}
                          >
                            <UpArrowIcon />
                          </div>
                          <div
                            className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                            onClick={handleDecrement(
                              field,
                              startTime,
                              setStartTime,
                              field === "hours" ? 23 : 59
                            )}
                          >
                            <DownArrowIcon />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center ">
                    <div className="text-titleColor text-xs font-['Open Sans Hebrew'] whitespace-nowrap font-bold items-center">
                      End time
                    </div>
                    {["hours", "minutes"].map((field) => (
                      <div
                        key={`end-${field}`}
                        className="flex ml-5 items-center"
                      >
                        <div className="px-1 h-7 border border-listBorder rounded-md text-[#707070] flex">
                          <input
                            name={field}
                            value={endTime[field]}
                            onChange={handleTimeChange(
                              field,
                              endTime,
                              setEndTime
                            )}
                            maxLength="2"
                            className="focus:outline-none w-5"
                          />
                        </div>
                        <div className="ml-1 flex flex-col gap-1">
                          <div
                            className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                            onClick={handleIncrement(
                              field,
                              endTime,
                              setEndTime,
                              field === "hours" ? 23 : 59
                            )}
                          >
                            <UpArrowIcon />
                          </div>
                          <div
                            className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                            onClick={handleDecrement(
                              field,
                              endTime,
                              setEndTime,
                              field === "hours" ? 23 : 59
                            )}
                          >
                            <DownArrowIcon />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex gap-14 mb-1">
                  <div className="flex gap-3 ml-[90px]">
                    <div className=" text-xs text-[#707070] font-['Open Sans Hebrew'] font-bold">
                      Hrs
                    </div>
                    <div className="ml-6 text-xs text-[#707070] font-['Open Sans Hebrew'] font-bold">
                      Min
                    </div>
                  </div>
                  <div className="flex gap-3 ml-20">
                    <div className="ml-2 text-xs text-[#707070] font-['Open Sans Hebrew'] font-bold">
                      Hrs
                    </div>
                    <div className="ml-6 text-xs text-[#707070] font-['Open Sans Hebrew'] font-bold">
                      Min
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default Calendar;
