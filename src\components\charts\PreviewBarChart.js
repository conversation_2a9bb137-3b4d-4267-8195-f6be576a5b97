import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts";

function PreviewBarChart({ data }) {
  const colors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];

  // Generate chart data
  let chartData = data.x_axis.map((item, index) => {
    const xAxisKey = Object.keys(item)[0];
    return {
      name: item[xAxisKey],
      ...data.y_axis[index],
    };
  });

  if (chartData.length > 7) {
    chartData = chartData.slice(0, 7);
  }

  // Check if data.y_axis is defined and not null
  if (!data.y_axis || !data.y_axis[0]) {
    return (
      <div className="my-3 d-flex justify-content-center font-bold text-[10px]">
        No records to display
      </div>
    );
  }

  const yAxisNames = Object.keys(data.y_axis[0]);

  const formatYAxis = (tick) => {
    if (tick >= 1000000) {
      return `${tick / 1000000}M`;
    } else if (tick >= 1000) {
      return `${tick / 1000}k`;
    }
    return tick;
  };

  return (
    <div style={{ position: "relative" }}>
      <BarChart
        width={200}
        height={130}
        data={chartData}
        margin={{
          top: 15,
          right: 25,
          left: -20,
        }}
        barGap={0}
        barCategoryGap={0}
      >
        <XAxis
          dataKey="name"
          tick={{ fontSize: 10 }}
          textAnchor="end"
          interval={3}
        />
        <YAxis
          tick={{ fontSize: 10 }}
          tickCount={5}
          tickFormatter={formatYAxis}
        />
        <Tooltip contentStyle={{ fontSize: 8, padding: "1px" }} />

        {yAxisNames.map((propertyKey, index) => (
          <Bar
            key={propertyKey}
            dataKey={propertyKey}
            fill={colors[index % colors.length]}
            barSize={15}
            name={yAxisNames[index]}
          />
        ))}
      </BarChart>
    </div>
  );
}

export default PreviewBarChart;
