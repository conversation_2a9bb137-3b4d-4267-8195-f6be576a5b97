import React from "react";
import Doughnut<PERSON>hart from "../charts/DoughnutChart";
import BarChartComponent from "../charts/BarCharts";
import LineChartComponent from "../charts/LineChart";
import MultiAxisChart from "../charts/MultiAxisChart";
import Table from "../table/ReportTable";
import Pagination from "../Pagination/Pagination";
import ResultPerPageComponent from "../Pagination/ResultsPerPage";

const VisualizationRenderer = ({
  panelById,
  activeView,
  responseData,
  colors,
  columns,
  resultPerPage,
  limitPerPage,
  handleLimitChange,
  currentPage,
  handlePageChange,
}) => {
  if (panelById.visualizationType === "Pie Chart") {
    return (
      <div
        className="pie-chart-container"
        style={{
          display: "flex",
          //justifyContent: "space-between",
          width: "100%",
          // height: "300px",
          margin: "auto",
        }}
      >
        <DoughnutChart
          className={"mx-auto"}
          respData={responseData?.data || []}
          reportData={true}
          isPanel={true}
        />
      </div>
    );
  }

  if (panelById.visualizationType === "Bar Graph") {
    return (
      <div className="w-full">
        <BarChartComponent
          className={"mx-auto"}
          chartData={responseData?.data || []}
        />
      </div>
    );
  }

  if (panelById.visualizationType === "Line Graph") {
    return (
      <div className="w-full">
        <LineChartComponent
          data={responseData?.data}
          colors={colors}
          // isPreview={isPreview}
        />
      </div>
    );
  }

  if (
    panelById.visualizationType === "Table Report" &&
    activeView === "table"
  ) {
    return (
      <div className="table-container">
        {responseData?.data?.length > 0 ? (
          <Table data={responseData?.data || []} columns={columns} />
        ) : null}

        {responseData?.data?.length > 0 && (
          <div className="flex items-center justify-between mt-5">
            <div className="flex items-center">
              <ResultPerPageComponent
                countPerPage={resultPerPage}
                limit={limitPerPage}
                handleLimitChange={handleLimitChange}
                pageName="reports"
              />
              <div className="text-sm pl-3 text-titleColor">
                {(currentPage - 1) * limitPerPage + 1} -{" "}
                {Math.min(
                  limitPerPage * currentPage,
                  responseData?.totalCount || 0
                )}{" "}
                of {responseData?.totalCount || 0} rows
              </div>
            </div>

            <Pagination
              className="pagination-bar"
              currentPage={currentPage || 1}
              totalCount={Number(responseData?.totalCount) || 0}
              pageSize={Number(limitPerPage) || 10}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>
    );
  }

  if (panelById.visualizationType === "MultiAxis Graph") {
    return (
      <div
        className="multi-axis-chart-container"
        style={{
          display: "flex",
          //justifyContent: "space-between",
          width: "100%",
          height: "450px",
          margin: "auto",
        }}
      >
        <MultiAxisChart data={responseData || []} />{" "}
      </div>
    );
  }

  return null;
};

export default VisualizationRenderer;
