import { Modal } from "react-bootstrap";
import styled from "styled-components";
import { DeleteIcon } from "../../icons";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import { CloseIcon } from "../../icons";
import Button from "../../components/Button/OutlinedButton";

// const TransparentButton = styled(Button)`
//   color: transparent;
//   border: 1px solid black;
//   background-color: transparent;
// `;
const CustomModal = styled(Modal)`
  & .modal-dialog {
    width: 400px;
    height: 600px;
  }
  & .modal-content {
    border: none;
  }

  & .modal-header,
  & .modal-footer {
    border: none;
  }
`;

function ConfirmationModal({
  icon,
  title,
  body,
  show,
  onHide,
  onConfirm,
  cancelHide,
  confirmButonName,
  align,
  isLoading,
  iconImage,
}) {
  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 411,
          maxHeight: 435,
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onHide}
      className="p-6 font-sans"
    >
      <DialogTitle className="mt-2 flex justify-center">
        {title ? title : ""}
      </DialogTitle>
      <div className="border-b border-panelBorder mx-5"></div>
      <div className="flex justify-center items-center mt-2">
        <div>{icon ? iconImage : ""}</div>
      </div>
      <div className="text-base text-center mt-4 mx-auto">{body}</div>
      <div>
        <div className="text-center mt-10 gap-5 mb-4">
          {cancelHide && cancelHide === "true" ? (
            ""
          ) : (
            <Button
              onClick={onHide}
              buttonClassName={"w-[120px]"}
              label={"No"}
              //style={{ width: "100px", marginRight: "26px" }}
            />
          )}
          <Button
            variant="danger"
            label={confirmButonName ? confirmButonName : "Yes"}
            onClick={onConfirm}
            disabled={isLoading ? isLoading : false}
            buttonClassName={"w-[120px]"}
            //style={{ width: "100px" }}
          />
        </div>
      </div>
    </Dialog>
  );
}

export default ConfirmationModal;
