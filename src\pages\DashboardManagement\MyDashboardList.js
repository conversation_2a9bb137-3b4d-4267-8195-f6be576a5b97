import React, { useState, useContext } from "react";
import { getAll } from "../../services/dashboard-api";
import { useQuery, useMutation } from "react-query";
import { useNavigate } from "react-router-dom";
import {
  getDashboardById,
  deleteDashboard,
} from "../../services/dashboard-api";
import { previewPanel } from "../../services/panels-api";
import ResponsiveLayout from "./ResponsiveLayout";
import { DeleteIcon, EditingIcon } from "../../icons";
import SuccessDialog from "../../popups/SuccessDialog";
import DeleteDialog from "../../popups/DeleteDialog";
import InfoModal from "../../components/modals/InfoModal";
import { AuthContext } from "../../context/AuthContext";
import bgImage from "../../assets/img/dashboard.png";
import BreadcrumbNavigation from "../../components/BreadCrumps/BreadCrump";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import ResponsiveLayoutCard from "./ResponsiveLayoutCard";
import { cardServices } from "../../services/cards-api";
import { MetaDataProvider } from "../../context/MetaDataContext";
import StaticCollapsibleFilter from "../../components/CollapsibleFilter/StaticCollapsibleFilter";

function MyDashboardList() {
  const { data: dataList } = useQuery(["dashboardList"], getAll);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedName, setSelectedName] = useState("");
  const [selectedId, setSelectedId] = useState("");
  const [searchClicked, setSearchClicked] = useState(false);
  const [cardDroppedData, setCardDroppedData] = useState([]);
  const [previewResponses, setPreviewResponses] = useState([]);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [errorCode, setErrorCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [isApplied, setIsApplied] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState(() => {
    const startDate = dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
    const endDate = dayjs().format("YYYY-MM-DD HH:mm:ss");
    return { startDate, endDate };
  });
  const [cardMetaData, setCardMetaData] = useState([]);
  const [filters, setFilters] = useState([]);

  dayjs.extend(customParseFormat);

  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const navigate = useNavigate();

  const { roles } = useContext(AuthContext);
  const permissions = roles.resources.filter(
    (res) => res.name === "Dashboard Management"
  )[0].permissions;

  const handleCloseInfoModal = () => {
    setShowAlertConfirmation(false);
  };

  const {
    data: dashboardList,
    isLoading: getDashboardLoading,
    refetch,
  } = useQuery(["getDashboardById", selectedId], getDashboardById, {
    enabled: !!selectedId,

    onSuccess: ({ data }) => {
      setCardDroppedData(
        data?.cards.map((card) => ({
          reportField: card.cardDetails.reportField,
          value: card.cardDetails.value,
          order: card.order,
        }))
      );
      setCardMetaData(
        data?.cards.map((card) => ({
          id: card.cardDetails.id,
          order: card.order,
        }))
      );
      const panels = data?.panels?.map((panel, i) => {
        let dimension = {
          w: 6,
          h: 3,
          x: 0,
          y: 0,
          order: panel.order,
        };

        return { ...panel, dimension };
      });
      setPreviewResponses(panels);
      previewAPICall(panels);
      if (isApplied) {
        getAllCardDetailAPI();
      } else {
        setCardDroppedData(
          data?.cards.map((card) => ({
            reportField: card.cardDetails.reportField,
            value: card.cardDetails.value,
            order: card.order,
          }))
        );
      }
    },
    refetchOnWindowFocus: false,
  });

  const { mutate: getAllCardDetail, isLoading: cardLoading } = useMutation(
    cardServices.getAllCardById
  );

  const getAllCardDetailAPI = () => {
    if (!cardMetaData?.length || !cardMetaData.some((card) => card.id)) return;
    let reqData = {
      reportFilters: filters,
      startDate: selectedFilter.startDate,
      endDate: selectedFilter.endDate,
      cardIds: cardMetaData.map((card) => card.id),
    };

    const orderMap = cardMetaData.reduce((acc, card) => {
      acc[card.id] = card.order;
      return acc;
    }, {});

    getAllCardDetail(reqData, {
      onSuccess: (res) => {
        setCardDroppedData(
          res?.data?.map((card) => {
            return {
              reportField: card.reportField,
              value: card.value,
              order: orderMap[card.id] ?? card.order,
            };
          })
        );
      },
      onError: (err) => {
        console.log("err", err);
      },
    });
  };

  const previewAPICall = (previewData) => {
    setLoading(true);
    previewData.forEach((payload, index) => {
      const selectedRange = payload.panelDetails.timePeriod;
      //console.log("payload", payload);
      let formattedStart = "";
      let formattedEnd = "";
      const currentDateTime = dayjs();

      if (selectedRange) {
        if (selectedRange.includes("to")) {
          const [startString, endString] = selectedRange.split("to");
          formattedStart = startString;
          formattedEnd = endString;
        } else {
          if (
            selectedRange === "Last Hour" ||
            selectedRange === "Last 6 Hours" ||
            selectedRange === "Last 12 Hours" ||
            selectedRange === "Last 24 Hours"
          ) {
            const hours = {
              "Last Hour": 1,
              "Last 6 Hours": 6,
              "Last 12 Hours": 12,
              "Last 24 Hours": 24,
            };

            const lastXHours = currentDateTime.subtract(
              hours[selectedRange],
              "hour"
            );
            if (selectedRange === "Last Hour") {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
            } else {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
            }
          } else if (selectedRange === "Today") {
            formattedStart = currentDateTime
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Yesterday") {
            const yesterday = currentDateTime.subtract(1, "day");
            formattedStart = yesterday
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Last Seven Days") {
            formattedStart = currentDateTime
              .subtract(6, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:59:59");
          } else if (selectedRange === "Last Week") {
            formattedStart = currentDateTime
              .subtract(1, "week")
              .startOf("week")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "week")
              .endOf("week")
              .format("YYYY-MM-DD HH:59:59");
          } else if (selectedRange === "Last 30 Days") {
            formattedStart = currentDateTime
              .subtract(29, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:59:59");
          } else if (selectedRange === "Last Month") {
            formattedStart = currentDateTime
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD HH:59:59");
          } else if (selectedRange === "This Month") {
            formattedStart = currentDateTime
              .startOf("month")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
          }
        }
      }

      let interval = undefined;
      if (payload.panelDetails.interval) {
        interval = payload.panelDetails.interval;
      }

      let reqData = {
        name: payload.panelDetails.name,
        visualizationType: payload.panelDetails.visualizationType,
        filters: [],
        dataColumns: {
          derivedFields: payload.panelDetails.dataColumns?.derivedFields,
        },
        reportFilters: filters,
        startDate: isApplied ? selectedFilter.startDate : formattedStart,
        endDate: isApplied ? selectedFilter.endDate : formattedEnd,
        ...(interval && !isApplied ? { interval: interval } : {}),
      };

      let reqDataForDashBoard = {
        name: payload.panelDetails.name,
        visualizationType: payload.panelDetails.visualizationType,
        filters: [],
        dataColumns: {
          derivedFields: payload.panelDetails.dataColumns?.derivedFields,
        },
        reportFilters: filters,
        startDate: isApplied ? selectedFilter.startDate : formattedStart,
        endDate: isApplied ? selectedFilter.endDate : formattedEnd,
        ...(interval && !isApplied ? { interval: interval } : {}),
      };
      payload.panelDetails.filters.forEach((condition) => {
        reqDataForDashBoard.filters.push({
          field: condition.field,
          condition: condition.condition,
          value: condition.value,
          operator: condition.operator,
        });
      });

      if (payload.panelDetails.visualizationType === "Bar Graph") {
        reqDataForDashBoard.dataColumns["X-Axis"] =
          payload.panelDetails.dataColumns?.["X-Axis"];
        reqDataForDashBoard.dataColumns.noOfRecords =
          payload.panelDetails.dataColumns.noOfRecords;
      } else {
        reqDataForDashBoard.dataColumns.tableFields =
          payload.panelDetails.dataColumns?.tableFields;
      }
      if (payload.panelDetails.visualizationType === "Bar Graph") {
        reqData.dataColumns["X-Axis"] =
          payload.panelDetails.dataColumns?.["X-Axis"];
        reqData.dataColumns.noOfRecords =
          payload.panelDetails.dataColumns.noOfRecords;
      } else {
        reqData.dataColumns.tableFields =
          payload.panelDetails.dataColumns?.tableFields;
      }

      payload.panelDetails.filters.forEach((condition) => {
        reqData.filters.push({
          field: condition.field,
          condition: condition.condition,
          value: condition.value,
          operator: condition.operator,
        });
      });
      previewPanel({ reqData })
        .then(({ data }) => {
          setLoading(false);
          // console.log("count", data);
          setPreviewResponses((prevResponses) => {
            let newData = prevResponses?.filter(
              ({ dimension }) => dimension.order !== payload.dimension.order
            );

            newData.push({
              data: data.data,
              panelData: {
                ...reqDataForDashBoard,
                data:
                  reqDataForDashBoard.visualizationType === "MultiAxis Graph"
                    ? data
                    : data?.data,
              },
              dimension: payload.dimension,
              id: payload.panelDetails.id,
              timePeriod: payload.panelDetails.timePeriod,
              count: data.totalCount,
              pageCount: data.count,
            });
            return newData;
          });
        })
        .catch((err) => {
          setErrorCode(err.response.data.message);
          setLoading(false);
          setPreviewResponses((prevResponses) => {
            let newData = prevResponses?.filter(
              ({ dimension }) => dimension.order !== payload.dimension.order
            );

            newData.push({
              data: [],
              panelData: {
                ...reqDataForDashBoard,
                data: "failed",
              },
              dimension: payload.dimension,
              id: payload.panelDetails.id,
              timePeriod: payload.panelDetails.timePeriod,
            });
            return newData;
          });
        });
    });
  };

  const { mutate: DeleteDashboard, isLoading: deleteLoading } =
    useMutation(deleteDashboard);

  const getGridData = (index, dimension) => {
    let dim = { x: 0, y: 0, w: dimension.w, h: dimension.h };

    switch (dimension.order - 1) {
      case 0:
        return dim;
      case 1:
        dim.y = 3;
        return dim;
      case 2:
        dim.x = 6;
        return dim;
      case 3:
        dim.y = 3;
        return dim;
      case 4:
        dim.x = 6;
        dim.y = 3;
        return dim;
      default:
        return dim;
    }
  };

  const filteredData = dataList?.data?.data.filter((dashboard) =>
    dashboard.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSearchInputChange = (value) => {
    setSearchQuery(value);
    setSelectedId("");
    setCardDroppedData([]);
    setPreviewResponses([]);
  };

  const handleSelectName = (id, name) => {
    setSelectedId(id);
    setSelectedName(name);
    setSearchQuery(name);
    setSearchClicked(false);
  };

  const handleClickSearch = () => {
    setSearchClicked(true);
  };

  return (
    <>
      <div className="flex justify-between mt-5">
        <div className="flex flex-col flex-grow">
          <div className="text-headingColor text-2xl font-bold cursor-pointer">
            <BreadcrumbNavigation
              //linkOne={"Roles"}
              linkTwo={"Dashboard"}
              onlinkTwoClick={() => navigate("/app/dashboard/details")}
              title={"My Dashboard"}
            />
            {/* {"< My Dashboard"} */}
          </div>
          {getDashboardLoading ? (
            <div className="w-full h-screen flex justify-center items-center text-center p-6">
              {" "}
              Loading ...{" "}
            </div>
          ) : errorCode === "Network Error" && !loading ? (
            <h1 className="w-full h-screen flex justify-center items-center text-center p-6">
              {" "}
              Something went wrong! Please check the connectivity!{" "}
            </h1>
          ) : null}
          {!selectedId ? (
            <div className="">
              <div className="flex justify-center mt-3">
                <img
                  src={bgImage}
                  style={{
                    height: "80%",
                    width: "70%",
                    objectFit: "cover",
                  }}
                  alt="bg"
                />
              </div>
            </div>
          ) : null}
        </div>

        <div className="bg-white p-2.5 rounded md:w-[200px] w-full">
          <div className="border border-panelBorder p-2 rounded flex items-center">
            <input
              type="text"
              placeholder="Search..."
              className="border border-panelBorder rounded-sm px-1  w-full text-sm"
              style={{ outline: "none", borderColor: "transparent" }}
              value={searchQuery}
              onChange={(e) => handleSearchInputChange(e.target.value)}
              onClick={handleClickSearch}
            />
            {selectedId && (
              <div className="ml-auto flex items-center">
                <EditingIcon
                  className="w-4 h-4 ml-2 text-gray-500 cursor-pointer"
                  onClick={() => {
                    if (permissions.update === 0) {
                      setShowAlertConfirmation(true);
                      setMessage("Update permission not allowed");
                    } else {
                      navigate(`/app/dashboard/details/edit/${selectedId}`, {
                        state: { isEdit: true },
                      });
                    }
                  }}
                />
                <DeleteIcon
                  className="w-4 h-4 ml-1 cursor-pointer"
                  onClick={() => {
                    if (permissions.delete === 0) {
                      setShowAlertConfirmation(true);
                      setMessage("Delete permission not allowed");
                    } else {
                      setDeleteDialog(true);
                    }
                  }}
                />
              </div>
            )}
          </div>
          <div className=" flex-col rounded-sm px-1 py-1 mt-2">
            {searchClicked && (
              <>
                {filteredData === undefined ? (
                  <div>Loading</div>
                ) : (
                  <>
                    {filteredData.map((dashboard) => (
                      <div
                        key={dashboard.id}
                        style={{
                          whiteSpace: "pre-wrap",
                          wordWrap: "break-word",
                          maxWidth: "200px",
                        }}
                        className={`text-sm text-titleColor cursor-pointer hover:bg-selectBackground ${
                          selectedName === dashboard.name
                            ? "bg-selectBackground"
                            : ""
                        }`}
                        onClick={() =>
                          handleSelectName(dashboard.id, dashboard.name)
                        }
                      >
                        {dashboard.name} - {dashboard.createdBy}
                        <div className="border-b border-panelBorder mt-1 mb-1"></div>
                      </div>
                    ))}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      <div className="-ml-3 w-[80vw] h-[82%] relative">
        {dashboardList?.data?.cards?.length === 0 &&
        dashboardList?.data?.panels.length === 0 ? (
          <div className="text-center md:mt-36 font-semibold">
            No cards or panels have been created for this dashboard
          </div>
        ) : (
          <>
            {selectedId ? (
              <>
                {" "}
                <MetaDataProvider>
                  <StaticCollapsibleFilter
                    setFilters={setFilters}
                    filterData={filters}
                    setIsApplied={setIsApplied}
                    refetch={refetch}
                    selectedFilter={selectedFilter}
                    setSelectedFilter={setSelectedFilter}
                    getAllCardDetailAPI={getAllCardDetailAPI}
                  />
                </MetaDataProvider>
              </>
            ) : null}

            <ResponsiveLayoutCard
              cardDroppedData={cardDroppedData}
              isApplied={isApplied}
            />
            <ResponsiveLayout
              cardDroppedData={cardDroppedData}
              panelDroppedData={previewResponses}
              getGridData={getGridData}
              errorCode={errorCode}
              isDownload={true}
              setPreviewLoading={setPreviewLoading}
              previewLoading={previewLoading}
              reportFilters={filters}
              selectedFilter={selectedFilter}
              isApplied={isApplied}
            />
          </>
        )}
      </div>

      <DeleteDialog
        show={deleteDialog}
        onHide={() => setDeleteDialog(false)}
        onConfirm={() => {
          DeleteDashboard(
            { value: selectedId },
            {
              onSuccess: () => {
                setDeleteDialog(false);
                setMessage("Dashboard deleted successfully");
                setSuccessDialog(true);
                navigate("/app/dashboard/details");
              },
              onError: (error) => {
                setDeleteDialog(false);
                setMessage(error?.response?.data?.message);
                setErrorDialog(true);
              },
            }
          );
        }}
        title={"Are you sure to delete the dashboard ?"}
        isLoading={deleteLoading}
      />
      <SuccessDialog
        show={suceessDialog}
        onHide={() => setSuccessDialog(false)}
        message={message}
      />
      <InfoModal
        show={showAlertConfirmation}
        onHide={handleCloseInfoModal}
        message={message}
      />
    </>
  );
}

export default MyDashboardList;
