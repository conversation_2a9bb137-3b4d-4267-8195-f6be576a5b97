import React, { useState, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lt<PERSON>,
  ResponsiveContainer,
} from "recharts";
import dayjs from "dayjs";
import { ExpandMore, ExpandLess } from "@mui/icons-material";

const LineChartComponent = ({
  data,
  colors = [],
  dimension,
  isChart,
  isWidth,
  isExpand,
  errMsg,
}) => {
  const [isLegendExpanded, setIsLegendExpanded] = useState(true);
  const [hiddenSeries, setHiddenSeries] = useState(new Set());

  const dateFormatter = (tick) => {
    return dayjs(tick).format("DD MMM HH:mm");
  };

  const formatYAxis = (tick) => {
    if (tick >= 1000000) {
      return `${tick / 1000000}Mn`;
    } else if (tick >= 1000) {
      return `${tick / 1000}k`;
    }
    return tick;
  };
  const visibleCount = 4;
  // Generate a distinct color using HSL color space
  const generateColor = (index) => {
    const hue = (index * 137.508) % 360; // golden angle for better distinction
    return `hsl(${hue}, 70%, 50%)`;
  };

  const transposedData = data?.x_axis
    ? data.x_axis.map((xItem, index) => ({
        Datetime: xItem.Datetime,
        ...Object.keys(data?.y_axis[index]).reduce((acc, key) => {
          acc[key] = data?.y_axis[index][key];
          acc["Datetime"] = xItem.Datetime;
          return acc;
        }, {}),
      }))
    : [];

  // Get all series keys
  const seriesKeys = useMemo(() => {
    return data?.y_axis &&
      data?.y_axis?.length > 0 &&
      Object.keys(data?.y_axis[0])?.length > 0
      ? Object.keys(data?.y_axis[0])
      : [];
  }, [data]);

  const displayedSeries = isLegendExpanded
    ? seriesKeys
    : seriesKeys.slice(0, visibleCount);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const shouldLimit = isChart && !isExpand;
      const visiblePayload = shouldLimit ? payload.slice(0, 4) : payload;

      return (
        <div
          style={{
            backgroundColor: "white",
            padding: "7px",
            border: "1px solid #ccc",
            fontSize: "12px",
            maxWidth: "260px",
            pointerEvents: "auto",
            fontWeight: "bold",
          }}
          onMouseDown={(e) => e.stopPropagation()}
        >
          <div style={{ marginBottom: "6px", fontWeight: 600 }}>
            {`Datetime: ${dateFormatter(label)}`}
          </div>

          <div>
            {visiblePayload.map((item, index) => (
              <div
                key={index}
                style={{ color: item.color, marginBottom: "4px" }}
              >
                <span>{`${item.name}: ${item.value.toLocaleString("en-US", {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 2,
                })}`}</span>
              </div>
            ))}
          </div>

          {shouldLimit && payload.length > 4 && (
            <div
              style={{
                marginTop: "6px",
                fontStyle: "italic",
                color: "#555",
                fontSize: "11px",
                textAlign: "center",
              }}
            >
              Click the expand icon to see more tooltip data.
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  const CollapsibleLegend = () => {
    const hasMore = seriesKeys.length > visibleCount;
    const shownSeries =
      isLegendExpanded || !hasMore
        ? seriesKeys
        : seriesKeys.slice(0, visibleCount);

    return (
      <div className="w-full bg-gray-50 rounded-lg p-2">
        {hasMore && (
          <div className="flex items-center justify-end">
            <button
              onClick={() => setIsLegendExpanded(!isLegendExpanded)}
              className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <span className="text-sm">
                {isLegendExpanded
                  ? "Show Less"
                  : `Show All (${seriesKeys.length})`}
              </span>
              {isLegendExpanded ? (
                <ExpandLess sx={{ fontSize: 16 }} />
              ) : (
                <ExpandMore sx={{ fontSize: 16 }} />
              )}
            </button>
          </div>
        )}

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {shownSeries.map((seriesKey, index) => {
            const color = colors[index] || generateColor(index);
            return (
              <div
                key={seriesKey}
                className="flex items-center space-x-2  rounded hover:bg-white transition-all"
              >
                <div
                  className="w-3 h-3 rounded flex-shrink-0"
                  style={{ backgroundColor: color }}
                />
                <span className="text-sm text-gray-700">{seriesKey}</span>
              </div>
            );
          })}
        </div>

        {!isLegendExpanded && hasMore && (
          <div className="mt-2 text-center">
            <span className="text-sm text-gray-500">
              +{seriesKeys.length - visibleCount} more series
            </span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div>
      <ResponsiveContainer
        width={dimension ? dimension.w * 120 : "100%"}
        height={dimension ? dimension.h * 70 : 400}
      >
        <LineChart
          data={transposedData}
          margin={{
            top: dimension ? 20 : 35,
            right: dimension ? 10 : 30,
            left: isWidth ? 0 : dimension ? 15 : 5,
            bottom: isWidth ? 70 : dimension ? 40 : 60,
          }}
          barGap={0}
          barCategoryGap={0}
        >
          <Tooltip content={<CustomTooltip />} />
          <XAxis
            dataKey="Datetime"
            type="category"
            tick={{ fontSize: dimension ? 10 : 12, fontWeight: 600 }}
            tickFormatter={dateFormatter}
            angle={-50}
            textAnchor="end"
            interval={0}
          />
          <YAxis
            tick={{ fontSize: dimension ? 10 : 12, fontWeight: 600 }}
            tickCount={dimension ? 7 : 10}
            tickFormatter={formatYAxis}
            interval={dimension ? 0 : 1}
          />

          {seriesKeys.length > 0 ? (
            seriesKeys.map((key, index) => (
              <Line
                key={key}
                type="natural"
                dataKey={key}
                stroke={colors[index] || generateColor(index)}
                dot={{ strokeWidth: 1.5, r: 3 }}
                strokeWidth={3}
                hide={hiddenSeries.has(key)}
              />
            ))
          ) : (
            <text
              x="50%"
              y="50%"
              textAnchor="middle"
              dominantBaseline="middle"
              fontSize="16px"
            >
              {errMsg ? errMsg : "No records to display"}
            </text>
          )}
        </LineChart>
      </ResponsiveContainer>
      {!isChart || isWidth ? <CollapsibleLegend /> : null}
    </div>
  );
};

export default LineChartComponent;
