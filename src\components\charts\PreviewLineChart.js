import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts";
import dayjs from "dayjs";

const PreviewLineChart = ({ data, colors }) => {
  if (
    !data ||
    !data.x_axis ||
    data.x_axis.length === 0 ||
    !data.y_axis ||
    data.y_axis.length === 0
  ) {
    return <div className="text-xs mx-3 my-3">No records to display</div>;
  }

  const dateFormatter = (tick) => {
    return dayjs(tick).format("HH:mm");
  };

  const formatYAxis = (tick) => {
    if (tick >= 1000000) {
      return `${tick / 1000000}M`;
    } else if (tick >= 1000) {
      return `${tick / 1000}k`;
    }
    return tick;
  };

  // Transpose data to display X-axis horizontally
  const transposedData = data.x_axis.slice(0, 10).map((xItem, index) => ({
    Datetime: xItem.Datetime,
    ...Object.keys(data.y_axis[index]).reduce((acc, key) => {
      acc[key] = data.y_axis[index][key];
      acc["Datetime"] = xItem.Datetime; // Add Datetime key
      return acc;
    }, {}),
  }));

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div
          className="custom-tooltip"
          style={{
            backgroundColor: "white",
            padding: "3px",
            border: "1px solid #ccc",
          }}
        >
          <div className="" style={{ fontSize: "8px" }}>
            <span>{`Datetime: ${dateFormatter(label)}`}</span>
            <br />
            {payload.map((item, index) => (
              <div
                key={index}
                style={{
                  color: item.color,
                  fontSize: "10px",
                  marginBottom: "2px",
                }}
              >
                <span>{`${item.name}: ${item.value}`}</span>
                <br />
              </div>
            ))}
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <LineChart
      width={200}
      height={130}
      data={transposedData}
      margin={{
        top: 15,
        right: 25,
        left: -20,
      }}
    >
      <XAxis
        dataKey="Datetime"
        type="category"
        tick={{ fontSize: 10, fontWeight: 600 }}
        tickFormatter={dateFormatter}
        textAnchor="end"
        interval={2}
        //angle={-20}
      />
      <YAxis
        tick={{ fontSize: 10, fontWeight: 600 }}
        tickFormatter={formatYAxis}
        tickCount={5}
      />
      <Tooltip content={<CustomTooltip />} />
      {/* Assuming colors array has the color codes for each line */}
      {Object.keys(data.y_axis[0]).map((key, index) => (
        <Line
          key={key}
          type="natural"
          dataKey={key}
          stroke={colors[index % colors.length]}
          dot={false}
          strokeWidth={3}
        />
      ))}
    </LineChart>
  );
};

export default PreviewLineChart;
