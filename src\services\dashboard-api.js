import axios from "axios";
//import { config } from "../assets/config/config";
import getAPIMap from "../routes/ApiUrls";

//const apiUrl = config.api.url;

export async function getAll(options) {
  //console.log(options);
  let url = getAPIMap("dashboard");
  if (
    options?.queryKey[1] &&
    options?.queryKey[1] !== "" &&
    options?.queryKey[1] !== undefined
  ) {
    url += `?page=${options.queryKey[1]}`;
  }
  if (options?.queryKey[2] !== "" && options?.queryKey[2] !== undefined) {
    url += `&limit=${options.queryKey[2]}`;
  }
  if (options?.queryKey[3] !== "" && options?.queryKey[3] !== undefined) {
    url = url + "&search=" + encodeURIComponent(options.queryKey[3]);
  }
  if (options.queryKey[4] !== "" && options.queryKey[4] !== undefined) {
    url += `?createdBy=${options.queryKey[4]}`;
  }

  let response = axios.get(url);
  return response;
}
export async function deleteDashboard(options) {
  let url = getAPIMap("dashboard") + `/${options.value}`;
  let response = axios.delete(url);
  // console.log("url", url);
  return response;
}

export async function createDashboard(options) {
  let url = getAPIMap("dashboard");
  let response = axios.post(url, options.reqData);
  // console.log("url", url);
  return response;
}

export async function getDashboardById(options) {
  let url = getAPIMap("viewDashboard");
  url = url.replace("{id}", options.queryKey[1]);
  url += `?userId=${options.queryKey[2]}`;
  if (options.queryKey[2] !== "" && options.queryKey[2] !== undefined) {
    url += `&userAnalysis=${true}`;
  }
  let response = axios.get(url);
  // console.log("url", url);
  return response;
}

export async function updateDashboard(options) {
  let url = getAPIMap("dashboard") + `/${options.value}`;
  let response = axios.put(url, options.reqData);
  // console.log("url", url);
  return response;
}

// export async function getAlltoRole(page, limit) {
//   let url = `${apiUrl}/v1/dashboard?page=${page}&limit=${limit}`;
//   let response = axios.get(url);
//   return response;
// }

export async function getAlltoRole() {
  let url = getAPIMap("dashboard");
  let response = axios.get(url);
  return response;
}
