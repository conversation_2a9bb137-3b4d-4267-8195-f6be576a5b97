import { useState, useEffect, useContext, useMemo } from "react";
import { roleService } from "../services/roles.service";
import Table from "../components/table/ReportTable";
import Pagination from "../components/Pagination/Pagination";
import { EditingIcon, TrashIcon, Alert } from "../icons";
import DeleteDialog from "../popups/DeleteDialog";
import { useNavigate } from "react-router-dom";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import InfoModal from "../components/modals/InfoModal";
import SuccessDialog from "../popups/SuccessDialog";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { DataContext } from "../context/DataContext";
import EditButton from "../components/Button/EditButton";
import theme from "../tailwind-theme";
import { CssTooltip } from "../components/StyledComponent";

function RolesList({
  reload,
  searchStr,
  permissions,
  message,
  showAlertConfirmation,
  handleCloseInfoModal,
  handleOpenInfoModal,
  handleSetMessage,
  handleSuccessDialog,
  totalResults,
  setTotalResults,
  isLoading,
  setIsLoading,
}) {
  const [Roles, setRoles] = useState([]);
  const [rolesState, setRolesState] = useState([]);

  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [roleId, setRoleId] = useState("");
  const [successDialog, setSuccessDialog] = useState(false);
  const [limitPerPage, setLimitPerPage] = useState(10);
  const { resultPerPage } = useContext(DataContext);
  const [refetchList, setRefetch] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (reload.searchStr !== "") {
      setIsLoading(true);
      roleService
        .getAll(searchStr, currentPage, limitPerPage)
        .then((data) => {
          let newData = data?.data?.data?.map((x) => {
            return {
              cards: x.cards,
              //createdAt: formatDateTime(x.createdAt),
              createdAt: x.createdAt,
              createdBy: x.createdBy,
              dashboardCount: x.dashboardCount,
              description: x.description,
              dynamicDashboard: x.dynamicDashboard,
              id: x.id,
              isSuperAdminRole: x.isSuperAdminRole,
              name: x.name,
              panelCount: x.panelCount,
              resources: x.resources,
              staticReports: x.staticReports,
              dynamicReports: x.dynamicReports,
              status: x.status,
              updatedAt: x.updatedAt,
              userCount: x.userCount,
              isPredefined: x.isPredefined,
              customerSupplier:
                x.customerSupplierRole || x.supplierRole || x.customerRole
                  ? true
                  : false,
            };
          });
          setRoles(newData);
          setRolesState(data?.data);
          setIsLoading(false);
          setTotalResults(data.data.totalCount);
        })
        .catch((error) => {
          setIsLoading(false);

          console.error("Error fetching data:", error);
        });
    }
  }, [reload, searchStr, currentPage, limitPerPage, refetchList]);

  function deleteUser(id) {
    const filteredUsers = Roles.map((x) => {
      if (x.id === id) {
        x.isDeleting = true;
      }
      return x;
    });
    setRoles(filteredUsers);
    roleService
      .delete(id)
      .then(() => {
        setRefetch((oldState) => !oldState);
        setCurrentPage(1);

        setShowDeleteConfirmation(0);
        handleSetMessage("Role deleted successfully");
        setSuccessDialog(true);
      })
      .catch((error) => {
        toast.error(
          error.response.data.message ? error.response.data.message : error
        );
        setRefetch((oldState) => !oldState);

        setShowDeleteConfirmation(0);
      });
  }

  function handlePageChange(page) {
    setCurrentPage(page);
  }

  const handleEditRole = (row) => {
    if (permissions?.update === 0) {
      handleSetMessage("Update permission not allowed");
      handleOpenInfoModal();
    } else {
      if (
        row.original.isPredefined === true &&
        row.original.customerSupplier === true
      ) {
        handleSetMessage("Update permission not allowed for predefined role");
        handleOpenInfoModal();
      } else {
        navigate(`/app/rolemanagement/add?edit=true`, {
          state: row.original.id,
        });
      }
    }
  };

  const handleDeleteRole = (row) => {
    if (permissions?.delete === 0) {
      handleSetMessage("Delete permission not allowed");
      handleOpenInfoModal();
    } else {
      if (
        row.original.isPredefined === true &&
        row.original.customerSupplier === true
      ) {
        handleSetMessage("Delete permission not allowed for predefined role");
        handleOpenInfoModal();
      } else {
        setShowDeleteConfirmation(1);
        setRoleId(row.original.id);
      }
    }
  };

  // const formatDateTime = (dateTimeString) => {
  //   const date = new Date(dateTimeString);

  //   const options = {
  //     day: "2-digit",
  //     month: "short",
  //     year: "numeric",
  //     hour: "2-digit",
  //     minute: "2-digit",
  //     second: "2-digit",
  //   };

  //   const formattedDate = date
  //     .toLocaleDateString("en-GB", options)
  //     .replace(/\//g, " "); // Replace slashes with spaces

  //   const formattedTime = date
  //     .toLocaleTimeString("en-GB", options)
  //     .replace(",", ""); // Remove comma

  //   const formattedDateTime = formattedTime;

  //   return formattedDateTime;
  // };

  const columns = useMemo(
    () => [
      {
        header: "Role Name",
        accessorKey: "name",
        size: 120,
        Cell: ({ row }) => (
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <div>
              <CssTooltip title={row?.original?.name} placement="top" arrow>
                <p>
                  {row?.original?.name
                    ? row?.original?.name.length > 20
                      ? `${row?.original?.name.slice(0, 20)}...`
                      : row?.original?.name
                    : "-"}
                </p>
              </CssTooltip>
            </div>
          </div>
        ),
      },
      {
        header: "No of Users",
        accessorKey: "userCount",
        size: 100,
      },
      {
        header: "Creation Date",
        accessorKey: "createdAt",
        size: 120,
      },
      {
        header: "No of Dashboard",
        accessorKey: "dashboardCount",
        size: 100,
      },
      {
        header: "No of Panels",
        accessorKey: "panelCount",
        size: 100,
      },
      {
        header: "",
        accessorKey: "action",
        size: 140,

        enableSorting: false,
        enableColumnFilter: false,
        Cell: ({ row }) => (
          <>
            <div className="flex gap-8 mr-7">
              <EditButton
                label={"Edit"}
                icon={EditingIcon}
                onClick={() => {
                  handleEditRole(row);
                }}
              />
              <EditButton
                label={"Delete"}
                icon={TrashIcon}
                onClick={() => {
                  handleDeleteRole(row);
                }}
              />
            </div>
          </>
        ),
      },
    ],
    [Roles]
  );

  const handleLimitChange = (e) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
  };

  return (
    <div
      style={{
        background: "white",
        //  margin: "auto",
        overflow: "hidden",
      }}
    >
      {totalResults === 0 && searchStr === "" ? null : (
        <>
          <div className="mt-7 mb-3 mx-6">
            <Table
              columns={columns}
              data={Array.isArray(Roles) ? Roles : []}
              isLoading={isLoading}
              // autoResetPage={false}
            />
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              // justifyContent: "flex-start",
              marginTop: "20px",
              padding: "0px 24px 10px",
            }}
          >
            <div className="flex ">
              <div>
                {" "}
                <ResultPerPageComponent
                  countPerPage={resultPerPage}
                  limit={limitPerPage}
                  handleLimitChange={handleLimitChange}
                />
              </div>

              <div
                style={{
                  display: "flex",
                  fontSize: "14px",
                  padding: "10px 0px 0px 10px",
                  color: theme.textColor.titleColor,
                }}
              >
                {(currentPage - 1) * limitPerPage + 1} -{" "}
                {Math.min(limitPerPage * currentPage, totalResults)} of{" "}
                {totalResults} rows
              </div>
            </div>
            <div>
              <Pagination
                className="pagination-bar"
                currentPage={currentPage}
                totalCount={totalResults}
                pageSize={limitPerPage}
                onPageChange={(page) => {
                  handlePageChange(page);
                }}
              />{" "}
            </div>
          </div>
        </>
      )}
      <DeleteDialog
        show={showDeleteConfirmation !== 0 ? true : false}
        onHide={() => setShowDeleteConfirmation(0)}
        onConfirm={() => deleteUser(roleId)}
        title={"Are you sure you want to delete this item?"}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => setSuccessDialog(false)}
        message={message}
      />{" "}
      <InfoModal
        icon={<Alert className="w-10 h-10 " />}
        show={showAlertConfirmation}
        onHide={handleCloseInfoModal}
        message={message}
      />
      <ToastContainer position="top-center" autoClose={3000} />
    </div>
  );
}
export default RolesList;
