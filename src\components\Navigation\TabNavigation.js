import React, { useState, useEffect, useRef } from "react";

function TabNavigation({ sections }) {
  const [activeTab, setActiveTab] = useState(sections[0]?.id || null);
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);
  const scrollRef = useRef(null);

  const activeSection = sections.find((section) => section.id === activeTab);

  // Check if content is scrollable
  useEffect(() => {
    const checkScrollable = () => {
      if (scrollRef.current) {
        const { scrollHeight, clientHeight } = scrollRef.current;
        setShowScrollIndicator(scrollHeight > clientHeight);
      }
    };

    checkScrollable();
    // Recheck when active section changes
  }, [activeSection]);

  // Inject custom styles for better scrollbar visibility
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      .custom-scrollbar::-webkit-scrollbar {
        width: 8px;
      }
      .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 4px;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 4px;
        border: 1px solid #f1f5f9;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }
    `;
    document.head.appendChild(style);

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Tab Headers - Changed to vertical/column layout */}
        <div className="flex flex-col border-b border-gray-200 bg-gray-50">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveTab(section.id)}
              className={`w-full px-4 py-3 text-sm font-medium transition-colors duration-200 border-b border-gray-200 last:border-b-0 ${
                activeTab === section.id
                  ? "text-blue-600 bg-white border-l-4 border-l-blue-600"
                  : "text-gray-600 hover:text-gray-800 hover:bg-gray-100"
              }`}
            >
              <div className="flex items-center justify-between w-full">
                <span>{section.title}</span>
                <span
                  className={`text-xs px-2 py-1 rounded-full ${
                    activeTab === section.id
                      ? "bg-blue-100 text-blue-800"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  {section.count}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mt-4">
        {/* Tab Content */}
        <div className="p-4">
          {activeSection ? (
            <>
              <div className="text-blue-600 bg-white mb-2 text-sm">
                {" "}
                {`${activeSection.title} List`}
              </div>
              <div
                className="max-h-80 overflow-y-auto overflow-x-hidden pr-2 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-600"
                style={{
                  scrollbarWidth: "thin",
                  scrollbarColor: "#9ca3af #f3f4f6",
                }}
              >
                {activeSection.content}
              </div>
            </>
          ) : (
            <div className="text-center text-gray-500 py-8">
              Select a tab to view content
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default TabNavigation;
