import React, { useState } from "react";
import { ResponsivePie } from "@nivo/pie";
import { ExpandMore, ExpandLess } from "@mui/icons-material";

const colors = [
  "#A1C3FA",
  "#EDDF82",
  "#FFAB2D",
  "#FF5733",
  "#6C5CE7",
  "#48DBFB",
];

const MIN_SLICE_PERCENTAGE = 1;

const formatDataForPieChart = (respData) => {
  const totalValue = Object.values(respData).reduce(
    (sum, value) => sum + Number(value),
    0
  );

  let formatted = Object.entries(respData).map(([label, value], index) => {
    const numericValue = Number(value) || 0;
    const percentage = totalValue === 0 ? 0 : (numericValue / totalValue) * 100;

    return {
      id: label,
      label,
      value: numericValue,
      rawValue: numericValue,
      color: colors[index % colors.length],
      percentage,
    };
  });

  const adjustedTotal = formatted.reduce((sum, item) => {
    const adjusted =
      item.percentage < MIN_SLICE_PERCENTAGE
        ? MIN_SLICE_PERCENTAGE
        : item.percentage;
    return sum + adjusted;
  }, 0);

  return formatted.map((item) => {
    const displayPercentage =
      item.percentage < MIN_SLICE_PERCENTAGE
        ? MIN_SLICE_PERCENTAGE
        : item.percentage;
    const adjustedValue = (displayPercentage / adjustedTotal) * totalValue;

    return {
      ...item,
      value: adjustedValue,
      displayValue: item.rawValue,
    };
  });
};

const DoughnutChart = ({
  respData,
  panelData,
  reportData,
  isPanel,
  errMsg,
}) => {
  const [isLegendExpanded, setIsLegendExpanded] = useState(true);
  const visibleCount = 4;

  const formattedData = formatDataForPieChart(respData);
  const totalValue = formattedData.reduce((sum, item) => sum + item.value, 0);

  const displayedData = isLegendExpanded
    ? formattedData
    : formattedData.slice(0, visibleCount);

  const CustomLegend = () => {
    const hasMore = formattedData.length > visibleCount;
    const displayedData =
      isLegendExpanded || !hasMore
        ? formattedData
        : formattedData.slice(0, visibleCount);

    return (
      <div className="w-full bg-gray-50 rounded-lg">
        {hasMore && (
          <div className="flex items-center justify-end mb-2">
            <button
              onClick={() => setIsLegendExpanded((prev) => !prev)}
              className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <span className="text-sm">
                {isLegendExpanded
                  ? "Show Less"
                  : `Show All (${formattedData.length})`}
              </span>
              {isLegendExpanded ? (
                <ExpandLess sx={{ fontSize: 16 }} />
              ) : (
                <ExpandMore sx={{ fontSize: 16 }} />
              )}
            </button>
          </div>
        )}

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {displayedData.map((item) => (
            <div
              key={item.id}
              className="flex items-center space-x-2 p-1 rounded hover:bg-white transition-all"
            >
              <div
                className="w-4 h-4 rounded flex-shrink-0"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-xs text-gray-700">
                {`${item.label}: ${item.value.toLocaleString("en-US", {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 2,
                })} (${item.percentage.toFixed(2)}%)`}
              </span>
            </div>
          ))}
        </div>

        {!isLegendExpanded && hasMore && (
          <div className="mt-2 text-center text-sm text-gray-500">
            +{formattedData.length - visibleCount} more series
          </div>
        )}
      </div>
    );
  };

  if (formattedData?.length === 0 || totalValue === 0) {
    return (
      <div
        style={{
          width: "100%",
          height: 240,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          fontSize: 16,
          //  fontWeight: 600,
          // color: "#999999",
        }}
      >
        {errMsg ? errMsg : "No records to display"}
      </div>
    );
  }

  return (
    <div className="w-full">
      <div
        style={{
          position: "relative",
          height: panelData ? 450 : reportData ? 390 : 310,
          width: "100%",
          overflow: "hidden",
          transform: "scale(0.8)",
          transformOrigin: "top center",
        }}
      >
        <ResponsivePie
          data={formattedData}
          margin={{ top: 20, right: 110, bottom: 10, left: 70 }}
          innerRadius={0.5}
          padAngle={1}
          activeOuterRadiusOffset={8}
          borderWidth={1.5}
          borderColor={{ from: "color", modifiers: [["darker", 0.2]] }}
          colors={{ datum: "data.color" }}
          arcLinkLabelsSkipAngle={360}
          arcLabelsSkipAngle={360}
          enableArcLabels={false}
          legends={[]}
          animate={false}
          theme={{
            labels: {
              text: {
                fontSize: 10,
                fontWeight: "bold",
                color: "black",
              },
            },
          }}
          tooltip={({ datum }) => (
            <div
              style={{
                background: "black",
                color: "white",
                padding: "5px 10px",
                fontSize: "14px",
                borderRadius: "3px",
                display: "flex",
                alignItems: "center",
                fontWeight: "bold",
              }}
            >
              <div
                style={{
                  width: "12px",
                  height: "12px",
                  backgroundColor: datum.color,
                  marginRight: "8px",
                  borderRadius: "2px",
                }}
              />
              <div>
                <strong>{datum.id}</strong>:{" "}
                {datum.value.toLocaleString("en-US", {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 2,
                })}{" "}
                ({datum.data.percentage.toFixed(2)}%)
              </div>
            </div>
          )}
          layers={[
            "arcs",
            "arcLabels",
            ({ centerX, centerY }) => (
              <text
                x={centerX}
                y={centerY}
                textAnchor="middle"
                dominantBaseline="central"
                style={{
                  fontSize: "12px",
                  fontWeight: "bold",
                }}
              >
                {`Total: ${totalValue.toLocaleString("en-US", {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 2,
                })}`}
              </text>
            ),
          ]}
        />
      </div>
      {(panelData || isPanel) && <CustomLegend />}
    </div>
  );
};

export default DoughnutChart;
