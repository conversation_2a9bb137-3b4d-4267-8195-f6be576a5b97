import React, { useMemo, useState, useContext, useCallback } from "react";
import Table from "../components/table/ReportTable";
import { SearchIcon } from "../icons";
import Pagination from "../components/Pagination/Pagination";
import ErrorDialog from "../popups/ErrorDialog";
import { useQuery, useMutation } from "react-query";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { DataContext } from "../context/DataContext";
import Title from "../Title";
import bgImage from "../assets/img/Records.png";
import theme from "../tailwind-theme";
import { downloadSearchCDR, getSearchCDR } from "../services/cdrsearch-api";
import { DownloadContext } from "../context/DownloadContext";
import { AuthContext } from "../context/AuthContext";

function CDRSearchDownloads() {
  const [users, setUsers] = useState([]);
  const [message, setMessage] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [downloadingFiles, setDownloadingFiles] = useState(new Set());

  const { resultPerPage } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(10);

  const [searchInput, setSearchInput] = useState("");

  const { setIsDownloading } = useContext(DownloadContext);
  const { roles } = useContext(AuthContext);

  function handlePageChange(page) {
    setCurrentPage(page);
  }
  const handleLimitChange = (e) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
  };
  const permissions = useMemo(() => {
    return roles?.resources?.filter((res) => res.name === "CDR Search")[0]
      .permissions;
  }, [roles]);
  const {
    data: searchDataList,
    isLoading: downloadLoading,
    isFetching,
  } = useQuery(
    ["searchCDR", currentPage, limitPerPage, searchInput],
    getSearchCDR,
    {
      onSuccess: (data) => {
        let newData = data?.data?.data?.map((x) => {
          return {
            id: x.id,
            initiatedBy: x.initiatedBy,
            filePath: x.filePath.substring(x.filePath.lastIndexOf("/") + 1),
            createdAt: x.createdAt,
            status: x.status,
            category: x.category,
          };
        });
        setUsers(newData);
      },
    }
  );

  // Download mutation for handling file downloads
  const downloadMutation = useMutation(
    ({ id }) => downloadSearchCDR({ queryKey: ["searchCDRDownload", id] }),
    {
      onMutate: ({ id }) => {
        // Add file to downloading set
        setDownloadingFiles((prev) => new Set(prev).add(id));
        setIsDownloading(true);
      },
      onSuccess: (resp, { filePath }) => {
        const url = URL.createObjectURL(resp.data);
        const link = document.createElement("a");
        link.href = url;
        link.download = filePath;
        link.click();
      },
      onError: (error, { id }) => {
        const errorMessage =
          error.code === "ERR_NETWORK"
            ? "Something went wrong! Please check the connectivity!"
            : "Something went wrong! ";
        setMessage(errorMessage);
        setShowErrorDialog(true);
      },
      onSettled: (_, __, variables) => {
        const { id } = variables;
        // Remove file from downloading set
        setDownloadingFiles((prev) => {
          const newSet = new Set(prev);
          newSet.delete(id);
          // Only set global downloading to false if no other downloads are active
          if (newSet.size === 0) {
            setIsDownloading(false);
          }
          return newSet;
        });
      },
    }
  );

  const handleDownload = useCallback(
    (fileId, filePath) => {
      // Prevent multiple downloads of the same file
      if (downloadingFiles.has(fileId)) {
        return;
      }

      downloadMutation.mutate({ id: fileId, filePath });
    },
    [downloadingFiles, downloadMutation]
  );

  const columns = useMemo(
    () => [
      {
        header: "ID",
        accessorKey: "id",
        size: 100,
      },
      {
        header: "Initiated By",
        accessorKey: "initiatedBy",
        size: 80,
      },
      {
        header: "Date",
        accessorKey: "createdAt",
        size: 80,
      },
      {
        header: "File name",
        accessorKey: "fileName",
        size: 120,

        Cell: ({ row }) => {
          const isDownloadable =
            row.original.status === "Successfully downloaded";
          const isCurrentlyDownloading = downloadingFiles.has(row.original.id);

          return (
            <div
              className={`${
                isDownloadable && !isCurrentlyDownloading
                  ? "cursor-pointer text-blue-600"
                  : "cursor-not-allowed text-gray-600"
              } underline ${isCurrentlyDownloading ? "opacity-50" : ""}`}
              onClick={() => {
                if (isDownloadable && !isCurrentlyDownloading) {
                  // if (permissions?.download === 0) {
                  //   setMessage("Download permission not allowed");
                  //   setShowErrorDialog(true);
                  // } else {
                  handleDownload(row.original.id, row.original.filePath);
                  // }
                }
              }}
            >
              {isCurrentlyDownloading ? (
                <span className="flex items-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Downloading...
                </span>
              ) : (
                row.original.filePath.split(".")[0]
              )}
            </div>
          );
        },
      },

      {
        header: "Category",
        accessorKey: "category",
        size: 80,
      },
      {
        header: "Status",
        accessorKey: "status",
        size: 80,
      },
    ],
    [downloadingFiles, permissions?.download, handleDownload]
  );

  return (
    <>
      <div className="bg-bgPrimary my-5">
        <div className="mt-10">
          <Title title={"Offline Downloads"} />
        </div>
        <div className="border border-listBorder bg-white p-3 mt-10">
          <div className="mx-3 mt-3">
            <div className="mt-5">
              {!downloadLoading &&
              searchDataList?.data?.totalCount === 0 &&
              searchInput === "" ? (
                <>
                  <div className="border border-outerBorder mb-5">
                    <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                      {"Oops ! no records to display."}
                    </div>
                    {/* <div className="flex justify-center text-tabColor text-base font-semibold mt-5 ">{`You have not added any cards yet.`}</div> */}
                    <div className="flex justify-center my-12">
                      <img
                        src={bgImage}
                        style={{
                          height: "10%",
                          width: "10%",
                          objectFit: "cover",
                        }}
                        alt="bg"
                      />
                    </div>
                  </div>
                </>
              ) : (
                <>
                  {" "}
                  <div className="mx-3">
                    <div class="w-full flex mt-3">
                      <div className=" w-1/2 ">
                        <input
                          type="text"
                          style={{
                            border: `1px solid ${theme.borderColor.outerBorder}`,
                            paddingLeft: "2rem",
                          }}
                          className="w-full px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                          placeholder="Search"
                          value={searchInput}
                          onChange={(e) => {
                            setSearchInput(e.target.value);
                            setCurrentPage(1);
                          }}
                        />

                        <div
                          class="top-0 right-0  mr-4"
                          style={{
                            marginLeft: "0.25rem",
                            marginTop: "-1.7rem",
                          }}
                        >
                          <SearchIcon className="w-5 h-5" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-7 mx-3">
                    <Table
                      columns={columns}
                      data={Array.isArray(users) ? users : []}
                      isLoading={isFetching}
                    />
                  </div>
                  {searchDataList?.data &&
                    searchDataList?.data?.totalCount !== undefined && (
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          margin: "20px 0px 20px 20px",
                          width: "95%",
                        }}
                      >
                        <div className="flex ">
                          <div>
                            <ResultPerPageComponent
                              countPerPage={resultPerPage}
                              limit={limitPerPage}
                              handleLimitChange={handleLimitChange}
                            />
                          </div>

                          <div
                            style={{
                              display: "flex",
                              fontSize: "14px",
                              padding: "10px 0px 0px 10px",
                              color: theme.textColor.titleColor,
                            }}
                          >
                            {(currentPage - 1) * limitPerPage + 1} -{" "}
                            {Math.min(
                              limitPerPage * currentPage,
                              searchDataList?.data?.totalCount
                            )}{" "}
                            of {searchDataList?.data?.totalCount} rows
                          </div>
                        </div>
                        <Pagination
                          className="pagination-bar"
                          currentPage={currentPage}
                          totalCount={searchDataList?.data?.totalCount}
                          pageSize={limitPerPage}
                          onPageChange={(page) => {
                            handlePageChange(page);
                          }}
                        />
                      </div>
                    )}{" "}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      <ErrorDialog
        show={showErrorDialog}
        onHide={() => setShowErrorDialog(false)}
        message={message}
      />
    </>
  );
}

export default CDRSearchDownloads;
