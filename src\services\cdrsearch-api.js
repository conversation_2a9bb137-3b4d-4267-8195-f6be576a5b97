import axios from "axios";
import { config } from "../assets/config/config";
import getAPIMap from "../routes/ApiUrls";

const apiUrl = config.api.url;

export async function searchCDR(options) {
  let url = `${apiUrl}/v1/reports/raw-cdr`;
  let response;

  response = axios.post(url, options.reqObj);

  return response;
}

export async function downloadCDR(options) {
  let response;
  let url = `${apiUrl}/v1/reports/raw-cdr`;
  if (options.totalCount < options.maxCount) {
    response = axios.post(url, options.reqObj, {
      responseType: "blob",
      timeout: 0,
    });
  } else {
    response = axios.post(url, options.reqObj);
  }
  return response;
}
export async function downloadSearchCDR({ queryKey }) {
  let url = `${apiUrl}/v1/offline-download/download/${queryKey[1]}`;
  let response = await axios.get(url, { responseType: "blob" });
  return response;
}

export async function getSearchCDR(options) {
  let url = `${apiUrl}/v1/offline-download`;
  if (options.queryKey[1] && options.queryKey[1] !== "") {
    url = url + "?page=" + options.queryKey[1];
  }
  if (options.queryKey[2] && options.queryKey[2] !== "") {
    url = url + "&limit=" + options.queryKey[2];
  }
  if (options.queryKey[3] && options.queryKey[3] !== "") {
    url = url + "&search=" + encodeURIComponent(options.queryKey[3]);
  }

  let response;

  response = axios.get(url);

  return response;
}
export async function billingReportRefresh(options) {
  let url = getAPIMap("billingRefresh");
  let response;

  response = axios.post(url, options.reqObj);

  return response;
}
