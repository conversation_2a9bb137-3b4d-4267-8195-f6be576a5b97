export const countryCodeArr = [
  "+93",
  "+355",
  "+213",
  "+376",
  "+244",
  "+1-268",
  "+54",
  "+374",
  "+61",
  "+43",
  "+994",
  "+1-242",
  "+973",
  "+880",
  "+1-246",
  "+375",
  "+32",
  "+501",
  "+229",
  "+975",
  "+591",
  "+387",
  "+267",
  "+55",
  "+673",
  "+359",
  "+226",
  "+257",
  "+855",
  "+237",
  "+1",
  "+238",
  "+236",
  "+235",
  "+56",
  "+86",
  "+57",
  "+269",
  "+242",
  "+506",
  "+385",
  "+53",
  "+357",
  "+420",
  "+45",
  "+253",
  "+1-767",
  "+1-809",
  "+1-829",
  "+1-849",
  "+593",
  "+20",
  "+503",
  "+240",
  "+291",
  "+372",
  "+268",
  "+251",
  "+679",
  "+358",
  "+33",
  "+241",
  "+220",
  "+995",
  "+49",
  "+233",
  "+30",
  "+1-473",
  "+502",
  "+224",
  "+245",
  "+592",
  "+509",
  "+504",
  "+852",
  "+36",
  "+354",
  "+91",
  "+62",
  "+98",
  "+964",
  "+353",
  "+972",
  "+39",
  "+225",
  "+1-876",
  "+81",
  "+962",
  "+7",
  "+254",
  "+686",
  "+965",
  "+996",
  "+856",
  "+371",
  "+961",
  "+266",
  "+231",
  "+218",
  "+423",
  "+370",
  "+352",
  "+853",
  "+261",
  "+265",
  "+60",
  "+960",
  "+223",
  "+356",
  "+692",
  "+222",
  "+230",
  "+52",
  "+691",
  "+373",
  "+377",
  "+976",
  "+382",
  "+1-664",
  "+212",
  "+258",
  "+95",
  "+264",
  "+674",
  "+977",
  "+31",
  "+687",
  "+64",
  "+505",
  "+227",
  "+234",
  "+683",
  "+850",
  "+389",
  "+47",
  "+968",
  "+92",
  "+680",
  "+970",
  "+507",
  "+675",
  "+595",
  "+51",
  "+63",
  "+48",
  "+351",
  "+1-787",
  "+1-939",
  "+974",
  "+40",
  "+7",
  "+250",
  "+1-869",
  "+1-758",
  "+1-784",
  "+685",
  "+378",
  "+239",
  "+966",
  "+221",
  "+381",
  "+248",
  "+232",
  "+65",
  "+421",
  "+386",
  "+677",
  "+252",
  "+27",
  "+82",
  "+211",
  "+34",
  "+94",
  "+249",
  "+597",
  "+46",
  "+41",
  "+963",
  "+886",
  "+992",
  "+255",
  "+66",
  "+670",
  "+228",
  "+690",
  "+676",
  "+1-868",
  "+216",
  "+90",
  "+993",
];

export const dialCodeToCountryCodeMap = {
  "+93": "AF",
  "+355": "AL",
  "+213": "DZ",
  "+376": "AD",
  "+244": "AO",
  "+1-268": "AG",
  "+54": "AR",
  "+374": "AM",
  "+61": "AU",
  "+43": "AT",
  "+994": "AZ",
  "+1-242": "BS",
  "+973": "BH",
  "+880": "BD",
  "+1-246": "BB",
  "+375": "BY",
  "+32": "BE",
  "+501": "BZ",
  "+229": "BJ",
  "+975": "BT",
  "+591": "BO",
  "+387": "BA",
  "+267": "BW",
  "+55": "BR",
  "+673": "BN",
  "+359": "BG",
  "+226": "BF",
  "+257": "BI",
  "+855": "KH",
  "+237": "CM",
  "+1": "CA",
  "+238": "CV",
  "+236": "CF",
  "+235": "TD",
  "+56": "CL",
  "+86": "CN",
  "+57": "CO",
  "+269": "KM",
  "+242": "CG",
  "+506": "CR",
  "+385": "HR",
  "+53": "CU",
  "+357": "CY",
  "+420": "CZ",
  "+45": "DK",
  "+253": "DJ",
  "+1-767": "DM",
  "+1-809": "DO",
  "+1-829": "DO",
  "+1-849": "DO",
  "+593": "EC",
  "+20": "EG",
  "+503": "SV",
  "+240": "GQ",
  "+291": "ER",
  "+372": "EE",
  "+268": "SZ",
  "+251": "ET",
  "+679": "FJ",
  "+358": "FI",
  "+33": "FR",
  "+241": "GA",
  "+220": "GM",
  "+995": "GE",
  "+49": "DE",
  "+233": "GH",
  "+30": "GR",
  "+1-473": "GD",
  "+502": "GT",
  "+224": "GN",
  "+245": "GW",
  "+592": "GY",
  "+509": "HT",
  "+504": "HN",
  "+852": "HK",
  "+36": "HU",
  "+354": "IS",
  "+91": "IN",
  "+62": "ID",
  "+98": "IR",
  "+964": "IQ",
  "+353": "IE",
  "+972": "IL",
  "+39": "IT",
  "+225": "CI",
  "+1-876": "JM",
  "+81": "JP",
  "+962": "JO",
  "+7": "KZ",
  "+254": "KE",
  "+686": "KI",
  "+965": "KW",
  "+996": "KG",
  "+856": "LA",
  "+371": "LV",
  "+961": "LB",
  "+266": "LS",
  "+231": "LR",
  "+218": "LY",
  "+423": "LI",
  "+370": "LT",
  "+352": "LU",
  "+853": "MO",
  "+261": "MG",
  "+265": "MW",
  "+60": "MY",
  "+960": "MV",
  "+223": "ML",
  "+356": "MT",
  "+692": "MH",
  "+222": "MR",
  "+230": "MU",
  "+52": "MX",
  "+691": "FM",
  "+373": "MD",
  "+377": "MC",
  "+976": "MN",
  "+382": "ME",
  "+1-664": "MS",
  "+212": "MA",
  "+258": "MZ",
  "+95": "MM",
  "+264": "NA",
  "+674": "NR",
  "+977": "NP",
  "+31": "NL",
  "+687": "NC",
  "+64": "NZ",
  "+505": "NI",
  "+227": "NE",
  "+234": "NG",
  "+683": "NU",
  "+850": "KP",
  "+389": "MK",
  "+47": "NO",
  "+968": "OM",
  "+92": "PK",
  "+680": "PW",
  "+970": "PS",
  "+507": "PA",
  "+675": "PG",
  "+595": "PY",
  "+51": "PE",
  "+63": "PH",
  "+48": "PL",
  "+351": "PT",
  "+1-787": "PR",
  "+1-939": "PR",
  "+974": "QA",
  "+40": "RO",
  "+7": "RU",
  "+250": "RW",
  "+1-869": "KN",
  "+1-758": "LC", // Saint Lucia
  "+1-784": "VC", // Saint Vincent and the Grenadines
  "+685": "WS", // Samoa
  "+378": "SM", // San Marino
  "+239": "ST", // Sao Tome and Principe
  "+966": "SA", // Saudi Arabia
  "+221": "SN", // Senegal
  "+381": "RS", // Serbia
  "+248": "SC", // Seychelles
  "+232": "SL", // Sierra Leone
  "+65": "SG", // Singapore
  "+421": "SK", // Slovakia
  "+386": "SI", // Slovenia
  "+677": "SB", // Solomon Islands
  "+252": "SO", // Somalia
  "+27": "ZA", // South Africa
  "+82": "KR", // South Korea
  "+211": "SS", // South Sudan
  "+34": "ES", // Spain
  "+94": "LK", // Sri Lanka
  "+249": "SD", // Sudan
  "+597": "SR", // Suriname
  "+46": "SE", // Sweden
  "+41": "CH", // Switzerland
  "+963": "SY", // Syria
  "+886": "TW", // Taiwan
  "+992": "TJ", // Tajikistan
  "+255": "TZ", // Tanzania
  "+66": "TH", // Thailand
  "+670": "TL", // Timor-Leste
  "+228": "TG", // Togo
  "+690": "TK", // Tokelau
  "+676": "TO", // Tonga
  "+1-868": "TT", // Trinidad and Tobago
  "+216": "TN", // Tunisia
  "+90": "TR", // Turkey
  "+993": "TM", // Turkmenistan
};
