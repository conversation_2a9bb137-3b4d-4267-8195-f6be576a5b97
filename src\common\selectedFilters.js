import React from "react";

const SelectedFilters = ({
  values,
  fieldList,
  conditionList,
  logicalOperator,
}) => {
  return (
    <div className="mx-12 text-sm mb-4 flex md:w-11/12 md:max-w-full flex-wrap  ">
      <p className="my-1"> Selected Filter: </p>
      {values.conditions.map((condition, index) => {
        const labels = [
          condition.type1 &&
            fieldList.find((item) => item.value === condition.type1)?.label,
          condition.type2,
          condition.type3,
          condition.type4 &&
            logicalOperator.find((item) => item.value === condition.type4)
              ?.label,
        ];
        const hasLabels = labels.some((label) => label);
        return (
          <div className=" flex   p-1 " key={index}>
            <span
              className={`rounded-md p-1  text-xs ml-1 ${
                hasLabels ? "bg-bgTeritary" : "bg-white"
              }`}
            >
              {labels.join(" ")}
            </span>
          </div>
        );
      })}
    </div>
  );
};

export default SelectedFilters;
