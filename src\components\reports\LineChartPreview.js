import React, { useContext, useState } from "react";
import LineChartComponent from "../charts/LineChart";
import { PreviewContext } from "../../context/PreviewContext";
import PreviewLineChart from "../charts/PreviewLineChart";

export default function LineChartPreview({ isPreview }) {
  const colors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];
  const { panelFormStepThreeResponse } = useContext(PreviewContext);
  let content;
  if (panelFormStepThreeResponse.data.data.y_axis.length === 0) {
    content = (
      <>
        <div className=" w-full font-bold text-xl flex justify-center items-center p-3">
          No records to display
        </div>
      </>
    );
  } else {
    content = (
      <>
        {isPreview ? (
          <PreviewLineChart
            data={
              panelFormStepThreeResponse?.data?.data
                ? panelFormStepThreeResponse?.data?.data
                : []
            }
            colors={colors}
            isPreview={isPreview}
          />
        ) : (
          <LineChartComponent
            data={
              panelFormStepThreeResponse?.data?.data
                ? panelFormStepThreeResponse?.data?.data
                : []
            }
            colors={colors}
            isPreview={isPreview}
          />
        )}
      </>
    );
  }

  return <>{content}</>;
}
