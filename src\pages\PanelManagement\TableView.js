import React, { useState, useContext, useEffect } from "react";
import StepperHeader from "../../components/Stepper/StepperHeader";
import PanelForm from "../../Forms/PanelForm";
import PanelFormStepThree from "../../Forms/PanelFormStepThree";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import PanelFormStepFour from "../../Forms/PanelFormStepFour";
import TableFormTwo from "../../Forms/TableFormTwo";
import BarChartFormTwo from "../../Forms/BarChartFormTwo";
import { useLocation } from "react-router-dom";
import { getId } from "../../services/panels-api";
import { useQuery } from "react-query";
import PieChartFormTwo from "../../Forms/PieChartFormTwo";
import MultiAxisFormTwo from "../../Forms/MultiAxisFormTwo";
import LineChartFormTwo from "../../Forms/LineChartFormTwo";
import InputLabel from "../../components/FormsUI/InputLabel/InputLabel";
import { useNavigate } from "react-router-dom";
import { DataContext } from "../../context/DataContext";
import { AuthContext } from "../../context/AuthContext";
import { CloseIcon } from "../../icons";

function TableView() {
  const [editDetails, setEditDetails] = useState({});
  const { currentStep, formData, setFormData, stepCount } =
    useContext(multiStepFormContext);
  const { configApiData } = useContext(AuthContext);

  const location = useLocation();
  const value = location.state && location.state.id;
  const edit = location.state && location.state.isEdit;
  const name = location?.state?.name;
  const IsDashboardTrue = location?.state?.Dashboard;
  const navigate = useNavigate();

  const { data: editDetail } = useQuery(["details", value], getId, {
    enabled: edit === true,
    onSuccess: (resp) => {
      //console.log("resp", resp?.data);
      setEditDetails(resp?.data);
    },
  });
  //console.log("configApiData", configApiData);
  const displayStepForms = (currentStep) => {
    // eslint-disable-next-line default-case
    switch (currentStep) {
      case 0:
        return <PanelForm editDetail={editDetails} />;
      case 1:
        return formData.type === "Table Report" ? (
          <TableFormTwo
            editDetail={editDetails}
            configApiData={configApiData}
          />
        ) : formData.type === "Bar Graph" ? (
          <BarChartFormTwo
            editDetail={editDetails}
            configApiData={configApiData}
          />
        ) : formData.type === "Pie Chart" ? (
          <PieChartFormTwo
            editDetail={editDetails}
            configApiData={configApiData}
          />
        ) : formData.type === "Line Graph" ? (
          <LineChartFormTwo
            editDetail={editDetails}
            configApiData={configApiData}
          />
        ) : formData.type === "MultiAxis Graph" ? (
          <MultiAxisFormTwo
            editDetail={editDetails}
            isEdit={edit}
            configApiData={configApiData}
          />
        ) : (
          <TableFormTwo editDetail={editDetails} />
        );
      case 2:
        return <PanelFormStepThree editDetail={editDetails} isEdit={edit} />;
      case 3:
        return <PanelFormStepFour value={value} isEdit={edit} />;

      default:
        return "";
    }
  };
  const getCompletionPercentage = () => {
    //console.log("currentSteptv", stepCount);
    switch (stepCount) {
      case 0:
        return "0% completed";
      case 1:
        return "25% completed";
      case 2:
        return "50% completed";
      case 3:
        return "75% completed";
      case 4:
        return "100% completed";
      default:
        return "";
    }
  };
  return (
    <>
      <div className="bg-bgPrimary my-4">
        <div className="text-headingColor text-2xl font-bold font-['Open Sans Hebrew'] leading-tight mb-4">
          {edit ? "Update Panel" : "Add Panel"}
        </div>
        <div className="border border-listBorder bg-white pb-5">
          <div className="flex flex-row mx-3 my-3">
            <div>
              <InputLabel
                label={"Enter Details"}
                labelClassName="font-semibold"
              />
            </div>
            <div className="flex-grow flex justify-end items-center">
              <CloseIcon
                onClick={() => {
                  if (IsDashboardTrue && name) {
                    navigate("/app/dashboard/details/create", {
                      state: { name: name },
                    });
                  } else if (IsDashboardTrue && value) {
                    navigate(`/app/dashboard/details/edit/${value}`, {
                      state: { isEdit: true },
                    });
                  } else {
                    navigate("/app/panelmanagement");
                  }
                }}
                className="w-2.5 h-2.5 cursor-pointer"
              />
            </div>
          </div>
          <div className="mt-3 mx-3 mb-3 border-b-2 border-listBorder" />
          <div className="my-10">
            <StepperHeader atStep={currentStep} module={"panel"} />
          </div>
          <div className="my-14 border bg-bgTeritary ">
            <div className="flex justify-between items-center text-base font-bold my-3">
              <div className="flex-1 text-center">
                <div>Step {currentStep + 1}</div>
              </div>
              <div className="mr-8">{getCompletionPercentage()}</div>
            </div>
          </div>

          <div className="mt-[45px]">{displayStepForms(currentStep)}</div>
        </div>
      </div>
    </>
  );
}

export default TableView;
