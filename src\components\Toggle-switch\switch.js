import React, { useState } from "react";
import Switch from "react-switch";

const CustomSwitch = ({ checked, onChange, width, diameter }) => {
  const handleSwitchChange = (checked, event) => {
    if (onChange) {
      onChange(checked, event);
    }
  };
  return (
    <label>
      <Switch
        onChange={handleSwitchChange}
        checked={checked}
        onColor="#e8f8e4" // Set the fill color when the switch is on
        onHandleColor="#83b776" // Set the handle color when the switch is on
        offColor="#9C9898"
        uncheckedIcon
        checkedIcon
        handleDiameter={diameter ? diameter : 10} // Set the diameter of the handle (width and height)
        height={15}
        width={width ? width : 28}
        boxShadow="0px 1px 5px rgba(0, 0, 0, 0.2)"
        activeBoxShadow="0 0 2px 3px #356B37"
      />
    </label>
  );
};

export default CustomSwitch;
