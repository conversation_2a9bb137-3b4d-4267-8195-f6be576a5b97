import dayjs from "dayjs";

/**
 * Formats date range based on selected range or custom dates
 * @param {string|Object} selectedRange - The selected time range or custom date object
 * @param {Object} selectedFilter - Custom filter object with startDate and endDate
 * @returns {Object} - Object containing formattedStart and formattedEnd dates
 */
export const getFormattedDateRange = (selectedRange, selectedFilter = null) => {
  let formattedStart = "";
  let formattedEnd = "";
  const currentDateTime = dayjs();

  // Check if we have custom date range from selectedFilter
  if (selectedFilter?.startDate && selectedFilter?.endDate) {
    return {
      formattedStart: selectedFilter.startDate,
      formattedEnd: selectedFilter.endDate,
    };
  }

  if (!selectedRange) {
    return { formattedStart, formattedEnd };
  }

  // Handle custom date range (contains "to")
  if (selectedRange.includes("to")) {
    const [startString, endString] = selectedRange.split("to");
    formattedStart = startString.trim();
    formattedEnd = endString.trim();
    return { formattedStart, formattedEnd };
  }

  // Handle predefined ranges
  const timeRangeHandlers = {
    "Last Hour": () => {
      const lastHour = currentDateTime.subtract(1, "hour");
      return {
        formattedStart: lastHour.format("YYYY-MM-DD HH:mm:ss"),
        formattedEnd: currentDateTime.format("YYYY-MM-DD HH:mm:ss"),
      };
    },
    "Last 6 Hours": () => {
      const lastXHours = currentDateTime.subtract(6, "hour");
      return {
        formattedStart: lastXHours.format("YYYY-MM-DD HH:00:00"),
        formattedEnd: currentDateTime.format("YYYY-MM-DD HH:00:00"),
      };
    },
    "Last 12 Hours": () => {
      const lastXHours = currentDateTime.subtract(12, "hour");
      return {
        formattedStart: lastXHours.format("YYYY-MM-DD HH:00:00"),
        formattedEnd: currentDateTime.format("YYYY-MM-DD HH:00:00"),
      };
    },
    "Last 24 Hours": () => {
      const lastXHours = currentDateTime.subtract(24, "hour");
      return {
        formattedStart: lastXHours.format("YYYY-MM-DD HH:00:00"),
        formattedEnd: currentDateTime.format("YYYY-MM-DD HH:00:00"),
      };
    },
    Today: () => ({
      formattedStart: currentDateTime
        .startOf("day")
        .format("YYYY-MM-DD HH:mm:ss"),
      formattedEnd: currentDateTime.format("YYYY-MM-DD HH:mm:ss"),
    }),
    Yesterday: () => {
      const yesterday = currentDateTime.subtract(1, "day");
      return {
        formattedStart: yesterday.startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        formattedEnd: yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
      };
    },
    "Last Seven Days": () => ({
      formattedStart: currentDateTime
        .subtract(6, "days")
        .startOf("day")
        .format("YYYY-MM-DD HH:00:00"),
      formattedEnd: currentDateTime.format("YYYY-MM-DD HH:59:59"),
    }),
    "Last Week": () => ({
      formattedStart: currentDateTime
        .subtract(1, "week")
        .startOf("week")
        .format("YYYY-MM-DD HH:00:00"),
      formattedEnd: currentDateTime
        .subtract(1, "week")
        .endOf("week")
        .format("YYYY-MM-DD HH:59:59"),
    }),
    "Last 30 Days": () => ({
      formattedStart: currentDateTime
        .subtract(29, "days")
        .startOf("day")
        .format("YYYY-MM-DD HH:00:00"),
      formattedEnd: currentDateTime.endOf("day").format("YYYY-MM-DD HH:59:59"),
    }),
    "Last Month": () => ({
      formattedStart: currentDateTime
        .subtract(1, "month")
        .startOf("month")
        .format("YYYY-MM-DD HH:00:00"),
      formattedEnd: currentDateTime
        .subtract(1, "month")
        .endOf("month")
        .format("YYYY-MM-DD HH:59:59"),
    }),
    "This Month": () => ({
      formattedStart: currentDateTime
        .startOf("month")
        .format("YYYY-MM-DD HH:mm:ss"),
      formattedEnd: currentDateTime.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
    }),
  };

  const handler = timeRangeHandlers[selectedRange];
  if (handler) {
    return handler();
  }

  return { formattedStart, formattedEnd };
};

/**
 * Generates filename for report downloads
 * @param {string} reportName - Name of the report
 * @param {string} startDate - Start date
 * @param {string} endDate - End date
 * @param {string} extension - File extension
 * @returns {string} - Generated filename
 */
export const generateReportFilename = (reportName, startDate, endDate) => {
  const reportNameFormatted = reportName.replace(/\s+/g, "_");
  const fromDate = dayjs(startDate).format("YYYY-MM-DD");
  const toDate = dayjs(endDate).format("YYYY-MM-DD");
  const downloadTime = dayjs().format("HH_mm_ss");
  return `${reportNameFormatted}_${fromDate}_${toDate}_${downloadTime}`;
};

/**
 * Maps duration strings to interval values for API
 * @param {string} duration - Duration string (Daily, Weekly, Monthly)
 * @returns {string|undefined} - Mapped interval value
 */
export const mapDurationToInterval = (duration) => {
  const durationMap = {
    Daily: "day",
    Weekly: "week",
    Monthly: "month",
  };
  return durationMap[duration];
};

/**
 * Returns initial filter and range for reports based on report type
 * @param {string} data - Report type identifier
 * @param {Object} dynamicReports - Dynamic reports constants
 * @returns {{filter: {startDate: string, endDate: string}, range: string}}
 */
export const getInitialFilterAndRange = (data, dynamicReports) => {
  const isSupplierOrCustomer =
    data === dynamicReports.Supplier ||
    data === dynamicReports.Customer ||
    data === dynamicReports.SupplierMultiple ||
    data === dynamicReports.CustomerMultiple;
  const isSlabBasedBilling =
    data === dynamicReports.SlabBasedCust ||
    data === dynamicReports.SlabBasedSupp ||
    data === dynamicReports.SlabBasedCustMultiple ||
    data === dynamicReports.SlabBasedSuppMultiple;

  const startDate = isSlabBasedBilling
    ? dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss")
    : isSupplierOrCustomer
    ? dayjs().subtract(6, "days").startOf("day").format("YYYY-MM-DD HH:00:00")
    : dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
  const endDate = dayjs().format("YYYY-MM-DD HH:mm:ss");

  let selectedRange = "Today";
  if (isSupplierOrCustomer) {
    selectedRange = "Last Seven Days";
  } else if (isSlabBasedBilling) {
    selectedRange = "This Month";
  }

  return {
    filter: { startDate, endDate },
    range: selectedRange,
  };
};
