import React, { forwardRef } from "react";
import { Width<PERSON><PERSON>ider, Responsive } from "react-grid-layout";
import Card<PERSON>ata from "./CardData";
const ResponsiveGridLayout = WidthProvider(Responsive);

const ResponsiveLayoutCard = forwardRef(
  ({ cardDroppedData, removeCard, isCreate, isEdit, isApplied }, ref) => {
    return (
      <>
        <div className=" " ref={ref}>
          <ResponsiveGridLayout
            className="layout"
            cols={{ lg: 6, md: 6, sm: 6, xs: 6, xxs: 6 }}
            rowHeight={45}
            isDraggable={false}
            isResizable={false}
          >
            {/* Cards */}
            {cardDroppedData.map((card, index) => {
              const x = (index % 4) * 1.5;
              const y = Math.floor(index / 4) * 2;
              return (
                <div
                  key={index + "card"}
                  data-grid={{
                    x: x,
                    y: y,
                    w: 1.5,
                    h: 2,
                  }}
                >
                  <CardData
                    key={index}
                    card={card}
                    onClose={() => removeCard(index)}
                    isCreate={isCreate}
                    isEdit={isEdit}
                    isApplied={isApplied}
                  />
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>
      </>
    );
  }
);

export default ResponsiveLayoutCard;
