//import React from "react";
import { ErrorMessage, useField, useFormikContext } from "formik";
import ReactSelect, { components, createFilter } from "react-select";
import { DataContext } from "../../../context/DataContext";
import { useContext, useState, useEffect } from "react";
import theme from "../../../tailwind-theme";

const Select = ({
  fontSize,
  minHeight,
  isDisabled,
  setFormData,
  visTypeDropdown,
  menuListzIndex,
  //  setVisTypeChange,
  //  visTypeChange,
  ...props
}) => {
  const { setVisTypeChange, visTypeChange } = useContext(DataContext);
  const [field, meta, helpers] = useField(props.name);
  const { setFieldValue, setFieldTouched, handleBlur } = useFormikContext();
  const [optionsData, setOptionsData] = useState([]);
  // console.log(`isDisabled: ${props.isDisabled}`);
  const customSelectStyles = {
    control: (baseStyles, state) => ({
      ...baseStyles,
      fontWeight: "400",
      fontSize: fontSize || "12px",
      minHeight: "40px",
      borderColor: isDisabled ? "#D9D9D9" : baseStyles.borderColor,
      pointerEvents: isDisabled ? "none" : baseStyles.pointerEvent,
      borderWidth: "1px",
      border:
        meta.error && meta.touched ? "1px solid #DC3833" : "1px solid #C8C0C0",
      borderRadius: "5px",

      // paddingTop: "7px",
      // paddingBottom: "7px",
      //borderRadius: template === templates.recruiter ? "0.3rem" : "0.75rem",
      "&:hover": {
        border: "1px solid #C8C0C0",
      },

      // This line disable the blue border
      boxShadow: "none",
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: menuListzIndex ? menuListzIndex : "auto",
    }),
    option: (provided, { data, isDisabled, isFocused, isSelected }) => ({
      ...provided,
      color: "#2D2D2D",
      cursor: isDisabled ? "not-allowed" : "pointer",
      background: isSelected ? "#7070701C" : "white",
      ":hover": {
        background: "#7070701C",
      },
    }),
    placeholder: (provided) => ({
      ...provided,
      fontSize: "12px",
      fontWeight: "500",
      color: "#7070708C",
    }),
    menu: (styles) => ({
      ...styles,
      backgroundColor: "white",
      fontSize: fontSize || "12px",
      position: "absolute",
      //The below width line is commented because it makes entire for scrollable
      //width: "max-content",
      minWidth: "100%",
      // border: "0 !important",
      border: "1px solid #70707059",
      boxShadow: "0 !important",
      "&:hover": {
        border: "1px solid #70707059",
      },
    }),
    // multiValueRemove: (base) => ({
    //   ...base,
    //   // backgroundColor: theme.backgroundColor.bgTeritary,
    //   color: "black",
    //   //borderTopRightRadius: "5px",
    //   //borderBottomRightRadius: "5px",
    //   // height: "30px",

    //   "&:hover": {
    //     backgroundColor: theme.backgroundColor.bgTeritary,
    //     color: "black",
    //   },
    // }),
    multiValueLabel: (data) => ({
      ...data,
      height: "25px",
      padding: "2px 3px",
      fontSize: "12px",
      marginTop: "1.5px",
      color: "black !important",
    }),
    multiValue: (data) => ({
      ...data,
      height: "25px",
      padding: "0px 6px",
      backgroundColor: theme.backgroundColor.bgTeritary,
      borderRadius: "5px",
      fontSize: "12px",
    }),
    input: (data) => ({
      ...data,

      "input:focus": {
        boxShadow: "none",
      },
      boxShadow: "0 !important",
      "&:hover": {
        border: "0 !important",
      },
    }),
  };

  const removeDuplicates = (array) => {
    const seen = new Set();
    return array.filter((item) => {
      const key = `${item.value}-${item.label}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  };

  useEffect(() => {
    if (props?.options) {
      setOptionsData(removeDuplicates(props.options));
    }
  }, [props.options]);

  return (
    <div className="relative">
      <ReactSelect
        aria-label={props.name}
        className={`input ${props.className} ${
          meta.touched && meta.error && "is-danger"
        }`}
        autoComplete="off"
        options={optionsData}
        menuPortalTarget={menuListzIndex ? document.body : null}
        //  menuPortalTarget={document.body}
        isSearchable={props.isSearchable || false}
        styles={customSelectStyles}
        isMulti={props.isMulti}
        type="text"
        filterOption={createFilter({ ignoreAccents: false })}
        isClearable={props.isClearable}
        maxMenuHeight={props.maxMenuHeight}
        name={props.name}
        isOptionDisabled={(option) => option.isDisabled}
        // isOptionDisabled={() =>
        //   props.isOptionDisabled
        //     ? field.value.length >= props.isOptionDisabled
        //     : null
        // }
        isDisabled={props.disabled}
        isLoading={props.isLoading}
        formatOptionLabel={props.formatOptionLabel}
        value={
          !props.isMulti
            ? props.options
              ? props.options.find((option) => {
                  // Handle explicit comparison for 0 and other falsy values
                  return (
                    option.value === field.value ||
                    (props.value !== undefined && option.value === props.value)
                  );
                }) || ""
              : ""
            : props.options.filter(
                (option) => field.value && field.value.includes(option.value)
              )
        }
        onChange={(option) => {
          // setFormData([]);
          //option?.queryKey
          if (visTypeDropdown) {
            //console.log("option", visTypeDropdown);
            setVisTypeChange(true);
            // visTypeDropdown = false;
          } else {
            // console.log("option", visTypeDropdown, visTypeChange);
            setVisTypeChange(false);
          }
          setFieldTouched(props.name, true);
          if (props.onChange) props.onChange(option);
          if (!props.isMulti) {
            helpers.setValue(option);
            setFieldValue(props.name, option.value);
          } else {
            let options = option.map((option) => option.value);
            setFieldValue(props.name, options);
          }
        }}
        onBlur={() => setFieldTouched(props.name, true)}
        placeholder={props.placeholder}
      ></ReactSelect>
      <ErrorMessage
        component="div"
        className={` text-[#d32f2f] text-[0.75rem] mt-0 font-normal`}
        name={field.name}
      />
    </div>
  );
};

export default Select;
